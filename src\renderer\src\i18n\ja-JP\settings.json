{"title": "設定", "common": {"title": "一般設定", "modelSettings": "モデル設定", "searchSettings": "検索設定", "networkSettings": "ネットワーク設定", "interfaceSettings": "インターフェースと操作", "personalizationSettings": "パーソナライゼーション設定", "securitySettings": "セキュリティとプライバシー", "operationSettings": "操作設定", "resetData": "データをリセット", "language": "言語", "languageSelect": "言語を選択", "searchEngine": "検索エンジン", "searchEngineDesc": "ウェブ検索強化に使用する検索エンジンを選択。Google、Bingなどの主要検索エンジンやカスタム検索エンジンをサポート", "searchAssistantModel": "アシスタントモデル", "searchAssistantModelDesc": "検索結果を処理して回答を生成するモデルを選択。検索強化の品質と速度に影響します", "searchPreview": "検索プレビュー", "searchPreviewDesc": "検索結果ページでプレビューサムネイルを表示し、検索コンテンツを素早く閲覧できます", "proxyMode": "プロキシモード", "proxyModeDesc": "ネットワークプロキシ設定を構成。海外サービスへのアクセスにシステムプロキシ、プロキシなし、またはカスタムプロキシを選択", "searchEngineSelect": "検索エンジンを選択", "selectModel": "モデルを選択", "visionModel": "ビジョンモデル", "visionModelDesc": "画像や視觚コンテンツを処理するためのモデルを選択し、画像説明、分析などの機能に使用されます", "visionModelNotSelectedWarning": "ビジョンモデルが選択されていません。画像機能を使用する際に設定を行うようプロンプトが表示されます", "proxyModeSelect": "プロキシモードを選択", "proxyModeSystem": "システムプロキシ", "proxyModeNone": "プロキシなし", "proxyModeCustom": "カスタムプロキシ", "customProxyUrl": "カスタムプロキシURL", "customProxyUrlPlaceholder": "例: http://127.0.0.1:7890", "invalidProxyUrl": "無効なプロキシURL、有効なhttp/https URLを入力してください", "contentProtection": "画面キャプチャ保護", "contentProtectionDialogTitle": "画面保護の切り替え確認", "contentProtectionEnableDesc": "DeepChatウィンドウの画面共有によるキャプチャを防止し、プライバシー保護に役立ちます。すべてのアプリがこの設定に対応するわけではなく、環境によっては黒いウィンドウが残る場合があります。", "contentProtectionDisableDesc": "画面共有アプリによるDeepChatウィンドウのキャプチャを許可します。", "contentProtectionRestartNotice": "この設定を変更するとアプリケーションが再起動します。続行しますか？", "soundEnabled": "サウンドを有効にする", "copyWithCotEnabled": "COT情報をコピー", "loggingEnabled": "ログを有効にする", "devToolsAutoOpen": "開発者ツールを自動で開く", "devToolsAutoOpenDesc": "ウィンドウやタブを作成する際に開発者ツールを自動で開きます", "loggingDialogTitle": "ログ設定の変更確認", "loggingEnableDesc": "ログを有効にすると、問題の診断とアプリケーションの改善に役立ちます。ログファイルには機密情報が含まれる可能性があります。", "loggingDisableDesc": "ログを無効にすると、アプリケーションログの収集が停止します。", "loggingRestartNotice": "この設定を変更するとアプリケーションが再起動します。続行しますか？", "devToolsDialogTitle": "開発者ツール設定の変更確認", "devToolsEnableDesc": "開発者ツールの自動オープンを有効にすると、新しいウィンドウやタブを作成した際に自動で開発者ツールが開きます。", "devToolsDisableDesc": "開発者ツールの自動オープンを無効にすると、開発者ツールが自動で開かなくなります。", "devToolsRestartNotice": "この設定は即座に有効になり、次回ウィンドウを作成した際に適用されます。", "addCustomSearchEngine": "カスタム検索エンジンを追加", "addCustomSearchEngineDesc": "新しい検索エンジンを追加するには、名前と検索URLを提供する必要があります。URLには{'{'}query{'}'}をクエリプレースホルダーとして含める必要があります。", "searchEngineName": "検索エンジン名", "searchEngineNamePlaceholder": "検索エンジン名を入力してください", "searchEngineUrl": "検索URL", "searchEngineUrlPlaceholder": "例: https://a.com/search?q={'{'}query{'}'}", "searchEngineUrlError": "URLには{'{'}query{'}'}をクエリプレースホルダーとして含める必要があります", "deleteCustomSearchEngine": "カスタム検索エンジンを削除", "deleteCustomSearchEngineDesc": "カスタム検索エンジン \"{name}\" を削除してもよろしいですか？この操作は元に戻せません。", "testSearchEngine": "検索エンジンをテスト", "testSearchEngineDesc": "{engine} 検索エンジンを使用して「天気」のテスト検索を実行します。", "testSearchEngineNote": "検索ページにログインやその他の操作が必要な場合は、テストウィンドウで実行できます。テスト完了後はテストウィンドウを閉じてください。", "theme": "テーマ", "themeSelect": "テーマを選択", "closeToQuit": "閉じるボタンでアプリを終了", "openLogFolder": "ログフォルダを開く", "shortcut": {"newChat": "新しいチャットを作成", "title": "ショートカットキー設定"}, "notifications": "システム通知", "notificationsDesc": "DeepChatがバックグラウンドのとき、応答が完了すると通知を送信します"}, "data": {"title": "データ設定", "syncEnable": "データ同期を有効にする", "syncFolder": "同期フォルダ", "openSyncFolder": "同期フォルダを開く", "lastSyncTime": "最終同期時間", "never": "未同期", "startBackup": "今すぐバックアップ", "backingUp": "バックアップ中...", "importData": "データをインポート", "incrementImport": "増分インポート", "overwriteImport": "上書きインポート", "importConfirmTitle": "データインポートの確認", "importConfirmDescription": "インポートすると、チャット履歴や設定を含むすべての現在のデータが上書きされます。重要なデータをバックアップしたことを確認してください。インポート後はアプリケーションを再起動する必要があります。", "importing": "インポート中...", "confirmImport": "インポートを確認", "importSuccessTitle": "インポート成功", "importErrorTitle": "インポート失敗", "resetData": "データをリセット", "resetConfirmTitle": "データリセットの確認", "resetConfirmDescription": "リセットするデータの種類を選択してください。この操作は元に戻せません。リセット後、アプリケーションは自動的に再起動されます。", "resetChatData": "チャットデータをリセット", "resetChatDataDesc": "すべてのチャット履歴と会話記録を削除", "resetKnowledgeData": "ナレッジベースデータをリセット", "resetKnowledgeDataDesc": "すべてのナレッジベースファイルとベクターデータを削除", "resetConfig": "設定をリセット", "resetConfigDesc": "すべてのアプリ設定、モデル設定、カスタムプロンプトを削除", "resetAll": "完全リセット", "resetAllDesc": "チャット履歴、設定、キャッシュファイルを含むすべてのデータを削除", "resetting": "リセット中...", "confirmReset": "リセットを確認", "resetCompleteDevTitle": "データリセット完了", "resetCompleteDevMessage": "開発環境では手動でアプリケーションを再起動してください。現在のプロセスを停止して pnpm run dev を再実行してください"}, "model": {"title": "モデル設定", "systemPrompt": {"label": "システムプロンプト", "placeholder": "システムプロンプトを入力してください...", "description": "AIアシスタントのシステムプロンプトを設定し、その行動と役割を定義します"}, "temperature": {"label": "モデルの温度", "description": "出力のランダム性を制御します。高い値はより創造的な応答を生成します"}, "contextLength": {"label": "コンテキストの長さ", "description": "会話のコンテキストの最大長を設定します"}, "responseLength": {"label": "応答の長さ", "description": "AIの応答の最大長を設定します"}, "artifacts": {"description": "Artifacts機能を有効にすると、AIはより豊かなコンテンツを生成できます", "title": "Artifacts"}, "addModel": "モデルを追加", "configureModel": "モデルを構成", "modelList": "モデルリスト", "provider": "サービスプロバイダー", "providerSetting": "サービスプロバイダーの設定", "selectModel": "モデルを選択します", "modelConfig": {"cancel": "キャンセル", "contextLength": {"description": "モデルが処理できるコンテキスト長を設定します", "label": "コンテキスト長"}, "description": "この構成は現在のモデルに対してのみ有効であり、他のモデルには影響しないことに注意してください。注意して変更してください。パラメーターが誤っていると、モデルが適切に機能しない場合があります。", "functionCall": {"description": "モデルが機能コールをネイティブにサポートするかどうか（このオプションをオフにした後、DeepChatは機能コールを自動的にシミュレートします）", "label": "関数呼び出し"}, "maxTokens": {"description": "モデルの単一出力に最大数のトークンを設定します", "label": "最大出力長"}, "reasoning": {"description": "モデルは推論能力をサポートしていますか？", "label": "推論能力"}, "thinkingBudget": {"label": "思考予算", "description": "モデルの思考長を制限する", "dynamic": "動的思考", "range": "範囲: {min} - {max}", "onlySupported": "Gemini 2.5 Flash、2.5 Pro、2.5 Flash-Liteでのみサポート", "valueLabel": "思考予算値", "placeholder": "思考予算値を入力", "forceEnabled": "Gemini 2.5 シリーズモデルは思考予算を強制的に有効にします", "dynamicPrefix": "-1 = 動的思考", "notice": "注意：", "warnings": {"proNoDisable": "このモデルは思考の無効化をサポートしていません、最小値 128", "proCannotDisable": "Gemini 2.5 Pro は思考機能を無効にできません", "flashLiteCannotSetZero": "Gemini 2.5 Flash-Lite は 0 に設定できません、最小値は 512", "modelCannotDisable": "このモデルは思考機能を無効にできません", "flashLiteMinValue": "思考予算を具体的な値に設定する場合、512 未満にはできません（または 0 で思考を無効、-1 で動的思考を有効）", "belowMin": "思考予算は {min} 未満にはできません{hint}", "aboveMax": "思考予算は {max} を超えることはできません"}, "hints": {"flashLiteDisable": "、0 = 思考を無効、具体的な値の最小値 512", "normalDisable": "、0 = 思考を無効", "withZeroAndDynamic": "（または 0 で思考を無効、-1 で動的思考を有効）", "withDynamic": "（または -1 で動的思考を有効）"}}, "resetConfirm": {"confirm": "リセットを確認します", "message": "このモデルの構成をデフォルトにリセットする必要がありますか？この操作は取消不能です。", "title": "リセットを確認します"}, "reasoningEffort": {"label": "推論努力度", "description": "モデルの推論の深さを制御します。より高い努力度はより良い結果を生成しますが、応答が遅くなります", "placeholder": "推論努力度を選択", "options": {"minimal": "Minimal - 最速応答", "low": "Low - 低努力", "medium": "Medium - 中程度の努力", "high": "High - 高努力"}}, "verbosity": {"label": "詳細度", "description": "モデル応答の詳細レベルと長さを制御します", "placeholder": "詳細度を選択", "options": {"low": "Low - 簡潔な回答", "medium": "Medium - バランスの取れた詳細", "high": "High - 詳細な回答"}}, "resetToDefault": "デフォルトにリセットします", "saveConfig": "構成を保存します", "useModelDefault": "モデルのデフォルト設定を使用", "currentUsingModelDefault": "現在モデルのデフォルト設定を使用中", "temperature": {"description": "出力のランダム性を制御します。値が高いほどランダム性が増します。", "label": "温度"}, "title": "カスタムモデルパラメーター", "type": {"description": "モデルのタイプを選択します", "label": "モデルタイプ", "options": {"chat": "言語モデル", "embedding": "埋め込みモデル", "imageGeneration": "画像生成モデル", "rerank": "リランクモデル"}}, "validation": {"contextLengthMax": "コンテキストの長さは10000000を超えることはできません", "contextLengthMin": "コンテキストの長さは0より大きくなければなりません", "contextLengthRequired": "コンテキストの長さを空にすることはできません", "maxTokensMax": "最大出力長は1000000を超えることはできません", "maxTokensMin": "最大出力長は0より大きくなければなりません", "maxTokensRequired": "最大出力長を空にすることはできません", "temperatureMax": "温度は2以下でなければなりません", "temperatureMin": "温度は0以上でなければなりません", "temperatureRequired": "温度を空にすることはできません"}, "vision": {"description": "モデルは視覚能力をサポートしていますか？", "label": "視覚能力"}}}, "provider": {"search": "プロバイダープラットフォームを検索…", "enable": "サービスを有効にする", "enabled": "有効", "disabled": "無効", "urlPlaceholder": "API URLを入力してください", "keyPlaceholder": "API Keyを入力してください", "accessKeyIdPlaceholder": "AWS Access Key IDを入力してください", "secretAccessKeyPlaceholder": "AWS Secret Access Keyを入力してください", "regionPlaceholder": "AWSリージョンを入力してください", "verifyKey": "キーを検証", "howToGet": "取得方法", "getKeyTip": "以下へアクセスしてください", "getKeyTipEnd": "API Keyを取得する", "urlFormat": "API例：{defaultUrl}", "modelList": "モデル一覧", "enableModels": "モデルを有効にする", "disableAllModels": "すべてのモデルを無効にする", "modelsEnabled": "モデルが有効になりました", "noModelsEnabled": {"title": "有効なモデルがありません", "description": "「モデルを有効にする」ボタンをクリックして使用したいモデルを手動で選択してください。"}, "verifyLink": "検証リンク", "syncModelsFailed": "モデルの同期に失敗しました...", "addCustomProvider": "カスタムプロバイダーを追加", "delete": "削除", "stopModel": "モデルを停止", "pulling": "取得中...", "runModel": "モデルを実行", "dialog": {"disableModel": {"title": "モデルを無効にする確認", "content": "モデル \"{name}\" を無効にしてもよろしいですか？", "confirm": "無効にする"}, "configModels": {"title": "モデルリストを構成", "description": "有効または無効にするモデルを選択"}, "disableAllModels": {"title": "すべてのモデルを無効にする確認", "content": "モデル \"{name}\" のすべてのモデルを無効にしてもよろしいですか？", "confirm": "すべて無効にする"}, "verify": {"missingFields": "API KeyとAPI URLを入力してください", "failed": "検証に失敗しました", "success": "検証に成功しました", "failedDesc": "APIキーまたは設定の検証に失敗しました。設定を確認してください", "successDesc": "APIキーと設定の検証に成功しました。使用可能です", "connectionError": "接続エラー、ネットワーク接続とAPIアドレスを確認してください", "serverError": "サーバーエラー、後でもう一度やり直してください", "unauthorized": "認証に失敗し、APIキーが無効または期限切れです"}, "addCustomProvider": {"title": "カスタムプロバイダーを追加", "description": "プロバイダーの必要な情報を入力してください", "name": "名前", "namePlaceholder": "プロバイダー名を入力してください", "apiType": "APIタイプ", "apiTypePlaceholder": "APIタイプを選択してください", "apiKey": "APIキー", "apiKeyPlaceholder": "APIキーを入力してください", "baseUrl": "API URL", "baseUrlPlaceholder": "API URLを入力してください", "enable": "プロバイダーを有効にする"}, "deleteProvider": {"title": "プロバイダーの削除確認", "content": "プロバイダー \"{name}\" を削除してもよろしいですか？この操作は元に戻せません。", "confirm": "削除"}, "deleteModel": {"title": "モデルの削除確認", "content": "モデル \"{name}\" を削除してもよろしいですか？この操作は元に戻せません。", "confirm": "削除"}, "pullModel": {"title": "モデルを取得", "description": "ローカルにダウンロードするモデルを選択", "pull": "取得"}, "modelCheck": {"checking": "テスト...", "description": "接続性とユーザビリティテストのモデルを選択します", "failed": "モデルテストに失敗しました", "model": "モデルを選択します", "modelPlaceholder": "テストするモデルを選択してください", "noModels": "このサービスプロバイダーが利用できるモデルはありません", "success": "モデルテストが成功しました", "test": "テストを開始します", "title": "モデルチェック"}}, "pullModels": "モデルを取得", "refreshModels": "モデルを更新", "modelsRunning": "実行中のモデル", "runningModels": "実行中のモデル", "noRunningModels": "実行中のモデルはありません", "deleteModel": "モデルを削除", "deleteModelConfirm": "モデル \"{name}\" を削除してもよろしいですか？この操作は元に戻せません。", "noLocalModels": "ローカルモデルはありません", "localModels": "ローカルモデル", "azureApiVersion": "APIバージョン", "safety": {"title": "セキュリティ設定", "blockHighest": "高リスクをブロックします", "blockMost": "中程度のリスクをブロックします", "blockNone": "ブロックされていません", "blockSome": "低リスクをブロックします"}, "serverList": "サーバーリスト", "totalServers": "サーバー総数", "addServer": "サーバーを追加", "autoStart": "セルフスタート", "githubCopilotAuth": "GitHub Copilot認証", "githubCopilotConnected": "GitHub Copilot接続済み", "githubCopilotNotConnected": "GitHub Copilot未接続", "loginWithGitHub": "GitHubでログイン", "loggingIn": "ログイン中...", "githubCopilotLoginTip": "DeepChatがGitHub Copilotサブスクリプションにアクセスすることを許可します。Copilot APIにアクセスするには'read:user'と'read:org'の権限が必要です。", "loginSuccess": "ログイン成功", "loginFailed": "ログイン失敗", "tokenValid": "トークンは有効です", "tokenInvalid": "トークンは無効です", "disconnect": "切断", "disconnected": "切断成功", "disconnectFailed": "切断失敗", "keyStatus": {"remaining": "残りの金額", "usage": "使用済み"}, "refreshingModels": "更新中...", "toast": {"modelRunning": "モデルが実行されています", "modelRunningDesc": "最初にモデル{model}を停止してから削除してください。"}, "anthropicApiKeyTip": "APIキーを取得するには、人類コンソールにアクセスしてください", "anthropicConnected": "人類接続", "anthropicNotConnected": "人類は接続されていません", "anthropicOAuthTip": "[deepchatの承認]をクリックして、人類アカウントにアクセスします", "oauthLogin": "oauthログイン", "authMethod": "認証方法", "authMethodPlaceholder": "認証方法を選択", "apiKeyLabel": "API Key", "apiUrlLabel": "API URL", "anthropicOAuthFlowTip": "システムは自動的に承認ウィンドウを開きます。承認後、戻って承認コードを入力してください。", "anthropicBrowserOpened": "外部ブラウザが開いています", "anthropicCodeInstruction": "外部ブラウザで承認を完了し、取得した認証コードを下の入力ボックスに貼り付けてください", "browserOpenedSuccess": "外部ブラウザが開いています。承認を完了してください", "codeRequired": "承認コードを入力してください", "inputOAuthCode": "承認コードを入力します", "codeExchangeFailed": "認証コード交換は失敗しました", "invalidCode": "無効な承認コード", "oauthCodeHint": "外部ブラウザで承認を完了した後、ここに承認コードを貼り付けてください", "oauthCodePlaceholder": "承認コードを入力してください...", "verifyConnection": "接続を確認", "manageModels": "モデル管理", "anthropicOAuthActiveTip": "OAuth認証が有効になっており、Anthropicサービスを直接使用できます", "oauthVerifySuccess": "OAuth接続の確認が成功しました", "oauthVerifyFailed": "OAuth接続の確認に失敗しました", "configurationSaved": "構成が保存されました", "configurationUpdated": "設定が更新されました", "dataRefreshed": "データは更新されています", "modelscope": {"apiKey": "APIキー", "apiKeyHelper": "ModelScopeコンソールでAPIキーを取得します", "apiKeyPlaceholder": "ModelScope APIキーを入力してください", "baseUrl": "APIアドレス", "baseUrlHelper": "ModelScope APIサービスアドレス", "connected": "接続", "connecting": "接続...", "description": "ModelScopeは、Alibaba Damo Academyによって開始されたサービスとしてのモデル共有プラットフォームです", "details": {"apiConfig": "API構成", "mcpSync": "MCP同期", "modelManagement": "モデル管理", "operationalDescription": "ModelScopeプラットフォームで直接使用できるMCPサーバーを同期する", "operationalServers": "サーバーの操作", "rateLimitConfig": "レート制限構成", "safetySettings": "セキュリティ設定", "specialConfig": "特別な構成", "syncFromModelScope": "ModelScopeから同期します", "title": "プロバイダーの設定の詳細"}, "invalidKey": "無効なAPIキー", "keyRequired": "APIキーを入力してください", "mcpSync": {"authenticationFailed": "認証に失敗しました。APIキーを確認してください", "convertingServers": "サーバー構成の変換...", "description": "MODESCOPEからローカル構成までMCPサーバーを同期すると、一般的に使用されるMCPツールをすばやく追加できます。すべてのサービスはデフォルトで無効になり、インポート後に手動で有効にすることができます。", "errorDetails": "エラーの詳細", "errors": "エラー{count}", "fetchingServers": "MCPサーバーのリストを取得...", "imported": "{count}サービスがインポートされています", "importingServers": "サーバー構成のインポート...", "invalidServerData": "無効なサーバーデータ", "noApiKey": "ModelScope APIキーを最初に構成してください", "noOperationalUrls": "利用可能な操作アドレスは見つかりません", "noServersFound": "利用可能なMCPサービスは見つかりません", "pageNumber": "ページ番号", "pageNumberPlaceholder": "ページ番号を入力してください", "pageSize": "ページごとの数量", "serverAlreadyExists": "サーバーはすでに存在し、インポートをスキップします", "skipped": "Skip {count}サービス", "sync": "同期を開始します", "syncComplete": "同期完了", "syncing": "同期...", "title": "MCPサービスを同期します"}, "name": "ModelScope", "networkError": "ネットワーク接続エラー", "notConnected": "接続されていません", "verifyFailed": "検証に失敗しました", "verifySuccess": "検証は成功しました"}, "operationFailed": "操作に失敗しました", "operationSuccess": "操作は成功しました", "settingsApplied": "適用された設定", "bedrockLimitTip": "* Anthropic Claude（Opus、Sonnet、Haikuモデルを含む）のみをサポート", "bedrockVerifyTip": "DeepChatは検証にClaude 3.5 Sonnetを使用しています。起動権限がない場合、検証は失敗します。ただし、他のモデルの使用には影響しません。"}, "knowledgeBase": {"title": "ナレッジベース設定", "addKnowledgeBase": "ナレッジベースを追加", "selectKnowledgeBaseType": "追加するナレッジベースの種類を選択してください", "difyDescription": "Difyナレッジベースはドキュメントデータの管理と利用をサポートします", "comingSoon": "近日公開", "featureNotAvailable": "この機能はまだ利用できません", "addDifyConfig": "Dify設定を追加", "apiKey": "APIキー", "datasetId": "データセットID", "endpoint": "APIエンドポイント", "configAdded": "設定が追加されました", "configAddedDesc": "{name}構成が正常に追加されました", "addConfig": "設定を追加", "moreComingSoon": "より多くのナレッジベースタイプが近日公開予定", "configUpdated": "設定が更新されました", "configUpdatedDesc": "{name}構成は正常に更新されました", "descriptionPlaceholder": "例：会社の製品ドキュメントナレッジベース", "ragflowTitle": "RAGFlowナレッジベース", "ragflowDescription": "RAGFlowは、複数の検索方法とドキュメント管理機能をサポートする強力なナレッジベース管理システムです。", "addRagflowConfig": "RAGFlow設定を追加", "editRagflowConfig": "RAGFlow設定を編集", "dify": "dify知識ベース", "editDifyConfig": "Dify構成を変更します", "fastgptTitle": "FastGPT知識ベース", "fastgptDescription": "FastGPTは、複数の検索方法とドキュメント管理機能をサポートする強力な知識ベース管理システムです。", "addFastGptConfig": "FastGPT設定を追加", "editFastGptConfig": "FastGPT設定を編集", "builtInKnowledgeDescription": "組み込みのナレッジベースは、オフライン環境でいくつかの基本機能を可能にするいくつかの簡単な実装を提供します。", "builtInKnowledgeTitle": "組み込みの知識ベース", "addBuiltinKnowledgeConfig": "組み込みのナレッジベースの構成を追加します", "editBuiltinKnowledgeConfig": "組み込みのナレッジベースの構成を編集します", "chunkSize": "ブロックサイズ", "chunkSizeHelper": "ドキュメントをセグメントにカットすると、各セグメントのサイズがモデルコンテキスト制限を超えることはできません", "chunkOverlap": "重複するサイズ", "chunkOverlapHelper": "隣接するテキストブロック間で繰り返されるコンテンツの量により、セグメント化されたテキストブロック間にコンテキスト接続がまだあることが保証され、長いテキストのモデル処理の全体的な効果が改善されます。", "selectEmbeddingModel": "埋め込みモデルを選択します", "modelNotFound": "サービスプロバイダー {provider} またはモデル {model} が見つかりません", "modelNotFoundDesc": "モデルが正しく構成されており、モデルが有効になっていることを確認してください。サービスプロバイダーの設定でモデル構成を確認できます。", "removeBuiltinKnowledgeConfirmDesc": "組み込みのナレッジベース構成を削除すると、関連するすべてのデータが削除され、復元できません。注意してください。", "removeBuiltinKnowledgeConfirmTitle": "組み込みのナレッジベース{name}を削除することを確認しますか？", "descriptionDesc": "AIがこの知識ベースを取得するかどうかを決定するように、知識ベースの説明", "advanced": "高度なオプション", "autoDetectDimensions": "埋め込み寸法を自動的に検出します", "autoDetectHelper": "埋め込み寸法を自動的に検出し、少量のトークンを消費します", "chunkOverlapPlaceholder": "デフォルト値、変更は推奨されません", "chunkSizePlaceholder": "デフォルト値、変更は推奨されません", "dimensions": "埋め込まれた寸法", "dimensionsPlaceholder": "1024などの寸法サイズを埋め込みます", "selectEmbeddingModelHelper": "埋め込みモデルは、知識ベースの作成後に禁止されています", "dimensionsHelper": "モデルが設定された埋め込み寸法サイズをサポートしていることを確認してください", "autoDetectDimensionsError": "埋め込まれた寸法障害を自動的に検出します", "fragmentsNumber": "要求されたドキュメントフラグメントの数", "fragmentsNumberHelper": "要求されたドキュメントの断片が多いほど、より多くの情報が付属していますが、より多くのトークンを消費する必要があります", "selectRerankModel": "再注文モデルを選択します", "rerankModel": "モデルを並べ替えます", "embeddingModel": "埋め込みモデル", "return": "戻る", "uploadHelper": "クリックしてここでファイルをアップロードまたはドラッグします", "fileSupport": "{accept} およびその他の {count} 形式をサポートします", "searchKnowledge": "ナレッジベースを検索します", "searchKnowledgePlaceholder": "クエリコンテンツを入力してください", "noData": "まだデータはありません", "file": "書類", "uploadProcessing": "アップロード", "uploadCompleted": "完了したアップロード", "reAdd": "再アップロード", "uploadError": "アップロードに失敗しました", "delete": "消去", "reason": "理由", "deleteSuccess": "正常に削除します", "copy": "コピー", "copySuccess": "正常にコピーします", "source": "ソース", "normalized": "L2正規化", "normalizedHelper": "モデルが出力ベクトルのL2正規化をサポートしていることを確認してください", "dialog": {"beforequit": {"cancel": "キャンセル", "confirm": "確認する", "title": "確認確認", "description": "実行中のナレッジベースタスクがあります。ソフトウェアを終了しますか？ソフトウェアを再起動した後、中止されたタスクを復元できます。"}}, "searchError": "クエリに失敗しました", "processing": "アップロード", "paused": "一時停止をアップロードします", "unknown": "不明なステータス", "reAddFile": {"title": "再アップロード確認", "content": "ファイル「{fileName}」を再アップロードしてもよろしいですか？"}, "deleteFile": {"title": "ファイル削除の確認", "content": "ファイル「{fileName}」を削除してもよろしいですか？この操作は元に戻せません。"}, "resumeAllPausedTasks": "ワンクリックリカバリ", "pauseAllRunningTasks": "ワンクリックの一時停止", "separators": "ブロックセパレーター", "separatorsHelper": "ドキュメントのセグメンテーション区切り文字。単一の区切り文字は二重引用符 (\"\") で囲まれ、区切り文字はコンマ (,) で区切られます", "invalidSeparators": "無効な分離器", "selectLanguage": "プリセットを選択します", "separatorsPreset": "プリセットの読み込み", "promptManagement": {"title": "プロンプト管理", "description": "カスタムプロンプトテンプレートを管理・使用して、AIとのより良いインタラクションを実現"}}, "mcp": {"title": "MCP設定", "description": "MCP（Model Context Protocol）サーバーとツールを管理および構成します", "enabledTitle": "MCPを有効化", "enabledDescription": "MCP機能とツールを有効または無効にします", "enableToAccess": "設定オプションにアクセスするにはMCPを有効にしてください", "tabs": {"servers": "サーバー", "tools": "ツール", "prompts": "迅速な言葉", "resources": "リソース"}, "serverList": "サーバーリスト", "totalServers": "サーバー総数", "addServer": "サーバーを追加", "running": "実行中", "stopped": "停止中", "stopServer": "停止サーバー", "startServer": "起動サーバー", "noServersFound": "サーバーは見つかりません", "addServerDialog": {"title": "サーバーを追加", "description": "新しいMCPサーバーを構成します"}, "editServerDialog": {"title": "サーバーを編集", "description": "MCPサーバーの設定を編集します"}, "serverForm": {"name": "サーバー名", "namePlaceholder": "サーバー名を入力", "nameRequired": "サーバー名は必須です", "type": "サーバータイプ", "typePlaceholder": "サーバータイプを選択", "typeStdio": "標準入力と出力（STDIO）", "typeSse": "サーバー送信イベント（SSE）", "baseUrl": "ベースURL", "baseUrlPlaceholder": "サーバーのベースURLを入力（例：http://localhost:3000）", "command": "コマンド", "commandPlaceholder": "コマンドを入力", "commandRequired": "命令は空にできません", "args": "パラメータ", "argsPlaceholder": "パラメータを入力してください", "argsRequired": "パラメータは空にできません", "env": "環境変数", "envPlaceholder": "JSON形式の環境変数を入力してください", "envInvalid": "環境変数は有効なJSON形式である必要があります", "description": "説明", "descriptionPlaceholder": "サーバーの説明を入力してください", "descriptions": "説明", "descriptionsPlaceholder": "サーバーの説明を入力してください", "icon": "アイコン", "iconPlaceholder": "アイコンを入力してください", "icons": "アイコン", "iconsPlaceholder": "アイコンを入力してください", "autoApprove": "自動承認", "autoApproveAll": "全部", "autoApproveRead": "読み取り", "autoApproveWrite": "書き込み", "autoApproveHelp": "自動承認する操作のタイプを選択してください。ユーザーの確認なしで実行できます", "submit": "提出", "add": "追加", "update": "更新", "cancel": "キャンセル", "jsonConfigIntro": "JSON設定を直接貼り付けるか、手動でサーバーを設定するかを選択できます。", "jsonConfig": "JSON設定", "jsonConfigPlaceholder": "MCPサーバーのJSON形式の設定を貼り付けてください", "jsonConfigExample": "JSON設定例", "parseSuccess": "設定の解析が成功しました", "configImported": "設定が正常にインポートされました", "parseError": "解析エラー", "skipToManual": "手動設定へスキップ", "parseAndContinue": "解析して続行", "jsonParseError": "JSONの解析は失敗しました", "typeHttp": "ストリーミングHTTPリクエスト（HTTP）", "typeInMemory": "メモリ", "browseMarketplace": "MCPサービス市場を閲覧します", "imageModel": "視覚モデルを選択します", "customHeadersParseError": "カスタムヘッダーの解析は失敗しました", "customHeaders": "カスタムリクエストヘッダー", "clickToEdit": "クリックして編集し、完全な内容を表示", "invalidKeyValueFormat": "誤ったリクエストヘッダー形式、入力が正しいかどうかを確認してください。", "npmRegistry": "カスタムNPMレジストリ", "npmRegistryPlaceholder": "カスタムNPMレジストリをセットアップし、システムを残して最速のレジストリを自動的に選択する", "browseHigress": "Higress MCP Marketplaceを表示します", "selectFolderError": "フォルダーの選択エラー", "folders": "アクセスが許可されています", "addFolder": "フォルダーを追加します", "noFoldersSelected": "フォルダーは選択されていません", "useE2B": "E2Bサンドボックスを有効にします", "e2bDescription": "E2Bサンドボックスを使用してPythonコードを実行します", "e2bApiKey": "E2B Apikey", "e2bApiKeyPlaceholder": "e2b_1111xx *******など、E2B APIキーをこちらから入力してください。", "e2bApiKeyHelp": "e2b.devにアクセスして、アピケイを取得します", "e2bApiKeyRequired": "E2B関数を有効にするには、Apikeyを入力する必要があります"}, "deleteServer": "サーバーを削除", "editServer": "サーバーを編集", "setDefault": "デフォルトに設定", "isDefault": "デフォルトのサーバー", "default": "デフォルト", "setAsDefault": "デフォルトに設定", "removeServer": "サーバーを削除", "autoStart": "自動起動", "confirmRemoveServer": "サーバー {name} を削除してもよろしいですか？この操作は元に戻せません。", "removeServerDialog": {"title": "サーバーを削除"}, "confirmDelete": {"title": "削除の確認", "description": "サーバー {name} を削除してもよろしいですか？この操作は取り消せません。", "confirm": "削除", "cancel": "キャンセル"}, "resetToDefault": "デフォルトに戻す", "resetConfirmTitle": "デフォルトサーバーに戻す", "resetConfirmDescription": "この操作は、カスタムサーバーを保持したまま、すべてのデフォルトサーバーを復元します。デフォルトサーバーへの変更はすべて失われます。", "resetConfirm": "リセット", "builtIn": "組み込み", "builtInServerCannotBeRemoved": "ビルトインサービスは削除できず、パラメーターの変更のみを変更し、環境変数がサポートされています。", "builtInServers": "組み込みサービス", "cannotRemoveBuiltIn": "ビルトインサービスを削除できません", "customServers": "カスタムサービス", "removeDefault": "デフォルトを削除します", "marketplace": "MCPマーケットに移動し、ワンクリックでインストールします", "maxDefaultServersReached": "せいぜい3つのデフォルトサーバーのみを設定できます", "removeDefaultFirst": "最初にいくつかのデフォルトサーバーを削除してください", "higressMarket": "Higress MCPのインストールに移動します", "npmRegistry": {"title": "NPMソース設定", "currentSource": "現在のソース", "cached": "キャッシュ", "lastChecked": "最終チェック", "refresh": "更新", "advanced": "詳細設定", "advancedSettings": "詳細設定", "advancedSettingsDesc": "自動検出やカスタムソース設定を含むNPMソースの詳細オプションを設定", "autoDetect": "最適なソースを自動検出", "autoDetectDesc": "起動時に自動的に最速のNPMソースを検出して使用", "customSource": "カスタムソース", "customSourcePlaceholder": "カスタムNPMソースアドレスを入力", "currentCustom": "現在のカスタムソース", "justNow": "たった今", "minutesAgo": "{minutes}分前", "hoursAgo": "{hours}時間前", "daysAgo": "{days}日前", "refreshSuccess": "NPMソースの更新が成功しました", "refreshSuccessDesc": "最適なNPMソースを再検出して更新しました", "refreshFailed": "NPMソースの更新に失敗しました", "autoDetectUpdated": "自動検出設定が更新されました", "autoDetectEnabled": "最適なNPMソースの自動検出が有効になりました", "autoDetectDisabled": "自動検出が無効になり、デフォルトソースを使用します", "updateFailed": "設定の更新に失敗しました", "customSourceSet": "カスタムソースが設定されました", "customSourceSetDesc": "カスタムNPMソースが設定されました：{registry}", "customSourceCleared": "カスタムソースがクリアされました", "customSourceClearedDesc": "カスタムNPMソースをクリアし、自動検出を使用します", "invalidUrl": "無効なURL", "invalidUrlDesc": "有効なHTTPまたはHTTPSアドレスを入力してください", "testing": "NPMソースをテスト中", "testingDesc": "ソース {registry} の接続性をテスト中...", "testFailed": "NPMソーステストに失敗しました", "testFailedDesc": "{registry} に接続できません。エラー：{error}。URLが正しいかネットワーク接続を確認してください。", "redetectingOptimal": "最適なNPMソースを再検出中...", "redetectComplete": "再検出完了", "redetectCompleteDesc": "現在最適なNPMソースを検出して設定しました", "redetectFailed": "再検出に失敗しました", "redetectFailedDesc": "最適なソースを再検出できません。デフォルト設定を使用します"}, "technicalDetails": "技術的詳細", "httpServer": "HTTPサーバー", "localProcess": "ローカルプロセス", "restartServer": "サーバーを再起動", "viewLogs": "ログを表示", "starting": "起動中", "error": "エラー"}, "about": {"title": "私たちについて", "version": "バージョン", "checkUpdate": "更新を確認", "checking": "確認中...", "latestVersion": "最新バージョン"}, "display": {"fontSize": "テキストサイズ", "text-2xl": "非常に大きい", "text-base": "デフォルト", "text-lg": "大きい", "text-sm": "小さい", "text-xl": "非常に大きい", "floatingButton": "フローティングボタン", "floatingButtonDesc": "デスクトップにフローティングボタンを表示し、アプリケーションウィンドウを素早く起動できます"}, "shortcuts": {"title": "ショートカットキー設定", "pressKeys": "キーを押してください", "pressEnterToSave": "Enterキーで保存、Escでキャンセル", "noModifierOnly": "修飾キーのみをショートカットキーとして使用することはできません", "keyConflict": "ショートカットキーが競合しています。別の組み合わせを選択してください", "clearShortcut": "ショートカットをクリア", "cleanHistory": "明確なチャット履歴", "deleteConversation": "会話を削除", "goSettings": "設定を開く", "hideWindow": "ウィンドウを非表示にします", "quitApp": "プログラムを終了します", "zoomIn": "フォントにズームインします", "zoomOut": "フォントを削減します", "zoomReset": "フォントをリセットします", "closeTab": "現在のタブページを閉じます", "newTab": "新しいタブを作成します", "newWindow": "新しいウィンドウを開きます", "showHideWindow": "ウィンドウを表示または非表示にします", "newConversation": "新しい会話", "lastTab": "最後のタブに切り替えます", "nextTab": "次のタブに切り替えます", "previousTab": "前のタブに切り替えます", "specificTab": "指定されたタブページ（1-8）に切り替える"}, "rateLimit": {"title": "レート制限", "description": "リクエストの時間間隔を制御し、API制限の超過を防ぐ", "intervalLimit": "リクエスト間隔", "intervalUnit": "秒", "intervalHelper": "2つのリクエスト間の最小間隔、不要な場合はレート制限を無効にしてください", "lastRequestTime": "最後のリクエスト", "queueLength": "キューの長さ", "nextAllowedTime": "次の許可されたリクエスト", "never": "なし", "justNow": "たった今", "secondsAgo": "秒前", "minutesAgo": "分前", "immediately": "すぐに", "secondsLater": "秒後", "confirmDisableTitle": "レート制限の無効化を確認", "confirmDisableMessage": "値は0以下にできません。レート制限機能を無効にしますか？", "confirmDisable": "制限を無効化", "disabled": "レート制限が無効化されました", "disabledDescription": "レート制限機能が無効化されました"}}