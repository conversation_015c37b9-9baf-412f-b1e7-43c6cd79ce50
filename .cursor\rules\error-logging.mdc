---
description:
globs:
alwaysApply: false
---
# 错误处理和日志指南

## 错误处理规范

1. 错误类型
- 用户错误：用户输入或操作导致的错误
- 系统错误：程序运行时的错误
- 网络错误：API 调用或网络请求错误
- 业务错误：业务逻辑相关的错误

2. 错误处理原则
- 始终使用 try-catch 处理可能的错误
- 提供有意义的错误信息
- 记录详细的错误日志
- 优雅降级处理

3. 错误处理示例：
```typescript
try {
  await someOperation()
} catch (error) {
  if (error instanceof UserError) {
    // 处理用户错误
    showUserFriendlyMessage(error.message)
  } else if (error instanceof NetworkError) {
    // 处理网络错误
    handleNetworkError(error)
  } else {
    // 处理未知错误
    logError(error)
    showGenericErrorMessage()
  }
}
```

## 日志规范

1. 日志级别
- ERROR：错误信息
- WARN：警告信息
- INFO：一般信息
- DEBUG：调试信息

2. 日志内容
- 时间戳
- 日志级别
- 错误代码
- 错误描述
- 堆栈跟踪（如果适用）
- 相关上下文信息

3. 日志记录示例：
```typescript
import { logger } from '@/utils/logger'

// 错误日志
logger.error('Failed to save data', {
  error: error,
  context: { userId, operation }
})

// 信息日志
logger.info('User action completed', {
  action: 'save',
  userId,
  timestamp: new Date()
})
```

## 最佳实践

1. 错误处理
- 不要吞掉错误
- 提供用户友好的错误信息
- 实现错误重试机制
- 使用错误边界捕获渲染错误

2. 日志记录
- 避免记录敏感信息
- 使用结构化日志
- 实现日志轮转
- 设置适当的日志级别

3. 监控和告警
- 设置错误监控
- 配置关键错误告警
- 定期检查错误日志
- 分析错误模式
