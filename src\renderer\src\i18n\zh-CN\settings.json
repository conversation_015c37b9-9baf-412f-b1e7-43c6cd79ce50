{"title": "设置", "common": {"title": "通用设置", "modelSettings": "模型设置", "searchSettings": "搜索设置", "networkSettings": "网络设置", "interfaceSettings": "界面与交互", "personalizationSettings": "个性化设置", "securitySettings": "安全与隐私", "operationSettings": "操作设置", "resetData": "重置数据", "language": "语言", "languageDesc": "选择应用界面语言，支持中文、英文、日文等多种语言，修改后立即生效", "languageSelect": "选择语言", "searchEngine": "搜索引擎", "searchEngineDesc": "选择搜索引擎用于网络搜索增强，支持谷歌、百度等主流搜索引擎，也可添加自定义搜索引擎", "searchEngineSelect": "选择搜索引擎", "searchPreview": "搜索预览", "searchPreviewDesc": "开启后在搜索结果页面显示预览图，可快速浏览搜索结果内容", "searchAssistantModel": "助手模型", "searchAssistantModelDesc": "选择用于处理搜索结果并生成回答的模型，影响搜索增强的质量和速度", "visionModel": "视觉模型", "visionModelDesc": "选择用于处理图像和视觉内容的模型，用于图像描述、分析等功能", "visionModelNotSelectedWarning": "未选择视觉模型，使用图像功能时将提示您先进行设定", "selectModel": "选择模型", "proxyMode": "代理模式", "proxyModeDesc": "设置网络代理模式，可选择系统代理、不使用代理或自定义代理，用于访问国外服务", "proxyModeSelect": "选择代理模式", "proxyModeSystem": "系统代理", "proxyModeNone": "不使用代理", "proxyModeCustom": "自定义代理", "customProxyUrl": "自定义代理地址", "customProxyUrlPlaceholder": "例如: http://127.0.0.1:7890", "invalidProxyUrl": "无效的代理地址，请输入有效的 http/https URL", "addCustomSearchEngine": "添加自定义搜索引擎", "addCustomSearchEngineDesc": "添加一个新的搜索引擎，需要提供名称和搜索URL。URL中必须包含{query}作为查询占位符。", "searchEngineName": "搜索引擎名称", "searchEngineNamePlaceholder": "请输入搜索引擎名称", "searchEngineUrl": "搜索URL", "searchEngineUrlPlaceholder": "如: https://a.com/search?q={'{'}query{'}'}", "searchEngineUrlError": "URL必须包含{'{'}query{'}'}作为查询占位符", "deleteCustomSearchEngine": "删除自定义搜索引擎", "deleteCustomSearchEngineDesc": "确定要删除自定义搜索引擎 \"{name}\" 吗？此操作无法撤销。", "testSearchEngine": "测试搜索引擎", "testSearchEngineDesc": "即将使用 {engine} 搜索引擎进行测试搜索，将搜索关键词 \"天气\"。", "testSearchEngineNote": "如果搜索页面需要登录或其他操作，您可以在测试窗口中进行。完成测试后请关闭测试窗口。", "theme": "主题", "themeSelect": "选择主题", "closeToQuit": "点击关闭按钮时退出程序", "closeToQuitDesc": "选择点击关闭按钮时是退出程序还是隐藏到系统托盘，隐藏后可从托盘恢复", "shortcut": {"title": "快捷键设置", "newChat": "新建聊天"}, "contentProtection": "投屏保护", "contentProtectionDesc": "防止屏幕共享软件捕获DeepChat窗口，保护聊天内容隐私，需要重启生效", "contentProtectionDialogTitle": "投屏保护切换确认", "contentProtectionEnableDesc": "开启投屏保护可以防止投屏软件捕获DeepChat主窗口，用来保护您的内容隐私。请注意，此功能不会彻底隐藏所有界面，请合理合规使用。并且，并不是所有投屏软件都遵守用户隐私设定，该功能可能会在一些不遵守隐私设定的投屏软件上失效，且部分环境中可能会残留一个黑色窗体。", "contentProtectionDisableDesc": "关闭投屏保护将允许投屏软件捕获DeepChat窗口。", "contentProtectionRestartNotice": "切换此设置将导致程序重启，请确认是否继续？", "soundEnabled": "启用音效", "soundEnabledDesc": "启用消息接收、错误提示等系统音效，提供更好的交互体验", "copyWithCotEnabled": "复制COT信息", "copyWithCotEnabledDesc": "复制消息时包含思维链（Chain of Thought）信息，方便理解AI的推理过程", "loggingEnabled": "启用日志", "loggingEnabledDesc": "启用日志记录可帮助诊断问题和改进应用，但可能包含敏感信息，需要重启生效", "devToolsAutoOpen": "自动打开开发者工具", "devToolsAutoOpenDesc": "在创建窗口和标签页时自动打开开发者工具，方便调试和开发", "loggingDialogTitle": "确认日志设置更改", "loggingEnableDesc": "启用日志将帮助我们诊断问题并改进应用程序。日志文件可能包含敏感信息。", "loggingDisableDesc": "禁用日志将停止收集应用程序日志。", "loggingRestartNotice": "切换此设置将导致程序重启，请确认是否继续？", "devToolsDialogTitle": "确认开发者工具设置更改", "devToolsEnableDesc": "启用开发者工具自动打开将在创建新窗口和标签页时自动打开开发者工具。", "devToolsDisableDesc": "禁用开发者工具自动打开将不再自动打开开发者工具。", "devToolsRestartNotice": "此设置将立即生效，在下次创建窗口时生效。", "openLogFolder": "打开日志文件夹", "notifications": "系统通知", "notificationsDesc": "当 DeepChat 不在前台时，如有会话生成完毕会发送系统通知"}, "data": {"title": "数据设置", "syncEnable": "启用数据同步", "syncFolder": "同步文件夹", "openSyncFolder": "打开同步文件夹", "lastSyncTime": "上次同步时间", "never": "从未同步", "startBackup": "立即备份", "backingUp": "备份中...", "importData": "导入数据", "incrementImport": "增量导入", "overwriteImport": "覆盖导入", "importConfirmTitle": "确认导入数据", "importConfirmDescription": "导入将会覆盖当前所有数据，包括聊天记录和设置。请确保已备份重要数据。导入完成后需要重启应用程序。", "importing": "导入中...", "confirmImport": "确认导入", "importSuccessTitle": "导入成功", "importErrorTitle": "导入失败", "resetData": "重置数据", "resetConfirmTitle": "确认重置数据", "resetConfirmDescription": "请选择要重置的数据类型。此操作不可撤销，重置后应用将自动重启。", "resetChatData": "重置聊天数据", "resetChatDataDesc": "删除所有聊天记录和对话历史", "resetKnowledgeData": "重置知识库数据", "resetKnowledgeDataDesc": "删除所有知识库文件和向量数据", "resetConfig": "重置配置", "resetConfigDesc": "删除所有应用设置、模型配置和自定义提示词", "resetAll": "完全重置", "resetAllDesc": "删除所有数据，包括聊天记录、配置和缓存文件", "resetting": "重置中...", "confirmReset": "确认重置", "resetCompleteDevTitle": "数据重置完成", "resetCompleteDevMessage": "开发环境下请手动重启应用以查看效果。请停止当前进程并重新运行 pnpm run dev"}, "model": {"title": "模型设置", "systemPrompt": {"label": "系统提示词", "placeholder": "请输入系统提示词...", "description": "设置AI助手的系统提示词，用于定义其行为和角色"}, "temperature": {"label": "模型温度", "description": "控制输出的随机性，较高的值会产生更具创造性的响应"}, "contextLength": {"label": "上下文长度", "description": "设置对话上下文的最大长度"}, "responseLength": {"label": "返回文本长度", "description": "设置AI响应的最大长度"}, "artifacts": {"title": "Artifacts效果", "description": "代码块生成动画效果"}, "provider": "服务商", "modelList": "模型列表", "selectModel": "选择模型", "providerSetting": "服务商设置", "configureModel": "配置模型", "addModel": "添加模型", "modelConfig": {"title": "自定义模型参数", "description": "请注意，此配置仅对当前模型有效，不会影响其他模型，请谨慎修改，错误的参数可能导致模型无法正常工作。", "maxTokens": {"label": "最大输出长度", "description": "设置模型单次输出的最大Token数量"}, "contextLength": {"label": "上下文长度", "description": "设置模型能够处理的上下文长度"}, "temperature": {"label": "温度", "description": "控制输出的随机性，大部分模型0-1，部分支持0-2之间，越高越随机"}, "vision": {"label": "视觉能力", "description": "模型是否支持视觉能力"}, "functionCall": {"label": "函数调用", "description": "模型是否原生支持函数调用(关闭这个选项后DeepChat会自动模拟函数调用)"}, "reasoning": {"label": "推理能力", "description": "模型是否支持推理能力"}, "thinkingBudget": {"label": "思考预算", "description": "限制模型思考长度", "dynamic": "动态思维", "range": "范围: {min} - {max}", "onlySupported": "仅在 Gemini 2.5 Flash、2.5 Pro 和 2.5 Flash-Li<PERSON> 中受支持", "valueLabel": "思考预算值", "placeholder": "输入思考预算值", "forceEnabled": "Gemini 2.5 系列模型强制开启思考预算", "dynamicPrefix": "-1 = 动态思维", "notice": "注意：", "warnings": {"proNoDisable": "此模型不支持停用思考，最小值 128", "proCannotDisable": "Gemini 2.5 Pro 无法停用思考功能", "flashLiteCannotSetZero": "Gemini 2.5 Flash-Lite 不能设置为 0，最小值为 512", "modelCannotDisable": "此模型无法停用思考功能", "flashLiteMinValue": "思考预算设置为具体数值时不能小于 512（或使用 0 停用思考，-1 启用动态思维）", "belowMin": "思考预算不能小于 {min}{hint}", "aboveMax": "思考预算不能大于 {max}"}, "hints": {"flashLiteDisable": "，0 = 停用思考，具体数值最小 512", "normalDisable": "，0 = 停用思考", "withZeroAndDynamic": "（或使用 0 停用思考，-1 启用动态思维）", "withDynamic": "（或使用 -1 启用动态思维）"}}, "type": {"label": "模型类型", "description": "选择模型的类型", "options": {"chat": "语言模型", "embedding": "嵌入模型", "rerank": "重排序模型", "imageGeneration": "图像生成模型"}}, "resetToDefault": "重置为默认", "saveConfig": "保存配置", "cancel": "取消", "useModelDefault": "使用模型默认配置", "currentUsingModelDefault": "当前使用模型默认配置", "resetConfirm": {"title": "确认重置", "message": "确定要重置此模型的配置为默认值吗？此操作不可撤销。", "confirm": "确定重置"}, "reasoningEffort": {"label": "推理努力程度", "description": "控制模型的推理深度，更高的努力程度会产生更好的结果但响应更慢", "placeholder": "选择推理努力程度", "options": {"minimal": "Minimal - 最快响应", "low": "Low - 低努力", "medium": "Medium - 中等努力", "high": "High - 高努力"}}, "verbosity": {"label": "详细程度", "description": "控制模型回答的详细程度和长度", "placeholder": "选择详细程度", "options": {"low": "Low - 简洁回答", "medium": "Medium - 平衡详细", "high": "High - 详细回答"}}, "validation": {"maxTokensRequired": "最大输出长度不能为空", "maxTokensMin": "最大输出长度必须大于0", "maxTokensMax": "最大输出长度不能超过1000000", "contextLengthRequired": "上下文长度不能为空", "contextLengthMin": "上下文长度必须大于0", "contextLengthMax": "上下文长度不能超过10000000", "temperatureRequired": "温度不能为空", "temperatureMin": "温度必须大于等于0", "temperatureMax": "温度必须小于等于2"}}}, "provider": {"search": "搜索服务商平台…", "enable": "开启服务", "enabled": "已启用", "disabled": "已禁用", "urlPlaceholder": "请输入API URL", "keyPlaceholder": "请输入API Key", "accessKeyIdPlaceholder": "请输入AWS Access Key ID", "secretAccessKeyPlaceholder": "请输入AWS Secret Access Key", "regionPlaceholder": "请输入AWS区域", "verifyKey": "验证密钥", "howToGet": "如何获取", "refreshingModels": "刷新中...", "getKeyTip": "请前往", "getKeyTipEnd": "获取API Key", "urlFormat": "API样例：{defaultUrl}", "modelList": "模型列表", "enableModels": "启用模型", "disableAllModels": "禁用全部", "modelsEnabled": "模型已经启用", "noModelsEnabled": {"title": "暂无已启用的模型", "description": "请点击「启用模型」按钮手动选择需要使用的模型。"}, "verifyLink": "验证链接", "syncModelsFailed": "同步模型失败...", "addCustomProvider": "添加自定义服务商", "delete": "删除", "stopModel": "停止模型", "pulling": "拉取中...", "runModel": "运行模型", "configurationUpdated": "配置已更新", "configurationSaved": "配置已保存", "operationSuccess": "操作成功", "operationFailed": "操作失败", "dataRefreshed": "数据已刷新", "settingsApplied": "设置已应用", "toast": {"modelRunning": "模型正在运行", "modelRunningDesc": "请先停止模型 {model}，然后再删除。"}, "modelscope": {"name": "ModelScope", "description": "ModelScope 是阿里巴巴达摩院推出的模型即服务共享平台", "apiKey": "API 密钥", "apiKeyPlaceholder": "请输入 ModelScope API Key", "apiKeyHelper": "在 ModelScope 控制台获取您的 API Key", "baseUrl": "API 地址", "baseUrlHelper": "ModelScope API 服务地址", "connected": "已连接", "notConnected": "未连接", "connecting": "连接中...", "verifySuccess": "验证成功", "verifyFailed": "验证失败", "keyRequired": "请输入 API Key", "invalidKey": "无效的 API Key", "networkError": "网络连接错误", "mcpSync": {"title": "同步 MCP 服务", "description": "从 ModelScope 同步 MCP 服务器到本地配置，可以快速添加常用的 MCP 工具。所有服务默认禁用，导入后可手动启用。", "sync": "开始同步", "syncing": "同步中...", "pageSize": "每页数量", "pageNumber": "页码", "pageNumberPlaceholder": "请输入页码", "imported": "已导入 {count} 个服务", "skipped": "跳过 {count} 个服务", "errors": "错误 {count} 个", "errorDetails": "错误详情", "noApiKey": "请先配置 ModelScope API Key", "noServersFound": "未找到可用的 MCP 服务", "fetchingServers": "正在获取 MCP 服务器列表...", "convertingServers": "正在转换服务器配置...", "importingServers": "正在导入服务器配置...", "syncComplete": "同步完成", "serverAlreadyExists": "服务器已存在，跳过导入", "noOperationalUrls": "未找到可用的运营地址", "invalidServerData": "无效的服务器数据", "authenticationFailed": "认证失败，请检查 API Key"}, "details": {"title": "提供商设置详情", "apiConfig": "API 配置", "rateLimitConfig": "速率限制配置", "modelManagement": "模型管理", "safetySettings": "安全设置", "specialConfig": "特殊配置", "mcpSync": "MCP 同步", "operationalServers": "运营服务器", "syncFromModelScope": "从 ModelScope 同步", "operationalDescription": "同步 ModelScope 平台上可直接使用的 MCP 服务器"}}, "dialog": {"disableModel": {"title": "确认禁用模型", "content": "是否确认禁用模型 \"{name}\"？", "confirm": "禁用"}, "disableAllModels": {"title": "确认禁用全部模型", "content": "是否确认禁用 \"{name}\" 的全部模型？", "confirm": "全部禁用"}, "configModels": {"title": "配置模型列表", "description": "选择要启用或禁用的模型"}, "verify": {"missingFields": "请输入 API Key 和 API URL", "failed": "验证失败", "success": "验证成功", "failedDesc": "API 密钥或配置验证失败，请检查配置信息", "successDesc": "API 密钥和配置验证成功，可以正常使用", "unauthorized": "认证失败，API Key 无效或已过期", "serverError": "服务器错误，请稍后重试", "connectionError": "连接错误，请检查网络连接和 API 地址"}, "addCustomProvider": {"title": "添加自定义服务商", "description": "请填写服务商的必要信息", "name": "名称", "namePlaceholder": "请输入服务商名称", "apiType": "API类型", "apiTypePlaceholder": "请选择API类型", "apiKey": "API密钥", "apiKeyPlaceholder": "请输入API密钥", "baseUrl": "API地址", "baseUrlPlaceholder": "请输入API基础地址", "enable": "启用服务商"}, "deleteProvider": {"title": "确认删除服务商", "content": "是否确认删除服务商 \"{name}\"？此操作不可恢复。", "confirm": "删除"}, "deleteModel": {"title": "确认删除模型", "content": "是否确认删除模型 \"{name}\"？此操作不可恢复。", "confirm": "删除"}, "pullModel": {"title": "拉取模型", "description": "选择要下载的模型到本地", "pull": "拉取"}, "modelCheck": {"title": "模型检查", "description": "选择一个模型进行连接性和可用性测试", "model": "选择模型", "modelPlaceholder": "请选择要测试的模型", "test": "开始测试", "checking": "测试中...", "success": "模型测试成功", "failed": "模型测试失败", "noModels": "该服务商没有可用的模型"}}, "pullModels": "拉取模型", "refreshModels": "刷新模型", "modelsRunning": "运行中的模型", "runningModels": "运行中的模型", "noRunningModels": "没有运行中的模型", "deleteModel": "删除模型", "deleteModelConfirm": "是否确认删除模型 \"{name}\"？此操作不可恢复。", "noLocalModels": "没有本地模型", "localModels": "本地模型", "azureApiVersion": "API 版本", "safety": {"title": "安全设置", "blockNone": "不屏蔽", "blockSome": "屏蔽低风险", "blockMost": "屏蔽中风险", "blockHighest": "屏蔽高风险"}, "serverList": "服务器列表", "totalServers": "服务器总数", "addServer": "添加服务器", "autoStart": "自启动", "githubCopilotAuth": "GitHub Copilot 认证", "githubCopilotConnected": "GitHub Copilot 已连接", "githubCopilotNotConnected": "GitHub Copilot 未连接", "loginWithGitHub": "使用 GitHub 登录", "loggingIn": "登录中...", "githubCopilotLoginTip": "点击授权 DeepChat 访问您的 GitHub Copilot 订阅", "loginSuccess": "登录成功", "loginFailed": "登录失败", "tokenValid": "令牌有效", "tokenInvalid": "令牌无效", "disconnect": "断开连接", "disconnected": "已成功断开连接", "disconnectFailed": "断开连接失败", "keyStatus": {"usage": "已使用", "remaining": "剩余额度"}, "authMethod": "认证方式", "authMethodPlaceholder": "选择认证方式", "apiKeyLabel": "API Key", "apiUrlLabel": "API URL", "anthropicConnected": "Anthropic 已连接", "anthropicNotConnected": "Anthropic 未连接", "anthropicOAuthTip": "点击授权 DeepChat 访问您的 Anthropic 账户", "oauthLogin": "OAuth 登录", "anthropicApiKeyTip": "请前往 Anthropic Console 获取您的 API Key", "anthropicOAuthFlowTip": "系统将自动打开授权窗口，授权后请回来输入授权码", "browserOpenedSuccess": "外部浏览器已打开，请完成授权", "anthropicBrowserOpened": "外部浏览器已打开", "anthropicCodeInstruction": "请在外部浏览器中完成授权，然后将获得的授权码粘贴到下方输入框中", "inputOAuthCode": "输入授权码", "oauthCodeHint": "请在外部浏览器中完成授权后，将获得的授权码粘贴到此处", "oauthCodePlaceholder": "请输入授权码...", "codeRequired": "请输入授权码", "invalidCode": "授权码无效", "codeExchangeFailed": "授权码交换失败", "verifyConnection": "验证连接", "manageModels": "管理模型", "anthropicOAuthActiveTip": "OAuth 认证已启用，您可以直接使用 Anthropic 服务", "oauthVerifySuccess": "OAuth 连接验证成功", "oauthVerifyFailed": "OAuth 连接验证失败", "bedrockLimitTip": "* 仅支持 Anthropic Claude（包括 Opus，Sonnet，Haiku 模型）", "bedrockVerifyTip": "DeepChat 使用 Claude 3.5 Sonnet 模型验证，若您无此模型的调用权限验证将失败。这不会影响其他模型的使用。"}, "knowledgeBase": {"title": "知识库设置", "addKnowledgeBase": "添加知识库", "selectKnowledgeBaseType": "请选择要添加的知识库类型", "difyDescription": "Dify知识库可以帮助您管理和使用文档数据。", "comingSoon": "即将推出", "featureNotAvailable": "该功能暂未开放，敬请期待", "addDifyConfig": "添加Dify配置", "apiKey": "API密钥", "datasetId": "数据集ID", "endpoint": "API地址", "configAdded": "配置已添加", "configAddedDesc": "{name}配置已成功添加", "addConfig": "添加配置", "moreComingSoon": "更多知识库类型即将推出", "configUpdated": "配置已更新", "configUpdatedDesc": "{name}配置已成功更新", "descriptionPlaceholder": "例如：公司产品文档知识库", "ragflowTitle": "RAGFlow知识库", "ragflowDescription": "RAGFlow是一个强大的知识库管理系统，支持多种检索方式和文档管理功能。", "addRagflowConfig": "添加RAGFlow配置", "editRagflowConfig": "编辑RAGFlow配置", "dify": "Dify知识库", "editDifyConfig": "修改Dify配置", "fastgptTitle": "FastGPT知识库", "fastgptDescription": "FastGPT是一个强大的知识库管理系统，支持多种检索方式和文档管理功能。", "addFastGptConfig": "添加FastGPT配置", "editFastGptConfig": "编辑FastGPT配置", "builtInKnowledgeTitle": "内置知识库", "builtInKnowledgeDescription": "内置知识库提供了一些简单实现，能够在离线环境下实现部分基础功能。", "addBuiltinKnowledgeConfig": "添加内置知识库配置", "editBuiltinKnowledgeConfig": "编辑内置知识库配置", "descriptionDesc": "知识库的描述，以便 AI 决定是否检索此知识库", "embeddingModel": "嵌入模型", "selectEmbeddingModel": "选择嵌入模型", "selectEmbeddingModelHelper": "嵌入模型在知识库创建后禁止修改", "rerankModel": "重排序模型", "selectRerankModel": "选择重排序模型", "chunkSize": "分块大小", "chunkOverlap": "重叠大小", "fragmentsNumber": "请求文档片段数量", "chunkSizeHelper": "将文档切割分段，每段的大小，不能超过模型上下文限制", "chunkOverlapHelper": "相邻文本块之间重复的内容量，确保分段后的文本块之间仍然有上下文联系，提升模型处理长文本的整体效果", "fragmentsNumberHelper": "请求文档片段数量越多，附带的信息越多，但需要消耗的token也越多", "modelNotFound": "服务商 {provider} 或模型 {model} 未找到", "modelNotFoundDesc": "请确保已正确配置模型，并且模型处于启用状态。您可以在服务商设置中检查模型配置。", "removeBuiltinKnowledgeConfirmTitle": "确认删除内置知识库 {name} ？", "removeBuiltinKnowledgeConfirmDesc": "删除内置知识库配置将会删除所有相关数据，且无法恢复，请谨慎操作。", "advanced": "高级选项", "dimensions": "嵌入维度", "dimensionsPlaceholder": "嵌入维度大小，如 1024", "autoDetectDimensions": "自动检测嵌入维度", "autoDetectHelper": "自动检测嵌入维度，会消耗少量 Tokens", "dimensionsHelper": "请确保模型支持所设置的嵌入维度大小", "autoDetectDimensionsError": "自动检测嵌入维度失败", "normalized": "L2 归一化", "normalizedHelper": "请确认模型支持对输出向量进行 L2 归一化", "chunkSizePlaceholder": "默认值，不建议修改", "chunkOverlapPlaceholder": "默认值，不建议修改", "return": "返回", "uploadHelper": "点击上传或拖拽文件到此处", "fileSupport": "支持 {accept} 等 {count} 种格式", "searchKnowledge": "搜索知识库", "searchKnowledgePlaceholder": "请输入查询内容", "noData": "暂无数据", "file": "文件", "uploadProcessing": "上传中", "uploadCompleted": "上传完成", "reAdd": "重新上传", "uploadError": "上传失败", "processing": "正在上传", "paused": "已暂停", "unknown": "未知状态", "delete": "删除", "reason": "原因", "deleteSuccess": "删除成功", "copy": "复制", "copySuccess": "复制成功", "source": "来源", "resumeAllPausedTasks": "一键恢复", "pauseAllRunningTasks": "一键暂停", "reAddFile": {"title": "重新上传确认", "content": "是否确认重新上传文件 \"{fileName}\"？"}, "deleteFile": {"title": "删除文件确认", "content": "是否确认删除文件 \"{fileName}\"？此操作不可恢复。"}, "dialog": {"beforequit": {"title": "退出确认", "description": "有正在运行的知识库任务，是否确认退出软件？\n被中止的任务可在重启软件后恢复。", "cancel": "取消", "confirm": "确认"}}, "searchError": "查询失败", "separators": "块分隔符", "separatorsHelper": "文档切分分隔符，单个分隔符用半角符号双引号(\"\")包裹，分隔符之间用半角符号逗号(,)分割", "invalidSeparators": "无效的分隔符", "selectLanguage": "选择预设", "separatorsPreset": "加载预设", "promptManagement": {"title": "Prompt管理", "description": "管理和使用自定义Prompt模板，帮助您更好地与AI交互"}}, "mcp": {"title": "MCP设置", "description": "管理和配置MCP（模型控制协议）服务器和工具", "enabledTitle": "启用MCP", "enabledDescription": "启用或禁用MCP功能和工具", "enableToAccess": "请先启用MCP以访问配置选项", "marketplace": "前往MCP市场一键安装", "technicalDetails": "技术详情", "httpServer": "HTTP服务器", "localProcess": "本地进程", "restartServer": "重启服务器", "viewLogs": "查看日志", "starting": "启动中", "error": "错误", "tabs": {"servers": "服务器", "tools": "工具", "prompts": "提示词", "resources": "资源"}, "serverList": "服务器列表", "addServer": "添加服务器", "running": "运行中", "stopped": "已停止", "stopServer": "停止服务器", "startServer": "启动服务器", "noServersFound": "未找到服务器", "addServerDialog": {"title": "添加服务器", "description": "配置新的MCP服务器"}, "editServerDialog": {"title": "编辑服务器", "description": "编辑MCP服务器配置"}, "serverForm": {"name": "服务器名称", "namePlaceholder": "输入服务器名称", "nameRequired": "服务器名称不能为空", "type": "服务器类型", "typePlaceholder": "选择服务器类型", "typeStdio": "标准输入输出(Stdio)", "typeSse": "服务器发送事件(SSE)", "typeInMemory": "内存型(InMemory)", "typeHttp": "可流式传输的HTTP请求(HTTP)", "baseUrl": "基础URL", "baseUrlPlaceholder": "输入服务器基础URL（如：http://localhost:3000）", "command": "命令", "commandPlaceholder": "输入命令", "commandRequired": "命令不能为空", "args": "参数", "argsPlaceholder": "输入参数，用空格分隔", "argsRequired": "参数不能为空", "env": "环境变量", "envPlaceholder": "输入JSON格式的环境变量", "envInvalid": "环境变量必须是有效的JSON格式", "description": "描述", "descriptionPlaceholder": "输入服务器描述", "descriptions": "描述", "descriptionsPlaceholder": "输入服务器描述", "icon": "图标", "iconPlaceholder": "输入图标", "icons": "图标", "iconsPlaceholder": "输入图标", "autoApprove": "自动授权", "autoApproveAll": "全部", "autoApproveRead": "读取", "autoApproveWrite": "写入", "autoApproveHelp": "选择需要自动授权的操作类型，无需用户确认即可执行", "submit": "提交", "add": "添加", "update": "更新", "cancel": "取消", "jsonConfigIntro": "您可以直接粘贴JSON配置或选择手动配置服务器。", "jsonConfig": "JSON配置", "jsonConfigPlaceholder": "请粘贴MCP服务器的JSON格式配置", "jsonConfigExample": "JSON配置示例", "parseSuccess": "配置解析成功", "configImported": "配置导入成功", "parseError": "解析错误", "skipToManual": "跳过至手动配置", "parseAndContinue": "解析并继续", "jsonParseError": "JSON解析失败", "browseMarketplace": "浏览MCP服务市场", "imageModel": "选择视觉模型", "customHeadersParseError": "自定义Header解析失败", "customHeaders": "自定义请求头", "clickToEdit": "点击编辑以查看完整内容", "invalidKeyValueFormat": "错误的请求头格式，请检查输入是否正确", "npmRegistry": "自定义NPM Registry", "npmRegistryPlaceholder": "设置自定义NPM Registry,留空系统会自动选择最快的一个", "browseHigress": "浏览 Higress MCP 市场", "selectFolderError": "文件夹选择错误", "folders": "允许访问的文件夹", "addFolder": "添加文件夹", "noFoldersSelected": "未选择任何文件夹", "useE2B": "启用E2B沙盒", "e2bDescription": "使用E2B沙盒执行Python代码", "e2bApiKey": "E2B ApiKey", "e2bApiKeyPlaceholder": "这里输入你的E2B Api Keys，如 e2b_1111xx*****", "e2bApiKeyHelp": "前往 e2b.dev 获取你的 ApiKey", "e2bApiKeyRequired": "启用E2B功能必须要输入 ApiKey"}, "deleteServer": "删除服务器", "editServer": "编辑服务器", "setDefault": "设为默认", "removeDefault": "移除默认", "isDefault": "默认服务器", "default": "默认", "setAsDefault": "设为默认服务器", "removeServer": "删除服务器", "autoStart": "自启动", "confirmRemoveServer": "确定要删除服务器 {name} 吗？此操作无法撤销。", "removeServerDialog": {"title": "删除服务器"}, "confirmDelete": {"title": "确认删除", "description": "确定要删除服务器 {name} 吗？此操作无法撤销。", "confirm": "删除", "cancel": "取消"}, "resetToDefault": "恢复默认服务", "resetConfirmTitle": "恢复默认服务", "resetConfirmDescription": "此操作将恢复所有默认服务器，同时保留您自定义的服务器。对默认服务器的任何修改将会丢失。", "resetConfirm": "恢复", "builtInServers": "内置服务", "customServers": "自定义服务", "builtIn": "内置", "cannotRemoveBuiltIn": "无法删除内置服务", "builtInServerCannotBeRemoved": "内置服务不能被删除，仅支持修改参数和环境变量", "maxDefaultServersReached": "最多只能设置3个默认服务器", "removeDefaultFirst": "请先移除一些默认服务器", "higressMarket": "前往 Higress MCP 安装", "totalServers": "服务器总数", "npmRegistry": {"title": "NPM源配置", "currentSource": "当前源", "cached": "缓存", "lastChecked": "上次检测", "refresh": "刷新", "advanced": "高级", "advancedSettings": "高级设置", "advancedSettingsDesc": "配置NPM源的高级选项，包括自动检测和自定义源设置", "autoDetect": "自动检测最优源", "autoDetectDesc": "启动时自动检测并使用最快的NPM源", "customSource": "自定义源", "customSourcePlaceholder": "输入自定义NPM源地址", "currentCustom": "当前自定义源", "justNow": "刚刚", "minutesAgo": "{minutes}分钟前", "hoursAgo": "{hours}小时前", "daysAgo": "{days}天前", "refreshSuccess": "NPM源刷新成功", "refreshSuccessDesc": "已重新检测并更新最优NPM源", "refreshFailed": "NPM源刷新失败", "autoDetectUpdated": "自动检测设置已更新", "autoDetectEnabled": "已启用自动检测最优NPM源", "autoDetectDisabled": "已禁用自动检测，将使用默认源", "updateFailed": "设置更新失败", "customSourceSet": "自定义源已设置", "customSourceSetDesc": "已设置自定义NPM源：{registry}", "customSourceCleared": "自定义源已清除", "customSourceClearedDesc": "已清除自定义NPM源，将使用自动检测", "invalidUrl": "无效的URL", "invalidUrlDesc": "请输入有效的HTTP或HTTPS地址", "testing": "正在测试NPM源", "testingDesc": "正在测试源 {registry} 的连通性...", "testFailed": "NPM源测试失败", "testFailedDesc": "无法连接到 {registry}，错误：{error}。请检查URL是否正确或网络连接。", "redetectingOptimal": "正在重新检测最优NPM源...", "redetectComplete": "重新检测完成", "redetectCompleteDesc": "已检测并设置当前最优的NPM源", "redetectFailed": "重新检测失败", "redetectFailedDesc": "无法重新检测最优源，将使用默认配置"}}, "about": {"title": "关于我们", "version": "版本", "checkUpdate": "检查更新", "checking": "检查中...", "latestVersion": "已是最新版本"}, "display": {"fontSize": "文字大小", "fontSizeDesc": "调整应用界面的文字大小，包括聊天内容、菜单和设置界面的字体大小", "text-sm": "小", "text-base": "默认", "text-lg": "大", "text-xl": "特大", "text-2xl": "超大", "floatingButton": "悬浮按钮", "floatingButtonDesc": "在桌面显示一个悬浮按钮，可以快速唤起应用窗口"}, "shortcuts": {"title": "快捷键设置", "pressKeys": "输入快捷键", "pressEnterToSave": "按Enter保存，Esc取消", "noModifierOnly": "不能仅使用修饰键作为快捷键", "keyConflict": "快捷键冲突，请选择其他组合", "clearShortcut": "清除快捷键", "zoomIn": "放大字体", "zoomOut": "缩小字体", "zoomReset": "重置字体", "goSettings": "打开设置", "cleanHistory": "清除聊天历史", "deleteConversation": "删除会话", "hideWindow": "隐藏窗口", "quitApp": "退出程序", "newWindow": "打开新窗口", "newTab": "新建标签页", "closeTab": "关闭当前标签页", "showHideWindow": "显示/隐藏窗口", "newConversation": "新会话", "nextTab": "切换到下一个标签页", "previousTab": "切换到上一个标签页", "specificTab": "切换到指定标签页 (1-8)", "lastTab": "切换到最后一个标签页"}, "rateLimit": {"title": "速率限制", "description": "控制请求间隔时间，防止超出API限制", "intervalLimit": "请求间隔", "intervalUnit": "秒", "intervalHelper": "两次请求之间的最小间隔时间，不需要时请关闭速率限制", "lastRequestTime": "上次请求", "queueLength": "队列长度", "nextAllowedTime": "下次可请求", "never": "从未", "justNow": "刚刚", "secondsAgo": "秒前", "minutesAgo": "分钟前", "immediately": "立即", "secondsLater": "秒后", "confirmDisableTitle": "确认关闭速率限制", "confirmDisableMessage": "该值不能小于或等于0，是否关闭速率限制功能？", "confirmDisable": "关闭限制", "disabled": "速率限制已关闭", "disabledDescription": "速率限制功能已关闭"}}