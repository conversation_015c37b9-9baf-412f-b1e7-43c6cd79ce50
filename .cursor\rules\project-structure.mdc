---
description:
globs:
alwaysApply: false
---
# 项目结构指南

本项目是一个基于 Electron + Vue 3 的桌面应用程序。主要目录结构如下：

- `src/`: 源代码目录
  - `main/`: Electron 主进程代码
  - `renderer/`: 渲染进程代码（Vue 3 应用）
  - `shared/`: 主进程和渲染进程共享的类型定义和工具

- `resources/`: 应用程序资源文件
- `build/`: 构建相关配置
- `scripts/`: 构建和开发脚本
- `docs/`: 项目文档
- `tests/`: 测试文件

主要配置文件：
- [electron.vite.config.ts](mdc:electron.vite.config.ts): Vite 构建配置
- [electron-builder.yml](mdc:electron-builder.yml): Electron Builder 配置
- [tsconfig.json](mdc:tsconfig.json): TypeScript 配置
- [tailwind.config.js](mdc:tailwind.config.js): Tailwind CSS 配置

开发规范：
1. 所有新功能应该在 `src` 目录下开发
2. 共享类型定义放在 `shared` 目录
3. 主进程代码放在 `src/main`
4. 渲染进程代码放在 `src/renderer`
5. 测试文件放在 `tests` 目录
