---
description: Best practices for Vue Router
globs: src/renderer/src/router/**/*.{vue,ts,tsx,js,jsx}
alwaysApply: false
---
- Use named routes for easier navigation and maintenance
- Implement route-level code splitting for better performance
- Use route meta fields for attaching additional data to routes
- Implement proper navigation guards for authentication and authorization
- Use dynamic routing for handling variable route segments