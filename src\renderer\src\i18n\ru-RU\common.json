{"enabled": "Включено", "disabled": "Выключено", "loading": "Загрузка...", "copySuccess": "Скопировано", "copySuccessDesc": "Содержимое скопировано в буфер обмена", "copyCode": "Скопировать код", "copy": "Копировать", "paste": "Вставить", "export": "Экспорт", "newChat": "Новый чат", "newTopic": "Новая тема", "cancel": "Отмена", "confirm": "Подтвердить", "close": "Закрыть", "error": {"requestFailed": "Ошибка запроса...", "createChatFailed": "Не удалось создать чат", "selectChatFailed": "Не удалось выбрать чат", "renameChatFailed": "Не удалось переименовать чат", "deleteChatFailed": "Не удалось удалить чат", "userCanceledGeneration": "Пользователь отменил генерацию", "sessionInterrupted": "Сессия прервана, генерация не завершена", "noModelResponse": "Модель не вернула никаких данных, возможно, истекло время ожидания", "cleanMessagesFailed": "Не удалось очистить сообщения чата", "invalidJson": "Неверный JSON формат", "maximumToolCallsReached": "Достигнуто максимальное количество вызовов инструментов", "causeOfError": "Возможные причины ошибки:", "error400": "Ошибка запроса, параметры или проблемы совместимости", "error401": "Провальная аутентификация, неправильный ключ API или доменное имя настроено", "error404": "Адрес запроса не существует, настроенное доменное имя или имя модели неверно", "error429": "Скорость запроса слишком быстрая, а частота доступа ограничена поставщиком услуг", "error500": "В сервере есть ошибка, и запрашиваемая служба может быть не очень стабильной в настоящее время. Вы можете попробовать позже", "error502": "Ошибка шлюза, запрошенная служба может не быть стабильной в настоящее время, вы можете попробовать еще раз позже", "error503": "Служба недоступна, запрашиваемая услуга может быть в настоящее время нестабильной, вы можете попробовать его позже", "error504": "Запрос истекает, запрашиваемая служба может быть в настоящее время нестабильной, или сетевая ссылка является неисправной. Пожалуйста, проверьте прокси и другую конфигурацию сети, прежде чем пытаться.", "error403": "Доступ к этой модели может быть связан с недостаточным балансом или отсутствием разрешения на доступ к доступу", "operationFailed": "Операция не удалась"}, "resetDataConfirmTitle": "Сбросить все данные?", "resetDataConfirmDescription": "Это сбросит все ваши данные до настроек по умолчанию. Это действие нельзя отменить.", "proxyMode": "Режим прокси", "proxyModeSelect": "Выбрать режим прокси", "proxyModeSystem": "Системный прокси", "proxyModeNone": "Без прокси", "proxyModeCustom": "Пользовательский прокси", "customProxyUrl": "URL пользовательского прокси", "customProxyUrlPlaceholder": "Пример: http://127.0.0.1:7890", "invalidProxyUrl": "Неверный URL прокси, введите корректный http/https URL", "disclaimer": "Отказ от ответственности", "language": "язык", "languageSelect": "Выберите язык", "resetData": "Сбросить данные", "searchAssistantModel": "Модель поиска", "searchEngine": "Поисковая система", "searchEngineSelect": "Выберите поисковую систему", "searchPreview": "Поиск предварительного просмотра", "selectModel": "Выберите модель", "title": "Общие настройки", "languageSystem": "Следуйте системе", "watermarkTip": "Сгенерировано AI", "collapse": "Свернуть", "expand": "Развернуть", "image": "картина", "copyImageSuccess": "Скопировано", "copyImageSuccessDesc": "Изображение скопировано в буфер обмена", "copyFailed": "Не удалось скопировать", "copyFailedDesc": "Не удалось скопировать содержимое в буфер обмена", "add": "Добавить", "reset": "Сброс", "format": "Формат", "edit": "Редактировать", "delete": "Удалить", "save": "Сохранить", "clear": "Очистить", "saved": "Сох<PERSON><PERSON><PERSON><PERSON>л", "closeToQuit": "Выйти из приложения при закрытии окна"}