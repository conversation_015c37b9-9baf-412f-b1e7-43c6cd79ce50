{"title": "Настройки", "common": {"title": "Общие настройки", "modelSettings": "Настройки модели", "searchSettings": "Настройки поиска", "networkSettings": "Настройки сети", "interfaceSettings": "Интерфейс и взаимодействие", "personalizationSettings": "Настройки персонализации", "securitySettings": "Безопасность и конфиденциальность", "operationSettings": "Настройки операций", "resetData": "Сбросить данные", "language": "Язык", "languageSelect": "Выбрать язык", "searchEngine": "Поисковая система", "searchEngineDesc": "Выберите поисковую систему для улучшения веб-поиска. Поддерживает основные поисковые системы как Google, Yandex и пользовательские поисковые системы", "searchEngineSelect": "Выбрать поисковую систему", "searchPreview": "Предпросмотр поиска", "searchPreviewDesc": "Включите миниатюры предпросмотра на страницах результатов поиска для быстрого просмотра содержимого поиска", "searchAssistantModel": "Помощник модели", "searchAssistantModelDesc": "Выберите модель для обработки результатов поиска и генерации ответов. Влияет на качество и скорость улучшения поиска", "selectModel": "Выбрать модель", "proxyMode": "Режим прокси", "proxyModeDesc": "Настройте параметры сетевого прокси. Выберите системный прокси, без прокси или пользовательский прокси для доступа к зарубежным сервисам", "proxyModeSelect": "Выбрать режим прокси", "proxyModeSystem": "Системный прокси", "proxyModeNone": "Без прокси", "proxyModeCustom": "Пользовательский прокси", "customProxyUrl": "Пользовательский URL прокси", "customProxyUrlPlaceholder": "Например: http://127.0.0.1:7890", "invalidProxyUrl": "Недействительный URL прокси, введите действительный http/https URL", "addCustomSearchEngine": "Добавить пользовательскую поисковую систему", "addCustomSearchEngineDesc": "Добавьте новую поисковую систему, указав имя и URL поиска. URL должен включать {query} в качестве заполнителя.", "searchEngineName": "Название поисковой системы", "searchEngineNamePlaceholder": "Введите название поисковой системы", "searchEngineUrl": "URL поиска", "searchEngineUrlPlaceholder": "Например: https://example.com/search?q={query}", "searchEngineUrlError": "URL должен содержать {query} в качестве заполнителя запроса", "deleteCustomSearchEngine": "Удалить пользовательскую поисковую систему", "deleteCustomSearchEngineDesc": "Вы уверены, что хотите удалить пользовательскую поисковую систему \"{name}\"? Это действие нельзя отменить.", "contentProtection": "Защита экрана", "contentProtectionDialogTitle": "Подтвердите изменение защиты экрана", "contentProtectionEnableDesc": "Включение защиты экрана предотвращает захват окна DeepChat программами для совместного использования экрана, защищая конфиденциальность вашего контента. Обратите внимание, что эта функция не полностью скрывает все интерфейсы. Пожалуйста, используйте эту функцию ответственно и в соответствии с правилами. Кроме того, не все программы для совместного использования экрана соблюдают настройки конфиденциальности пользователей, и эта функция может не работать на некоторых программах. Кроме того, в некоторых окружениях может остаться черный экран.", "contentProtectionDisableDesc": "Отключение защиты экрана позволит программам для совместного использования экрана захватывать окно DeepChat.", "contentProtectionRestartNotice": "Изменение этой настройки приведет к перезапуску приложения. Вы хотите продолжить?", "testSearchEngine": "Тестировать поисковую систему", "testSearchEngineDesc": "Будет выполнен тестовый поиск по запросу \"погода\" с использованием поисковой системы {engine}.", "testSearchEngineNote": "Если поисковая страница требует входа или других действий, вы можете выполнить их в тестовом окне. Пожалуйста, закройте тестовое окно, когда закончите.", "theme": "Тема", "soundEnabled": "Звуковые уведомления", "soundEnabledDesc": "Включите системные звуковые эффекты для уведомлений о сообщениях, предупреждений об ошибках и других взаимодействий для лучшего пользовательского опыта", "copyWithCotEnabled": "Скопировать информацию COT", "copyWithCotEnabledDesc": "Включать информацию о цепочке размышлений при копировании сообщений, чтобы помочь понять процесс рассуждений ИИ", "loggingEnabled": "Включить логирование", "loggingEnabledDesc": "Включить логирование для диагностики проблем и улучшения приложения. Может содержать конфиденциальную информацию и требует перезапуска для вступления в силу", "loggingDialogTitle": "Подтверждение изменения настроек логирования", "loggingEnableDesc": "Включение логирования поможет нам диагностировать проблемы и улучшить приложение. Файлы логов могут содержать конфиденциальную информацию.", "loggingDisableDesc": "Отключение логирования остановит сбор логов приложения.", "loggingRestartNotice": "Изменение этой настройки приведет к перезапуску приложения. Вы хотите продолжить?", "openLogFolder": "Открыть папку логов", "closeToQuit": "Выйдите из программы при нажатии кнопки «Закрыть»", "closeToQuitDesc": "Выберите, следует ли выйти из приложения или скрыть в системный трей при нажатии кнопки закрытия. Скрытое приложение можно восстановить из трея", "shortcut": {"newChat": "Создать новый чат", "title": "Настройки клавиши ярлыка"}, "themeSelect": "Выберите тему", "notifications": "Системное уведомление", "notificationsDesc": "Когда DeepChat не находится на переднем плане, при завершении генерации сеанса будет отправлено системное уведомление", "languageDesc": "Выберите язык интерфейса приложения. Поддерживает несколько языков, включая русский, английский, китайский и др. Изменения вступают в силу немедленно", "contentProtectionDesc": "Предотвращение захвата окна DeepChat приложениями для демонстрации экрана для защиты конфиденциальности содержимого чата. Требует перезапуска для вступления в силу"}, "data": {"title": "Настройки данных", "syncEnable": "Включить синхронизацию данных", "syncFolder": "Папка синхронизации", "openSyncFolder": "Открыть папку синхронизации", "lastSyncTime": "Время последней синхронизации", "never": "Никогда", "startBackup": "Начать резервное копирование", "backingUp": "Резервное копирование...", "importData": "Импорт данных", "incrementImport": "Инкрементальный импорт", "overwriteImport": "Импорт с перезаписью", "importConfirmTitle": "Подтверждение импорта данных", "importConfirmDescription": "Импорт перезапишет все текущие данные, включая историю чатов и настройки. Убедитесь, что вы сделали резервную копию важных данных. После импорта потребуется перезапуск приложения.", "importing": "Импорт...", "confirmImport": "Подтвердить импорт", "importSuccessTitle": "Импорт успешно завершен", "importErrorTitle": "Ошибка импорта", "resetData": "Сбросить данные", "resetConfirmTitle": "Подтверждение сброса данных", "resetConfirmDescription": "Выберите тип данных для сброса. Эта операция необратима, после сброса приложение автоматически перезапустится.", "resetChatData": "Сбросить данные чатов", "resetChatDataDesc": "Удалить всю историю чатов и записи разговоров", "resetKnowledgeData": "Сбросить данные базы знаний", "resetKnowledgeDataDesc": "Удалить все файлы базы знаний и векторные данные", "resetConfig": "Сбросить настройки", "resetConfigDesc": "Удалить все настройки приложения, конфигурации моделей и пользовательские промпты", "resetAll": "Полный сброс", "resetAllDesc": "Удалить все данные, включая историю чатов, настройки и кэш-файлы", "resetting": "Сброс...", "confirmReset": "Подтвердить сброс", "resetCompleteDevTitle": "Сброс данных завершен", "resetCompleteDevMessage": "В среде разработки перезапустите приложение вручную. Остановите текущий процесс и выполните pnpm run dev снова"}, "model": {"title": "Настройки модели", "systemPrompt": {"label": "Системный запрос", "placeholder": "Введите системный запрос...", "description": "Настройте системный запрос AI помощника для определения его поведения и роли"}, "temperature": {"label": "Температура модели", "description": "Контролирует случайность вывода, более высокие значения создают более креативные ответы"}, "contextLength": {"label": "<PERSON><PERSON><PERSON><PERSON> контекста", "description": "Установите максимальную длину контекста разговора"}, "responseLength": {"label": "<PERSON><PERSON><PERSON>на ответа", "description": "Установите максимальную длину ответа AI"}, "artifacts": {"description": "Включение функции артефактов позволяет AI генерировать более богатый контент", "title": "Эффекты артефактов"}, "addModel": "Добавить модель", "configureModel": "Модель конфигурации", "modelList": "Список моделей", "provider": "Поставщик услуг", "providerSetting": "Настройки поставщика услуг", "selectModel": "Выберите модель", "visionModel": "Модель зрения", "visionModelDesc": "Выберите модель для обработки изображений и визуального контента, используемую для описания изображений, анализа и других функций", "visionModelNotSelectedWarning": "Модель зрения не выбрана. При использовании функций изображений вам будет предложено настроить её", "modelConfig": {"cancel": "Отмена", "contextLength": {"description": "Установите длину контекста, с которой модель может обработать", "label": "Контекст длины"}, "description": "Обратите внимание, что эта конфигурация действительна только для текущей модели и не будет влиять на другие модели. Пожалуйста, измените его с осторожностью. Неправильные параметры могут привести к тому, что модель не работает должным образом.", "functionCall": {"description": "Независимо от того, поддерживает ли модель вызовы функций назначать (DeepChat автоматически смоделирует вызовы функций после отключения этой опции)", "label": "Функциональные вызовы"}, "maxTokens": {"description": "Установите максимальное количество токенов для одного выхода модели", "label": "Максимальная длина выходного сигнала"}, "reasoning": {"description": "Поддерживает ли модель способность рассуждать?", "label": "Способность рассуждать"}, "thinkingBudget": {"label": "Бюджет Размышлений", "description": "Ограничить длину размышлений модели", "dynamic": "Динамические Размышления", "range": "Диапазон: {min} - {max}", "onlySupported": "Поддерживается только в Gemini 2.5 Flash, 2.5 Pro и 2.5 Flash-Lite", "valueLabel": "Значение Бюджета Размышлений", "placeholder": "Введите значение бюджета размышлений", "forceEnabled": "Модели серии Gemini 2.5 принудительно включают бюджет размышлений", "dynamicPrefix": "-1 = Динамические Размышления", "notice": "Внимание: ", "warnings": {"proNoDisable": "Эта модель не поддерживает отключение размышлений, минимальное значение 128", "proCannotDisable": "Gemini 2.5 Pro не может отключить функцию размышлений", "flashLiteCannotSetZero": "Gemini 2.5 Flash-Lite нельзя установить в 0, минимальное значение 512", "modelCannotDisable": "Эта модель не может отключить функцию размышлений", "flashLiteMinValue": "Бюджет размышлений не может быть меньше 512 при установке конкретного значения (или используйте 0 для отключения размышлений, -1 для включения динамических размышлений)", "belowMin": "Бюджет размышлений не может быть меньше {min}{hint}", "aboveMax": "Бюджет размышлений не может быть больше {max}"}, "hints": {"flashLiteDisable": ", 0 = отключить размышления, конкретное значение минимум 512", "normalDisable": ", 0 = отключить размышления", "withZeroAndDynamic": "(или используйте 0 для отключения размышлений, -1 для включения динамических размышлений)", "withDynamic": "(или используйте -1 для включения динамических размышлений)"}}, "resetConfirm": {"confirm": "Подтвердите сброс", "message": "Вы уверены, что хотите сбросить конфигурацию этой модели по умолчанию? Эта операция безвозврата.", "title": "Подтвердите сброс"}, "reasoningEffort": {"label": "Усилие рассуждения", "description": "Контролирует глубину рассуждения модели; более высокое усилие дает лучшие результаты, но более медленные ответы", "placeholder": "Выберите усилие рассуждения", "options": {"minimal": "Minimal - Самый быстрый ответ", "low": "Low - Низкое усилие", "medium": "Medium - Умеренное усилие", "high": "High - Высокое усилие"}}, "verbosity": {"label": "Подробность", "description": "Контролирует уровень детализации и длину ответов модели", "placeholder": "Выберите уровень подробности", "options": {"low": "Low - Краткие ответы", "medium": "Medium - Сбалансированная детализация", "high": "High - Подробные ответы"}}, "resetToDefault": "Сбросить по умолчанию", "saveConfig": "Сохранить конфигурацию", "useModelDefault": "Использовать настройки модели по умолчанию", "currentUsingModelDefault": "В настоящее время используются настройки модели по умолчанию", "temperature": {"description": "Управляйте случайностью выхода. Большинство моделей 0-1, а некоторая поддержка между 0-2. Чем выше, тем выше случайность.", "label": "температура"}, "title": "Пользовательские параметры модели", "type": {"description": "Выберите тип модели", "label": "Тип модели", "options": {"chat": "Языковая модель", "embedding": "Встроенная модель", "imageGeneration": "Модель генерации изображений", "rerank": "Переупорядочить модель"}}, "validation": {"contextLengthMax": "<PERSON><PERSON><PERSON><PERSON> контекста не может превышать 10000000", "contextLengthMin": "Длина контекста должна быть больше 0", "contextLengthRequired": "<PERSON><PERSON><PERSON><PERSON> контекста не может быть пустой", "maxTokensMax": "Максимальная длина выходной сигналы не может превышать 1000000", "maxTokensMin": "Максимальная длина выходной сигналы должна быть больше 0", "maxTokensRequired": "Максимальная длина выходной подачи не может быть пустой", "temperatureMax": "Температура должна быть меньше или равна 2", "temperatureMin": "Температура должна быть больше или равна 0", "temperatureRequired": "Температура не может быть пустой"}, "vision": {"description": "Поддерживает ли модель визуальные способности?", "label": "Визуальная способность"}}}, "provider": {"search": "Поиск платформ провайдеров…", "enable": "Включить сервис", "enabled": "Включен", "disabled": "Отключен", "urlPlaceholder": "Введите API URL", "keyPlaceholder": "Введите API Key", "accessKeyIdPlaceholder": "Введите AWS Access Key ID", "secretAccessKeyPlaceholder": "Введите AWS Secret Access Key", "regionPlaceholder": "Введите регион AWS", "verifyKey": "Проверить ключ", "howToGet": "Как получить", "getKeyTip": "Перейдите по следующему адресу", "getKeyTipEnd": "Получите API Key", "modelList": "Список моделей", "enableModels": "Включить модели", "disableAllModels": "Отключить все модели", "modelsEnabled": "Модели включены", "noModelsEnabled": {"title": "Нет активных моделей", "description": "Нажмите кнопку «Включить модели», чтобы вручную выбрать модели для использования."}, "verifyLink": "Проверить ссылку", "syncModelsFailed": "Не удалось синхронизировать модели...", "addCustomProvider": "Добавить пользовательский провайдер", "delete": "Удалить", "stopModel": "Остановить модель", "pulling": "Скачивание...", "runModel": "Запустить модель", "dialog": {"disableModel": {"title": "Подтвердите отключение модели", "content": "Вы уверены, что хотите отключить модель \"{name}\"?", "confirm": "Отключить"}, "configModels": {"title": "Настройка списка моделей", "description": "Выберите модели для включения или отключения"}, "disableAllModels": {"title": "Подтвердите отключение всех моделей", "content": "Вы уверены, что хотите отключить все модели для \"{name}\"?", "confirm": "Отключить все"}, "verify": {"missingFields": "Пожалуйста, введите API Key и API URL", "failed": "Проверка не удалась", "success": "Проверка успешна", "failedDesc": "Проверка API ключа или конфигурации не удалась, проверьте настройки", "successDesc": "API ключ и конфигурация успешно проверены, готовы к использованию", "connectionError": "Ошибка соединения, пожалуйста, проверьте сетевое соединение и адрес API", "serverError": "Ошибка сервера, попробуйте еще раз позже", "unauthorized": "Аутентификация не удалась, ключ API является недействительным или истекшим"}, "addCustomProvider": {"title": "Добавить пользовательский провайдер", "description": "Пожалуйста, заполните необходимую информацию для провайдера", "name": "Имя", "namePlaceholder": "Введите имя провайдера", "apiType": "Тип <PERSON>", "apiTypePlaceholder": "Выберите тип API", "apiKey": "API Key", "apiKeyPlaceholder": "Введите API Key", "baseUrl": "API URL", "baseUrlPlaceholder": "Введите API URL", "enable": "Включить провайдер"}, "deleteProvider": {"title": "Подтверждение удаления провайдера", "content": "Вы уверены, что хотите удалить провайдера \"{name}\"? Это действие нельзя отменить.", "confirm": "Удалить"}, "deleteModel": {"title": "Подтверждение удаления модели", "content": "Вы уверены, что хотите удалить модель \"{name}\"? Это действие нельзя отменить.", "confirm": "Удалить"}, "pullModel": {"title": "Скачивание модели", "description": "Выберите модели для загрузки локально", "pull": "Скачать"}, "modelCheck": {"checking": "Тестирование ...", "description": "Выберите модель для подключения и удобства использования", "failed": "Тест модели не удался", "model": "Выберите модель", "modelPlaceholder": "Пожалуйста, выберите модель для тестирования", "noModels": "Для этого поставщика услуг нет модели", "success": "Модельный тест преуспел", "test": "Начните тест", "title": "Модель проверка"}}, "pullModels": "Скачать модели", "refreshModels": "Обновить модели", "modelsRunning": "Модели запущены", "runningModels": "Запущенные модели", "noRunningModels": "Нет запущенных моделей", "deleteModel": "Удалить модель", "deleteModelConfirm": "Вы уверены, что хотите удалить модель \"{name}\"? Это действие нельзя отменить.", "noLocalModels": "Нет локальных моделей", "localModels": "Локальные модели", "urlFormat": "Образец API: {defaultUrl}", "azureApiVersion": "Версия API", "safety": {"title": "Настройки безопасности", "blockHighest": "Блок высокий риск", "blockMost": "Блок средних рисков", "blockNone": "Не заблокирован", "blockSome": "Блок низкий риск"}, "serverList": "Список серверов", "totalServers": "Всего серверов", "addServer": "Добавить сервер", "autoStart": "Самостоятельно", "githubCopilotAuth": "Аутентификация GitHub Copilot", "githubCopilotConnected": "GitHub Copilot подключен", "githubCopilotNotConnected": "GitHub Copilot не подключен", "loginWithGitHub": "Войти через GitHub", "loggingIn": "Вход...", "githubCopilotLoginTip": "Разрешите DeepChat доступ к вашей подписке GitHub Copilot. Для доступа к API Copilot требуются разрешения 'read:user' и 'read:org'.", "loginSuccess": "Вход выполнен успешно", "loginFailed": "Ошибка входа", "tokenValid": "Токен действителен", "tokenInvalid": "Токен недействителен", "disconnect": "Отключить", "disconnected": "Отключено успешно", "disconnectFailed": "Не удалось отключить", "keyStatus": {"remaining": "Оставшаяся сумма", "usage": "Использовал"}, "refreshingModels": "Освежает ...", "toast": {"modelRunning": "Модель работает", "modelRunningDesc": "Пожалуйста, остановите модель {model} сначала, а затем удалите ее."}, "anthropicApiKeyTip": "Пожалуйста, перейдите на антропную консоль, чтобы получить ключ API", "anthropicConnected": "Антропическая связь", "anthropicNotConnected": "Антропический не связан", "anthropicOAuthTip": "Нажмите «Авторизировать» DeepChat для доступа к вашей антропной учетной записи", "oauthLogin": "Оаут логин", "authMethod": "Метод сертификации", "authMethodPlaceholder": "Выберите метод аутентификации", "apiKeyLabel": "<PERSON> ключ", "apiUrlLabel": "API URL", "anthropicOAuthFlowTip": "Система автоматически откроет окно авторизации. Пожалуйста, вернитесь и введите код авторизации после авторизации.", "anthropicBrowserOpened": "Внешний браузер открыт", "anthropicCodeInstruction": "Пожалуйста, заполните авторизацию во внешнем браузере и вставьте полученный код авторизации в поле ввода ниже", "browserOpenedSuccess": "Внешний браузер открыт, пожалуйста, заполните авторизацию", "codeRequired": "Пожалуйста, введите код авторизации", "inputOAuthCode": "Введите код авторизации", "codeExchangeFailed": "Обмен кодом авторизации не удалось", "invalidCode": "Неверный код авторизации", "oauthCodeHint": "Пожалуйста, вставьте здесь код авторизации после завершения авторизации во внешнем браузере", "oauthCodePlaceholder": "Пожалуйста, введите код авторизации ...", "verifyConnection": "Проверить соединение", "manageModels": "Управление моделями", "anthropicOAuthActiveTip": "Аутентификация OAuth включена, вы можете использовать сервисы Anthropic напрямую", "oauthVerifySuccess": "Соединение OAuth успешно проверено", "oauthVerifyFailed": "Ошибка проверки соединения OAuth", "configurationSaved": "Конфигурация сохранена", "configurationUpdated": "Конфигурация обновлена", "dataRefreshed": "Данные были обновлены", "modelscope": {"apiKey": "API -к<PERSON><PERSON><PERSON>", "apiKeyHelper": "Получите ключ API в консоли моделей", "apiKeyPlaceholder": "Пожалуйста, введите ключ API моделей моделей", "baseUrl": "Адрес API", "baseUrlHelper": "ModelsCope API -адрес службы службы", "connected": "Подключенный", "connecting": "Соединение ...", "description": "ModelCope-это платформа для обмена моделями, запущенная Alibaba Damo Academy", "details": {"apiConfig": "Конфигурация API", "mcpSync": "Синхронизация MCP", "modelManagement": "Управление моделями", "operationalDescription": "Синхронизировать серверы MCP, которые можно использовать непосредственно на платформе моделей", "operationalServers": "Эксплуатация сервера", "rateLimitConfig": "Конфигурация ограничения скорости", "safetySettings": "Настройки безопасности", "specialConfig": "Специальная конфигурация", "syncFromModelScope": "Синхронизация от моделей", "title": "Детали настроек поставщика"}, "invalidKey": "Неверный ключ API", "keyRequired": "Пожалуйста, введите ключ API", "mcpSync": {"authenticationFailed": "Аутентификация не удалась, пожалуйста, проверьте ключ API", "convertingServers": "Преобразование конфигурации сервера ...", "description": "Синхронизация сервера MCP от моделей к локальным конфигурациям позволяет быстро добавлять обычно используемые инструменты MCP. Все службы отключены по умолчанию и могут быть включены вручную после импорта.", "errorDetails": "Детали ошибки", "errors": "Ошиб<PERSON><PERSON> {count}", "fetchingServers": "Получение списка серверов MCP ...", "imported": "{count} Услуги были импортированы", "importingServers": "Конфигурация импорта сервера ...", "invalidServerData": "Неверные данные сервера", "noApiKey": "Сначала настройте ключ API моделей ModelsCope", "noOperationalUrls": "Не найдено доступного операционного адреса", "noServersFound": "Не найдена доступной услуги MCP", "pageNumber": "номер страницы", "pageNumberPlaceholder": "Пожалуйста, введите номер страницы", "pageSize": "Количество на страницу", "serverAlreadyExists": "Сервер уже существует, пропустите импорт", "skipped": "Skip {count} Сервисы", "sync": "Начните синхронизировать", "syncComplete": "Синхронное завершение", "syncing": "Синхронизация ...", "title": "Синхронизировать услуги MCP"}, "name": "Моделикоп", "networkError": "Ошибка сетевого соединения", "notConnected": "Не подключен", "verifyFailed": "Проверка не удалась", "verifySuccess": "Проверка успешна"}, "operationFailed": "Операция не удалась", "operationSuccess": "Операция успешна", "settingsApplied": "Применяются настройки", "bedrockLimitTip": "* Поддерживает только Anthropic <PERSON> (включая модели Opus, Sonnet, Haiku)", "bedrockVerifyTip": "DeepChat использует модель Claude 3.5 Sonnet для верификации. Если у вас нет разрешения на её использование, верификация не пройдёт. Это не повлияет на использование других моделей."}, "knowledgeBase": {"title": "Настройки базы знаний", "addKnowledgeBase": "Добавить базу знаний", "selectKnowledgeBaseType": "Пожалуйста, выберите тип базы знаний для добавления", "difyDescription": "База знаний Dify помогает управлять и использовать данные документов", "comingSoon": "Скоро", "featureNotAvailable": "Эта функция пока недоступна", "addDifyConfig": "Добавить конфигурацию Dify", "apiKey": "<PERSON> ключ", "datasetId": "ID набора данных", "endpoint": "API эндпоинт", "configAdded": "Конфигурация добавлена", "configAddedDesc": "{name} конфигурация была успешно добавлена", "addConfig": "Добавить конфигурацию", "moreComingSoon": "Больше типов баз знаний появится в ближайшее время", "configUpdated": "Конфигурация обновлена", "configUpdatedDesc": "{name} конфигурация была успешно обновлена", "descriptionPlaceholder": "Например: база знаний документации продуктов компании", "ragflowTitle": "База знаний RAGFlow", "ragflowDescription": "RAGFlow - это мощная система управления базами знаний, поддерживающая множество методов поиска и функций управления документами.", "addRagflowConfig": "Добавить конфигурацию RAGFlow", "editRagflowConfig": "Редактировать конфигурацию RAGFlow", "dify": "База знаний DIFY", "editDifyConfig": "Изменить конфигурацию DIFY", "fastgptTitle": "База знаний FastGPT", "fastgptDescription": "FastGPT - это мощная система управления базами знаний, поддерживающая несколько методов поиска и функции управления документами.", "addFastGptConfig": "Добавить конфигурацию FastGPT", "editFastGptConfig": "Редактировать конфигурацию FastGPT", "builtInKnowledgeDescription": "Встроенная база знаний предоставляет некоторые простые реализации, которые позволяют некоторые основные функции в автономной среде.", "builtInKnowledgeTitle": "Встроенная база знаний", "addBuiltinKnowledgeConfig": "Добавить встроенную конфигурацию базы знаний", "editBuiltinKnowledgeConfig": "Редактировать встроенную конфигурацию базы знаний", "chunkSize": "Размер блока", "chunkSizeHelper": "Разрежьте документ на сегменты, размер каждого сегмента не может превышать предел контекста модели", "chunkOverlap": "Перекрывающийся размер", "chunkOverlapHelper": "Количество повторного содержания между соседними текстовыми блоками гарантирует, что между сегментированными текстовыми блоками все еще существует контекстуальная связь, улучшая общий эффект обработки модели длинных текстов", "selectEmbeddingModel": "Выберите модель встраивания", "modelNotFound": "Поставщик услуг {provider} или модель {model} не найден", "modelNotFoundDesc": "Убедитесь, что модель настроена правильно, и что модель включена. Вы можете проверить конфигурацию модели в настройках поставщика услуг.", "removeBuiltinKnowledgeConfirmDesc": "Удаление встроенной конфигурации базы знаний удалит все соответствующие данные и не может быть восстановлено. Пожалуйста, будьте осторожны.", "removeBuiltinKnowledgeConfirmTitle": "Подтвердите, чтобы удалить встроенную базу знаний {name}?", "descriptionDesc": "Описание базы знаний, чтобы ИИ решал, получить ли эта база знаний", "advanced": "Расширенные варианты", "autoDetectDimensions": "Автоматически обнаруживает встроенные размеры", "autoDetectHelper": "Автоматически обнаруживает встроенные размеры, потребляет небольшое количество токенов", "chunkOverlapPlaceholder": "Значение по умолчанию, изменение не рекомендуется", "chunkSizePlaceholder": "Значение по умолчанию, изменение не рекомендуется", "dimensions": "Встроенные размеры", "dimensionsPlaceholder": "Встроенный размер измерения, такой как 1024", "selectEmbeddingModelHelper": "Встроенные модели запрещены после создания базы знаний", "dimensionsHelper": "Убедитесь, что модель поддерживает размер встроенного размера", "autoDetectDimensionsError": "Автоматически обнаружение встроенного сбоя измерения", "fragmentsNumber": "Количество запрошенных фрагментов документа", "fragmentsNumberHelper": "Чем больше фрагментов запрашиваемого документа, тем больше информации он поступает, но чем больше токенов, которые он должен потреблять", "selectRerankModel": "Выберите модель повторного заказа", "rerankModel": "Переупорядочить модель", "embeddingModel": "Встроенная модель", "return": "возвращаться", "uploadHelper": "Нажмите, чтобы загрузить или перетащить файл здесь", "fileSupport": "Поддержка {accept} и {count} других форматов", "searchKnowledge": "Поиск базы знаний", "searchKnowledgePlaceholder": "Пожалуйста, введите содержание запроса", "noData": "Нет данных пока", "file": "документ", "uploadProcessing": "Загрузка", "uploadCompleted": "Загрузка завершена", "reAdd": "Повторная загрузка", "uploadError": "Загрузка не удалась", "delete": "удалить", "reason": "причина", "deleteSuccess": "Удалить успешно", "copy": "копия", "copySuccess": "Копировать успешно", "source": "источник", "normalized": "L2 Нормализация", "normalizedHelper": "Убедитесь, что модель поддерживает нормализацию выходных векторов L2", "dialog": {"beforequit": {"cancel": "Отмена", "confirm": "подтверждать", "title": "Выход подтверждения", "description": "Существует задача базы знаний. Вы обязательно выйдете из программного обеспечения? Аренданые задачи могут быть восстановлены после перезапуска программного обеспечения."}}, "searchError": "Запрос не удался", "processing": "Загрузка", "paused": "Загрузить паузу", "unknown": "Неизвестный статус", "reAddFile": {"title": "Повторная загрузка подтверждения", "content": "Вы уверены, что повторно загрузите файл \"{filename}\"?"}, "deleteFile": {"title": "Удалить подтверждение файла", "content": "Вы обязательно удалите файл \"{filename}\"? Эта операция не восстановлена."}, "resumeAllPausedTasks": "Восстановление одного клика", "pauseAllRunningTasks": "Пауза с одним щелчком", "separators": "Сепаратор блока", "separatorsHelper": "Разделитель сегментации документа, одиночный разделитель заключается в двойные кавычки (\"\"), а разделители разделяются запятыми (,)", "invalidSeparators": "Неверный сепаратор", "selectLanguage": "Выберите предустановку", "separatorsPreset": "Загрузка пресетов", "promptManagement": {"title": "Управление промптами", "description": "Управление и использование пользовательских шаблонов промптов для лучшего взаимодействия с ИИ"}}, "mcp": {"title": "Настройки MCP", "description": "Управление и настройка серверов и инструментов MCP (Model Context Protocol)", "enabledTitle": "Включить MCP", "enabledDescription": "Включение или отключение функций и инструментов MCP", "enableToAccess": "Пожалуйста, включите MCP для доступа к настройкам", "tabs": {"servers": "Серверы", "tools": "Инструменты", "prompts": "Prompts", "resources": "ресурс"}, "serverList": "Список серверов", "totalServers": "Всего серверов", "addServer": "Добавить сервер", "running": "Запущен", "stopped": "Остановлен", "stopServer": "Остановить сервер", "startServer": "Запустить сервер", "noServersFound": "Серверы не найдены", "addServerDialog": {"title": "Добавить сервер", "description": "Настройка нового сервера MCP"}, "editServerDialog": {"title": "Редактировать сервер", "description": "Редактирование конфигурации сервера MCP"}, "serverForm": {"name": "Имя сервера", "namePlaceholder": "Введите имя сервера", "nameRequired": "Имя сервера обязательно", "type": "Тип сервера", "typePlaceholder": "Выберите тип сервера", "typeStdio": "Стандартный вход и выход (Stdio)", "typeSse": "Сервер отправлять события (SSE)", "typeInMemory": "In-Memory", "baseUrl": "Базовый URL", "baseUrlPlaceholder": "Введите базовый URL сервера (например, http://localhost:3000)", "command": "Команда", "commandPlaceholder": "Введите команду", "commandRequired": "Команда не может быть пустой", "args": "Параметры", "argsPlaceholder": "Введите параметры, разделенные пробелами", "argsRequired": "Параметры не могут быть пустыми", "env": "Переменные окружения", "envPlaceholder": "Введите переменные окружения в формате JSON", "envInvalid": "Переменные окружения должны быть в формате JSON", "description": "Описание", "descriptionPlaceholder": "Введите описание сервера", "descriptions": "Описание", "descriptionsPlaceholder": "Введите описание сервера", "icon": "Иконка", "iconPlaceholder": "Введите иконку", "icons": "Иконки", "iconsPlaceholder": "Введите иконки", "autoApprove": "Автоматический допуск", "autoApproveAll": "Все", "autoApproveRead": "Чтение", "autoApproveWrite": "Запись", "autoApproveHelp": "Выберите тип операции, которая будет автоматически разрешена без подтверждения пользователя", "submit": "Отправить", "add": "Добавить", "update": "Обновить", "cancel": "Отменить", "jsonConfigIntro": "Вы можете вставить JSON-конфигурацию напрямую или выбрать ручную настройку сервера.", "jsonConfig": "JSON-конфигурация", "jsonConfigPlaceholder": "Вставьте конфигурацию MCP-сервера в формате JSON", "jsonConfigExample": "Пример J<PERSON>-конфигурации", "parseSuccess": "Конфигурация успешно обработана", "configImported": "Конфигурация успешно импортирована", "parseError": "Ошибка обработки", "skipToManual": "Перейти к ручной настройке", "parseAndContinue": "Обработать и продолжить", "jsonParseError": "JSON SAINING не удалось", "typeHttp": "Потоковые http -запросы (http)", "browseMarketplace": "Просмотреть рынок услуг MCP", "imageModel": "Выберите визуальную модель", "customHeadersParseError": "Пользовательский диапазон заголовка не удалось", "customHeaders": "Пользовательский заголовок запроса", "invalidKeyValueFormat": "Неправильный формат заголовка запроса, пожалуйста, проверьте, является ли вход правильным.", "npmRegistry": "Пользовательский реестр NPM", "npmRegistryPlaceholder": "Установите пользовательский реестр NPM, оставьте систему, чтобы автоматически выбрать самый быстрый", "browseHigress": "Посмотреть Higress MCP Marketplace", "selectFolderError": "Ошибка выбора папки", "folders": "Папки разрешены доступа", "addFolder": "Добавить папку", "noFoldersSelected": "Не было выбрано папки", "useE2B": "Включить песочницу E2B", "e2bDescription": "Выполните код Python с помощью песочницы E2B", "e2bApiKey": "E2b apikey", "e2bApiKeyPlaceholder": "Введите здесь свои клавиши E2B API, такие как E2B_1111XX *******", "e2bApiKeyHelp": "Перейдите в E2B.DEV, чтобы получить свой Apikey", "e2bApiKeyRequired": "Apikey должен быть введен, чтобы включить функцию E2B", "clickToEdit": "Нажмите «Редактировать», чтобы просмотреть полный контент"}, "deleteServer": "Удалить сервер", "editServer": "Редактировать сервер", "setDefault": "Установить по умолчанию", "removeDefault": "Удалить по умолчанию", "isDefault": "Сервер по умолчанию", "default": "По умолчанию", "setAsDefault": "Установить по умолчанию", "removeServer": "Удалить сервер", "autoStart": "Автозапуск", "confirmRemoveServer": "Вы уверены, что хотите удалить сервер {name}? Это действие нельзя отменить.", "removeServerDialog": {"title": "Удалить сервер"}, "confirmDelete": {"title": "Подтвердите удаление", "description": "Вы уверены, что хотите удалить сервер {name}? Это действие нельзя отменить.", "confirm": "Удалить", "cancel": "Отмена"}, "resetToDefault": "Восстановить по умолчанию", "resetConfirmTitle": "Восстановить серверы по умолчанию", "resetConfirmDescription": "Это восстановит все серверы по умолчанию, сохраняя при этом ваши пользовательские серверы. Любые изменения серверов по умолчанию будут потеряны.", "resetConfirm": "Восстановить", "builtInServers": "Встроенные серверы", "customServers": "Пользовательские серверы", "builtIn": "Встроенный", "cannotRemoveBuiltIn": "Невозможно удалить встроенный сервер", "builtInServerCannotBeRemoved": "Встроенные серверы нельзя удалить, можно изменять только параметры и переменные окружения", "marketplace": "Перейдите на MCP Market и установите его одним щелчком", "maxDefaultServersReached": "Только 3 сервера по умолчанию могут быть установлены по максимум", "removeDefaultFirst": "Сначала удалите несколько серверов по умолчанию", "higressMarket": "Перейти к установке Higress MCP", "npmRegistry": {"title": "Настройка источников NPM", "currentSource": "Текущий источник", "cached": "Кэшировано", "lastChecked": "Последняя проверка", "refresh": "Обновить", "advanced": "Дополнительно", "advancedSettings": "Дополнительные настройки", "advancedSettingsDesc": "Настройка дополнительных параметров источников NPM, включая автоматическое обнаружение и пользовательские настройки источника", "autoDetect": "Автоматически определять оптимальный источник", "autoDetectDesc": "Автоматически определять и использовать самый быстрый источник NPM при запуске", "customSource": "Пользовательский источник", "customSourcePlaceholder": "Введите адрес пользовательского источника NPM", "currentCustom": "Текущий пользовательский источник", "justNow": "Только что", "minutesAgo": "{minutes} минут назад", "hoursAgo": "{hours} часов назад", "daysAgo": "{days} дней назад", "refreshSuccess": "Обновление источника NPM успешно", "refreshSuccessDesc": "Оптимальный источник NPM переопределен и обновлен", "refreshFailed": "Ошибка обновления источника NPM", "autoDetectUpdated": "Настройки автоматического обнаружения обновлены", "autoDetectEnabled": "Автоматическое обнаружение оптимального источника NPM включено", "autoDetectDisabled": "Автоматическое обнаружение отключено, будет использоваться источник по умолчанию", "updateFailed": "Ошибка обновления настроек", "customSourceSet": "Пользовательский источник установлен", "customSourceSetDesc": "Пользовательский источник NPM установлен: {registry}", "customSourceCleared": "Пользовательский источник очищен", "customSourceClearedDesc": "Пользовательский источник NPM очищен, будет использоваться автоматическое обнаружение", "invalidUrl": "Недействительный URL", "invalidUrlDesc": "Пожалуйста, введите действительный HTTP или HTTPS адрес", "testing": "Тестирование источника NPM", "testingDesc": "Тестирование подключения к источнику {registry}...", "testFailed": "Ошибка тестирования источника NPM", "testFailedDesc": "Невозможно подключиться к {registry}, ошибка: {error}. Пожалуйста, проверьте правильность URL или сетевое подключение.", "redetectingOptimal": "Переопределение оптимального источника NPM...", "redetectComplete": "Переопределение завершено", "redetectCompleteDesc": "Текущий оптимальный источник NPM определен и настроен", "redetectFailed": "Ошибка переопределения", "redetectFailedDesc": "Невозможно переопределить оптимальный источник, будет использоваться конфигурация по умолчанию"}, "technicalDetails": "Технические детали", "httpServer": "HTTP сервер", "localProcess": "Локальный процесс", "restartServer": "Перезапустить сервер", "viewLogs": "Просмотр логов", "starting": "Запуск", "error": "Ошибка"}, "about": {"title": "О нас", "version": "Версия", "checkUpdate": "Проверить обновления", "checking": "Проверка...", "latestVersion": "Последняя версия"}, "display": {"fontSize": "Размер текста", "text-2xl": "Чрезвычайно большой", "text-base": "Маленький", "text-lg": "по умолчанию", "text-sm": "Чрезвычайно маленький", "text-xl": "большой", "floatingButton": "Плавающая кнопка", "floatingButtonDesc": "Отображать плавающую кнопку на рабочем столе для быстрого активации окна приложения"}, "shortcuts": {"title": "Настройки сочетаний клавиш", "pressKeys": "Нажмите клавиши", "pressEnterToSave": "Нажмите Enter для сохранения, Esc для отмены", "noModifierOnly": "Нельзя использовать только модификатор в качестве сочетания", "keyConflict": "Конфликт сочетаний клавиш, выберите другую комбинацию", "clearShortcut": "Очистить сочетание клавиш", "cleanHistory": "Четкая история чата", "deleteConversation": "Удалить разговор", "goSettings": "Откройте настройки", "hideWindow": "Скрыть окно", "quitApp": "Отказаться от приложения", "zoomIn": "Увеличить", "zoomOut": "Увеличить", "zoomReset": "Сбросить Zoom", "closeTab": "Закройте текущую страницу вкладки", "newTab": "Создать новую вкладку", "newWindow": "Откройте новое окно", "showHideWindow": "Показать/Скрыть окно", "newConversation": "Новый разговор", "lastTab": "Переключиться на последнюю вкладку", "previousTab": "Переключиться на предыдущую вкладку", "specificTab": "Переключитесь на указанную вкладку (1-8)", "nextTab": "Переключиться на следующую вкладку"}, "rateLimit": {"title": "Ограничение скорости", "description": "Контроль временного интервала запросов, предотвращение превышения лимитов API", "intervalLimit": "Интерва<PERSON> запро<PERSON>ов", "intervalUnit": "секунд", "intervalHelper": "Минимальный интервал между двумя запросами, отключите ограничение скорости если не нужно", "lastRequestTime": "Последний запрос", "queueLength": "<PERSON><PERSON><PERSON>на очереди", "nextAllowedTime": "Следующий разрешенный запрос", "never": "Никогда", "justNow": "Только что", "secondsAgo": "секунд назад", "minutesAgo": "минут назад", "immediately": "Немедленно", "secondsLater": "секунд позже", "confirmDisableTitle": "Подтвердить отключение ограничения скорости", "confirmDisableMessage": "Значение не может быть меньше или равно 0. Отключить ограничение скорости?", "confirmDisable": "Отключить ограничение", "disabled": "Ограничение скорости отключено", "disabledDescription": "Ограничение скорости было отключено"}}