{"title": "設定", "common": {"title": "一般設定", "modelSettings": "模型設定", "searchSettings": "搜尋設定", "networkSettings": "網路設定", "interfaceSettings": "介面與互動", "personalizationSettings": "個人化設定", "securitySettings": "安全與隱私", "operationSettings": "操作設定", "resetData": "重設資料", "language": "語言", "languageSelect": "選擇語言", "searchEngine": "搜尋引擎", "searchEngineDesc": "選擇搜尋引擎用於網路搜尋增強，支持谷歌、Bing等主流搜尋引擎，也可新增自訂搜尋引擎", "searchEngineSelect": "選擇搜尋引擎", "searchPreview": "搜尋預覽", "searchPreviewDesc": "開啟後在搜尋結果頁面顯示預覽圖，可快速瀏覽搜尋結果內容", "searchAssistantModel": "助手模型", "searchAssistantModelDesc": "選擇用於處理搜尋結果並生成回答的模型，影響搜尋增強的品質和速度", "selectModel": "選擇模型", "visionModel": "視覺模型", "visionModelDesc": "選擇用於處理圖像和視覺內容的模型，用於圖像描述、分析等功能", "visionModelNotSelectedWarning": "未選擇視覺模型，使用圖像功能時將提示您先進行設定", "proxyMode": "代理伺服器模式", "proxyModeDesc": "設定網路代理模式，可選擇系統代理、不使用代理或自訂代理，用於存取國外服務", "proxyModeSelect": "選擇代理伺服器模式", "proxyModeSystem": "系統代理伺服器", "proxyModeNone": "不使用代理伺服器", "proxyModeCustom": "自訂代理伺服器", "customProxyUrl": "自訂代理伺服器位址", "customProxyUrlPlaceholder": "例如：http://127.0.0.1:7890", "invalidProxyUrl": "無效的代理伺服器位址，請輸入有效的 http/https URL", "addCustomSearchEngine": "新增自訂搜尋引擎", "addCustomSearchEngineDesc": "新增一個新的搜尋引擎，需要提供名稱和搜尋 URL。URL 中必須包含 {query} 作為查詢預留位置。", "searchEngineName": "搜尋引擎名稱", "searchEngineNamePlaceholder": "請輸入搜尋引擎名稱", "searchEngineUrl": "搜尋 URL", "searchEngineUrlPlaceholder": "如：https://a.com/search?q={query}", "searchEngineUrlError": "URL 必須包含 {query} 作為查詢預留位置", "deleteCustomSearchEngine": "刪除自訂搜尋引擎", "deleteCustomSearchEngineDesc": "確定要刪除自訂搜尋引擎「{name}」嗎？此操作無法復原。", "testSearchEngine": "測試搜尋引擎", "testSearchEngineDesc": "即將使用 {engine} 搜尋引擎進行測試搜尋，將搜尋關鍵字「天氣」。", "testSearchEngineNote": "如果搜尋頁面需要登入或其他操作，您可以在測試視窗中進行。完成測試後請關閉測試視窗。", "theme": "主題", "themeSelect": "選擇主題", "closeToQuit": "點選關閉按鈕時結束程式", "closeToQuitDesc": "選擇點選關閉按鈕時是結束程式還是隱藏到系統匣，隱藏後可從匣中恢復", "contentProtection": "畫面保護", "contentProtectionDialogTitle": "畫面保護切換確認", "contentProtectionEnableDesc": "開啟畫面保護可以防止錄影軟體擷取 DeepChat 主視窗，用來保護您的內容隱私。請注意，此功能不會徹底隱藏所有介面，請合理合規使用。並且，並不是所有錄影軟體都遵守使用者隱私設定，該功能可能會在一些不遵守隱私設定的錄影軟體上失效，某些環境中可能會殘留一個黑色視窗。", "contentProtectionDisableDesc": "關閉畫面保護將允許錄影軟體擷取 DeepChat 視窗。", "contentProtectionRestartNotice": "切換此設定將會重新啟動應用程式，請問您是否要繼續？", "soundEnabled": "啟用音效", "soundEnabledDesc": "啟用訊息接收、錯誤提示等系統音效，提供更好的交互體驗", "copyWithCotEnabled": "複製COT資訊", "copyWithCotEnabledDesc": "複製訊息時包含思維鏈（Chain of Thought）資訊，方便理解AI的推理過程", "loggingEnabled": "啟用日誌", "loggingEnabledDesc": "啟用日誌記錄可幫助診斷問題和改進應用程式，但可能包含敏感資訊，需要重新啟動生效", "devToolsAutoOpen": "自動打開開發者工具", "devToolsAutoOpenDesc": "在建立視窗和標籤頁時自動打開開發者工具，方便除錯和開發", "loggingDialogTitle": "確認日誌設定變更", "loggingEnableDesc": "啟用日誌將幫助我們診斷問題並改進應用程式。日誌檔案可能包含敏感資訊。", "loggingDisableDesc": "停用日誌將停止收集應用程式日誌。", "loggingRestartNotice": "切換此設定將會重新啟動應用程式，請問您是否要繼續？", "devToolsDialogTitle": "確認開發者工具設定變更", "devToolsEnableDesc": "啟用開發者工具自動打開將在建立新視窗和標籤頁時自動打開開發者工具。", "devToolsDisableDesc": "停用開發者工具自動打開將不再自動打開開發者工具。", "devToolsRestartNotice": "此設定將立即生效，在下次建立視窗時生效。", "openLogFolder": "打開日誌文件夾", "shortcut": {"newChat": "新建聊天", "title": "快捷鍵設置"}, "notifications": "系統通知", "notificationsDesc": "當 DeepChat 不在前台時，如有會話生成完畢會發送系統通知", "languageDesc": "選擇應用程式介面語言，支援中文、英文、日文等多種語言，修改後立即生效", "contentProtectionDesc": "防止螢幕共享軟體捕獲DeepChat視窗，保護聊天內容隱私，需要重新啟動生效"}, "data": {"title": "資料設定", "syncEnable": "啟用資料同步", "syncFolder": "同步資料夾", "openSyncFolder": "開啟同步資料夾", "lastSyncTime": "上次同步時間", "never": "從未同步", "startBackup": "立即備份", "backingUp": "備份中...", "importData": "匯入資料", "incrementImport": "增量匯入", "overwriteImport": "覆蓋匯入", "importConfirmTitle": "確認匯入資料", "importConfirmDescription": "匯入將會覆蓋目前所有資料，包括聊天記錄和設定。請確保已備份重要資料。匯入完成後需要重新啟動應用程式。", "importing": "匯入中...", "confirmImport": "確認匯入", "importSuccessTitle": "匯入成功", "importErrorTitle": "匯入失敗", "resetData": "重設資料", "resetConfirmTitle": "確認重設資料", "resetConfirmDescription": "請選擇要重設的資料類型。此操作無法復原，重設後應用程式將自動重新啟動。", "resetChatData": "重設聊天資料", "resetChatDataDesc": "刪除所有聊天記錄和對話歷史", "resetKnowledgeData": "重設知識庫資料", "resetKnowledgeDataDesc": "刪除所有知識庫檔案和向量資料", "resetConfig": "重設設定", "resetConfigDesc": "刪除所有應用程式設定、模型設定和自訂提示詞", "resetAll": "完全重設", "resetAllDesc": "刪除所有資料，包括聊天記錄、設定和快取檔案", "resetting": "重設中...", "confirmReset": "確認重設", "resetCompleteDevTitle": "資料重設完成", "resetCompleteDevMessage": "開發環境下請手動重新啟動應用程式。請停止目前程序並重新執行 pnpm run dev"}, "model": {"title": "模型設定", "systemPrompt": {"label": "系統提示詞", "placeholder": "請輸入系統提示詞...", "description": "設定 AI 助理的系統提示詞，用於定義其行為和角色"}, "temperature": {"label": "模型溫度", "description": "控制輸出的隨機性，較高的值會產生更具創造性的回應"}, "contextLength": {"label": "前後文長度", "description": "設定對話前後文的最大長度"}, "responseLength": {"label": "回應文字長度", "description": "設定 AI 回應的最大長度"}, "artifacts": {"title": "Artifacts 效果", "description": "程式碼區塊生成動畫效果"}, "provider": "服務提供者", "modelList": "模型清單", "selectModel": "選擇模型", "providerSetting": "服務提供者設定", "configureModel": "設定模型", "addModel": "新增模型", "modelConfig": {"cancel": "取消", "contextLength": {"description": "設置模型能夠處理的上下文長度", "label": "上下文長度"}, "description": "請注意，此配置僅對當前模型有效，不會影響其他模型，請謹慎修改，錯誤的參數可能導致模型無法正常工作。", "functionCall": {"description": "模型是否原生支持函數調用(關閉這個選項後DeepChat會自動模擬函數調用)", "label": "函數調用"}, "maxTokens": {"description": "設置模型單次輸出的最大Token數量", "label": "最大輸出長度"}, "reasoning": {"description": "模型是否支持推理能力", "label": "推理能力"}, "thinkingBudget": {"label": "思考預算", "description": "限制模型思考長度", "dynamic": "動態思維", "range": "範圍: {min} - {max}", "onlySupported": "僅在 Gemini 2.5 Flash、2.5 Pro 和 2.5 Flash-Li<PERSON> 中受支援", "valueLabel": "思考預算值", "placeholder": "輸入思考預算值", "forceEnabled": "Gemini 2.5 系列模型強制開啟思考預算", "dynamicPrefix": "-1 = 動態思維", "notice": "注意：", "warnings": {"proNoDisable": "此模型不支援停用思考，最小值 128", "proCannotDisable": "Gemini 2.5 Pro 無法停用思考功能", "flashLiteCannotSetZero": "Gemini 2.5 Flash-Lite 不能設定為 0，最小值為 512", "modelCannotDisable": "此模型無法停用思考功能", "flashLiteMinValue": "思考預算設定為具體數值時不能小於 512（或使用 0 停用思考，-1 啟用動態思維）", "belowMin": "思考預算不能小於 {min}{hint}", "aboveMax": "思考預算不能大於 {max}"}, "hints": {"flashLiteDisable": "，0 = 停用思考，具體數值最小 512", "normalDisable": "，0 = 停用思考", "withZeroAndDynamic": "（或使用 0 停用思考，-1 啟用動態思維）", "withDynamic": "（或使用 -1 啟用動態思維）"}}, "resetConfirm": {"confirm": "確定重置", "message": "確定要重置此模型的配置為默認值嗎？\n此操作不可撤銷。", "title": "確認重置"}, "reasoningEffort": {"label": "推理努力程度", "description": "控制模型的推理深度，更高的努力程度會產生更好的結果但回應更慢", "placeholder": "選擇推理努力程度", "options": {"minimal": "Minimal - 最快回應", "low": "Low - 低努力", "medium": "Medium - 中等努力", "high": "High - 高努力"}}, "verbosity": {"label": "詳細程度", "description": "控制模型回答的詳細程度和長度", "placeholder": "選擇詳細程度", "options": {"low": "Low - 簡潔回答", "medium": "Medium - 平衡詳細", "high": "High - 詳細回答"}}, "resetToDefault": "重置為默認", "saveConfig": "保存配置", "useModelDefault": "使用模型預設配置", "currentUsingModelDefault": "目前使用模型預設配置", "temperature": {"description": "控制輸出的隨機性，大部分模型0-1，部分支持0-2之間，越高越隨機", "label": "溫度"}, "title": "自定義模型參數", "type": {"description": "選擇模型的類型", "label": "模型類型", "options": {"chat": "語言模型", "embedding": "嵌入模型", "imageGeneration": "圖像生成模型", "rerank": "重排序模型"}}, "validation": {"contextLengthMax": "上下文長度不能超過10000000", "contextLengthMin": "上下文長度必須大於0", "contextLengthRequired": "上下文長度不能為空", "maxTokensMax": "最大輸出長度不能超過1000000", "maxTokensMin": "最大輸出長度必須大於0", "maxTokensRequired": "最大輸出長度不能為空", "temperatureMax": "溫度必須小於等於2", "temperatureMin": "溫度必須大於等於0", "temperatureRequired": "溫度不能為空"}, "vision": {"description": "模型是否支持視覺能力", "label": "視覺能力"}}}, "provider": {"search": "搜尋服務提供者平台…", "enable": "啟用服務", "enabled": "已啟用", "disabled": "已停用", "urlPlaceholder": "請輸入 API URL", "keyPlaceholder": "請輸入 API 金鑰", "accessKeyIdPlaceholder": "請輸入 AWS Access Key ID", "secretAccessKeyPlaceholder": "請輸入 AWS Secret Access Key", "regionPlaceholder": "請輸入 AWS 區域", "verifyKey": "驗證金鑰", "howToGet": "如何取得", "getKeyTip": "請前往", "getKeyTipEnd": "取得 API 金鑰", "urlFormat": "API 範例：{defaultUrl}", "modelList": "模型清單", "enableModels": "啟用模型", "disableAllModels": "全部停用", "modelsEnabled": "模型已啟用", "noModelsEnabled": {"title": "暫無已啟用的模型", "description": "請點選「啟用模型」按鈕手動選擇需要使用的模型。"}, "verifyLink": "驗證連結", "syncModelsFailed": "同步模型失敗...", "addCustomProvider": "新增自訂服務提供者", "delete": "刪除", "stopModel": "停止模型", "pulling": "下載中...", "runModel": "執行模型", "dialog": {"disableModel": {"title": "確認停用模型", "content": "是否確認停用模型「{name}」？", "confirm": "停用"}, "disableAllModels": {"title": "確認停用所有模型", "content": "是否確認停用「{name}」的所有模型？", "confirm": "全部停用"}, "configModels": {"title": "設定模型清單", "description": "選擇要啟用或停用的模型"}, "verify": {"missingFields": "請輸入 API 金鑰和 API URL", "failed": "驗證失敗", "success": "驗證成功", "failedDesc": "API 金鑰或設定驗證失敗，請檢查設定資訊", "successDesc": "API 金鑰和設定驗證成功，可以正常使用", "connectionError": "連接錯誤，請檢查網絡連接和 API 地址", "serverError": "服務器錯誤，請稍後重試", "unauthorized": "認證失敗，API Key 無效或已過期"}, "addCustomProvider": {"title": "新增自訂服務提供者", "description": "請填寫服務提供者的必要資訊", "name": "名稱", "namePlaceholder": "請輸入服務提供者名稱", "apiType": "API 類型", "apiTypePlaceholder": "請選擇 API 類型", "apiKey": "API 金鑰", "apiKeyPlaceholder": "請輸入 API 金鑰", "baseUrl": "API 基礎網址", "baseUrlPlaceholder": "請輸入 API 基礎網址", "enable": "啟用服務提供者"}, "deleteProvider": {"title": "確認刪除服務提供者", "content": "是否確認刪除服務提供者「{name}」？此操作無法復原。", "confirm": "刪除"}, "deleteModel": {"title": "確認刪除模型", "content": "是否確認刪除模型「{name}」？此操作無法復原。", "confirm": "刪除"}, "pullModel": {"title": "下載模型", "description": "選擇要下載的模型到本機", "pull": "下載"}, "modelCheck": {"checking": "測試中...", "description": "選擇一個模型進行連接性和可用性測試", "failed": "模型測試失敗", "model": "選擇模型", "modelPlaceholder": "請選擇要測試的模型", "noModels": "該服務商沒有可用的模型", "success": "模型測試成功", "test": "開始測試", "title": "模型檢查"}}, "pullModels": "下載模型", "refreshModels": "重新整理模型", "modelsRunning": "執行中的模型", "runningModels": "執行中的模型", "noRunningModels": "沒有執行中的模型", "deleteModel": "刪除模型", "deleteModelConfirm": "是否確認刪除模型「{name}」？此操作無法復原。", "localModels": "本機模型", "noLocalModels": "沒有本機模型", "azureApiVersion": "API 版本", "safety": {"title": "安全設置", "blockHighest": "屏蔽高風險", "blockMost": "屏蔽中風險", "blockNone": "不屏蔽", "blockSome": "屏蔽低風險"}, "serverList": "伺服器列表", "totalServers": "伺服器總數", "addServer": "添加伺服器", "autoStart": "自啟動", "githubCopilotAuth": "GitHub Copilot 認證", "githubCopilotConnected": "GitHub Copilot 已連接", "githubCopilotNotConnected": "GitHub Copilot 未連接", "loginWithGitHub": "使用 GitHub 登入", "loggingIn": "登入中...", "githubCopilotLoginTip": "請授權 DeepChat 訪問您的 GitHub Copilot 訂閱。需要 'read:user' 和 'read:org' 權限才能正常使用 Copilot API。", "loginSuccess": "登入成功", "loginFailed": "登入失敗", "tokenValid": "令牌有效", "tokenInvalid": "令牌無效", "disconnect": "斷開連接", "disconnected": "已成功斷開連接", "disconnectFailed": "斷開連接失敗", "keyStatus": {"remaining": "剩餘額度", "usage": "已使用"}, "refreshingModels": "刷新中...", "toast": {"modelRunning": "模型正在運行", "modelRunningDesc": "請先停止模型 {model}，然後再刪除。"}, "anthropicApiKeyTip": "請前往 Anthropic Console 獲取您的 API Key", "anthropicConnected": "Anthropic 已連接", "anthropicNotConnected": "Anthropic 未連接", "anthropicOAuthTip": "點擊授權 DeepChat 訪問您的 Anthropic 賬戶", "oauthLogin": "OAuth 登錄", "authMethod": "認證方式", "authMethodPlaceholder": "選擇認證方式", "apiKeyLabel": "API Key", "apiUrlLabel": "API URL", "anthropicOAuthFlowTip": "系統將自動打開授權窗口，授權後請回來輸入授權碼", "anthropicBrowserOpened": "外部瀏覽器已打開", "anthropicCodeInstruction": "請在外部瀏覽器中完成授權，然後將獲得的授權碼粘貼到下方輸入框中", "browserOpenedSuccess": "外部瀏覽器已打開，請完成授權", "codeRequired": "請輸入授權碼", "inputOAuthCode": "輸入授權碼", "codeExchangeFailed": "授權碼交換失敗", "invalidCode": "授權碼無效", "oauthCodeHint": "請在外部瀏覽器中完成授權後，將獲得的授權碼粘貼到此處", "oauthCodePlaceholder": "請輸入授權碼...", "verifyConnection": "驗證連接", "manageModels": "管理模型", "anthropicOAuthActiveTip": "OAuth 認證已啟用，您可以直接使用 Anthropic 服務", "oauthVerifySuccess": "OAuth 連接驗證成功", "oauthVerifyFailed": "OAuth 連接驗證失敗", "modelscope": {"mcpSync": {"invalidServerData": "無效的服務器數據", "authenticationFailed": "認證失敗，請檢查 API Key", "convertingServers": "正在轉換服務器配置...", "description": "從 ModelScope 同步 MCP 服務器到本地配置，可以快速添加常用的 MCP 工具。\n所有服務默認禁用，導入後可手動啟用。", "errorDetails": "錯誤詳情", "errors": "錯誤 {count} 個", "fetchingServers": "正在獲取 MCP 服務器列表...", "imported": "已導入 {count} 個服務", "importingServers": "正在導入服務器配置...", "noApiKey": "請先配置 ModelScope API Key", "noOperationalUrls": "未找到可用的運營地址", "noServersFound": "未找到可用的 MCP 服務", "pageNumber": "頁碼", "pageNumberPlaceholder": "請輸入頁碼", "pageSize": "每頁數量", "serverAlreadyExists": "服務器已存在，跳過導入", "skipped": "跳過 {count} 個服務", "sync": "開始同步", "syncComplete": "同步完成", "syncing": "同步中...", "title": "同步 MCP 服務"}, "apiKey": "API 密鑰", "apiKeyHelper": "在 ModelScope 控制台獲取您的 API Key", "apiKeyPlaceholder": "請輸入 ModelScope API Key", "baseUrl": "API 地址", "baseUrlHelper": "ModelScope API 服務地址", "connected": "已連接", "connecting": "連接中...", "description": "ModelScope 是阿里巴巴達摩院推出的模型即服務共享平台", "details": {"apiConfig": "API 配置", "mcpSync": "MCP 同步", "modelManagement": "模型管理", "operationalDescription": "同步 ModelScope 平台上可直接使用的 MCP 服務器", "operationalServers": "運營服務器", "rateLimitConfig": "速率限製配置", "safetySettings": "安全設置", "specialConfig": "特殊配置", "syncFromModelScope": "從 ModelScope 同步", "title": "提供商設置詳情"}, "invalidKey": "無效的 API Key", "keyRequired": "請輸入 API Key", "name": "ModelScope", "networkError": "網絡連接錯誤", "notConnected": "未連接", "verifyFailed": "驗證失敗", "verifySuccess": "驗證成功"}, "configurationSaved": "配置已保存", "configurationUpdated": "配置已更新", "dataRefreshed": "數據已刷新", "operationFailed": "操作失敗", "operationSuccess": "操作成功", "settingsApplied": "設置已應用", "bedrockLimitTip": "* 僅支援 Anthropic Claude（包括 Opus，Sonnet，Haiku 模型）", "bedrockVerifyTip": "DeepChat 使用 Claude 3.5 Sonnet 模型驗證，若您無此模型的呼叫許可權驗證將失敗。這不會影響其他模型的使用。"}, "knowledgeBase": {"title": "知識庫設置", "addKnowledgeBase": "添加知識庫", "selectKnowledgeBaseType": "請選擇要添加的知識庫類型", "difyDescription": "Dify知識庫可以幫助您管理和使用文檔數據", "comingSoon": "即將推出", "featureNotAvailable": "該功能暫未開放，敬請期待", "addDifyConfig": "添加Dify配置", "apiKey": "API密鑰", "datasetId": "數據集ID", "endpoint": "API地址", "configAdded": "配置已添加", "configAddedDesc": "{name}配置已成功添加", "addConfig": "添加配置", "moreComingSoon": "更多知識庫類型即將推出", "configUpdated": "配置已更新", "configUpdatedDesc": "{name}配置已成功更新", "descriptionPlaceholder": "例如：公司產品文檔知識庫", "ragflowTitle": "RAGFlow 知識庫", "ragflowDescription": "RAGFlow 是一個強大的知識庫管理系統，支援多種檢索方式和文件管理功能。", "addRagflowConfig": "新增 RAGFlow 設定", "editRagflowConfig": "編輯 RAGFlow 設定", "dify": "Dify知識庫", "editDifyConfig": "修改Dify配置", "fastgptTitle": "FastGPT知識庫", "fastgptDescription": "FastGPT是一個強大的知識庫管理系統，支援多種檢索方式和文檔管理功能。", "addFastGptConfig": "添加FastGPT配置", "editFastGptConfig": "編輯FastGPT配置", "builtInKnowledgeDescription": "內置知識庫提供了一些簡單實現，能夠在離線環境下實現部分基礎功能。", "builtInKnowledgeTitle": "內置知識庫", "addBuiltinKnowledgeConfig": "添加內置知識庫配置", "editBuiltinKnowledgeConfig": "編輯內置知識庫配置", "chunkSize": "分塊大小", "chunkSizeHelper": "將文檔切割分段，每段的大小，不能超過模型上下文限制", "chunkOverlap": "重疊大小", "chunkOverlapHelper": "相鄰文本塊之間重複的內容量，確保分段後的文本塊之間仍然有上下文聯繫，提升模型處理長文本的整體效果", "selectEmbeddingModel": "選擇嵌入模型", "modelNotFound": "服務商 {provider} 或模型 {model} 未找到", "modelNotFoundDesc": "請確保已正確配置模型，並且模型處於啟用狀態。\n您可以在服務商設置中檢查模型配置。", "removeBuiltinKnowledgeConfirmDesc": "刪除內置知識庫配置將會刪除所有相關數據，且無法恢復，請謹慎操作。", "removeBuiltinKnowledgeConfirmTitle": "確認刪除內置知識庫 {name} 嗎？", "descriptionDesc": "知識庫的描述，以便 AI 決定是否檢索此知識庫", "advanced": "高級選項", "autoDetectDimensions": "自動檢測嵌入維度", "autoDetectHelper": "自動檢測嵌入維度，會消耗少量 Tokens", "chunkOverlapPlaceholder": "默認值，不建議修改", "chunkSizePlaceholder": "默認值，不建議修改", "dimensions": "嵌入維度", "dimensionsPlaceholder": "嵌入維度大小，如 1024", "selectEmbeddingModelHelper": "嵌入模型在知識庫創建後禁止修改", "dimensionsHelper": "請確保模型支持所設置的嵌入維度大小", "autoDetectDimensionsError": "自動檢測嵌入維度失敗", "fragmentsNumber": "請求文檔片段數量", "fragmentsNumberHelper": "請求文檔片段數量越多，附帶的信息越多，但需要消耗的token也越多", "selectRerankModel": "選擇重排序模型", "rerankModel": "重排序模型", "embeddingModel": "嵌入模型", "return": "返回", "uploadHelper": "點擊上傳或拖拽文件到此處", "fileSupport": "支持 {accept} 等 {count} 種格式", "searchKnowledge": "搜索知識庫", "searchKnowledgePlaceholder": "請輸入查詢內容", "noData": "暫無數據", "file": "文件", "uploadProcessing": "上傳中", "uploadCompleted": "上傳完成", "reAdd": "重新上傳", "uploadError": "上傳失敗", "delete": "刪除", "reason": "原因", "deleteSuccess": "刪除成功", "copy": "複製", "copySuccess": "複製成功", "source": "來源", "normalized": "L2 歸一化", "normalizedHelper": "請確認模型支持對輸出向量進行 L2 歸一化", "dialog": {"beforequit": {"cancel": "取消", "confirm": "確認", "title": "退出確認", "description": "有正在運行的知識庫任務，是否確認退出軟件？\n被中止的任務可在重啟軟件後恢復。"}}, "searchError": "查詢失敗", "processing": "正在上傳", "paused": "上傳暫停", "unknown": "未知狀態", "reAddFile": {"title": "重新上傳確認", "content": "是否確認重新上傳文件 \"{fileName}\"？"}, "deleteFile": {"title": "刪除文件確認", "content": "是否確認刪除文件 \"{fileName}\"？\n此操作不可恢復。"}, "resumeAllPausedTasks": "一鍵恢復", "pauseAllRunningTasks": "一鍵暫停", "separators": "塊分隔符", "separatorsHelper": "文檔切分分隔符，單個分隔符用半角符號雙引號(\"\")包裹，分隔符之間用半角符號逗號(,)分割", "invalidSeparators": "無效的分隔符", "selectLanguage": "選擇預設", "separatorsPreset": "加載預設", "promptManagement": {"title": "Prompt管理", "description": "管理和使用自定義Prompt範本，幫助您更好地與AI互動"}}, "mcp": {"title": "MCP設定", "description": "管理和配置MCP（模型控制協議）伺服器和工具", "enabledTitle": "啟用MCP", "enabledDescription": "啟用或停用MCP功能和工具", "enableToAccess": "請先啟用MCP以訪問配置選項", "tabs": {"servers": "伺服器", "tools": "工具", "prompts": "提示詞", "resources": "資源"}, "serverList": "伺服器列表", "totalServers": "伺服器總數", "addServer": "新增伺服器", "running": "運行中", "stopped": "已停止", "stopServer": "停止伺服器", "startServer": "啟動伺服器", "noServersFound": "未找到伺服器", "addServerDialog": {"title": "新增伺服器", "description": "配置新的MCP伺服器"}, "editServerDialog": {"title": "編輯伺服器", "description": "編輯MCP伺服器配置"}, "serverForm": {"name": "伺服器名稱", "namePlaceholder": "輸入伺服器名稱", "nameRequired": "伺服器名稱不能為空", "type": "伺服器類型", "typePlaceholder": "選擇伺服器類型", "typeStdio": "標準輸入輸出(Stdio)", "typeSse": "服務器發送事件(SSE)", "baseUrl": "基礎網址", "baseUrlPlaceholder": "輸入伺服器基礎網址（如：http://localhost:3000）", "command": "命令", "commandPlaceholder": "輸入命令", "commandRequired": "命令不能為空", "args": "參數", "argsPlaceholder": "輸入參數，用空格分隔", "argsRequired": "參數不能為空", "env": "環境變數", "envPlaceholder": "輸入JSON格式的環境變數", "envInvalid": "環境變數必須是有效的JSON格式", "description": "描述", "descriptionPlaceholder": "輸入伺服器描述", "descriptions": "描述", "descriptionsPlaceholder": "輸入伺服器描述", "icon": "圖示", "iconPlaceholder": "輸入圖示", "icons": "圖示", "iconsPlaceholder": "輸入圖示", "autoApprove": "自動授權", "autoApproveAll": "全部", "autoApproveRead": "讀取", "autoApproveWrite": "寫入", "autoApproveHelp": "選擇需要自動授權的操作類型，無需用戶確認即可執行", "submit": "提交", "add": "新增", "update": "更新", "cancel": "取消", "jsonConfigIntro": "您可以直接粘貼JSON配置或選擇手動配置伺服器。", "jsonConfig": "JSON配置", "jsonConfigPlaceholder": "請粘貼MCP伺服器的JSON格式配置", "jsonConfigExample": "JSON配置範例", "parseSuccess": "配置解析成功", "configImported": "配置導入成功", "parseError": "解析錯誤", "skipToManual": "跳過至手動配置", "parseAndContinue": "解析並繼續", "jsonParseError": "JSON解析失敗", "typeHttp": "可流式傳輸的HTTP請求(HTTP)", "typeInMemory": "內存(InMemory)", "browseMarketplace": "瀏覽MCP服務市場", "imageModel": "選擇視覺模型", "customHeadersParseError": "自定義Header解析失敗", "customHeaders": "自定義請求頭", "invalidKeyValueFormat": "錯誤的請求頭格式，請檢查輸入是否正確", "npmRegistry": "自定義NPM Registry", "npmRegistryPlaceholder": "設置自定義NPM Registry,留空系統會自動選擇最快的一個", "browseHigress": "瀏覽 Higress MCP 市場", "selectFolderError": "文件夾選擇錯誤", "folders": "允許訪問的文件夾", "addFolder": "添加文件夾", "noFoldersSelected": "未選擇任何文件夾", "useE2B": "啟用E2B沙盒", "e2bDescription": "使用E2B沙盒執行Python代碼", "e2bApiKey": "E2B ApiKey", "e2bApiKeyPlaceholder": "這裡輸入你的E2B Api Keys，如 e2b_1111xx*****", "e2bApiKeyHelp": "前往 e2b.dev 獲取你的 ApiKey", "e2bApiKeyRequired": "啟用E2B功能必須要輸入 ApiKey", "clickToEdit": "點擊編輯以查看完整內容"}, "deleteServer": "刪除伺服器", "editServer": "編輯伺服器", "setDefault": "設為預設", "isDefault": "預設伺服器", "default": "預設", "setAsDefault": "設為預設伺服器", "removeServer": "刪除伺服器", "autoStart": "自啟動", "confirmRemoveServer": "確定要刪除伺服器 {name} 嗎？此操作無法撤銷。", "removeServerDialog": {"title": "刪除伺服器"}, "confirmDelete": {"title": "確認刪除", "description": "確定要刪除伺服器 {name} 嗎？此操作無法撤銷。", "confirm": "刪除", "cancel": "取消"}, "resetToDefault": "恢復預設服務", "resetConfirmTitle": "恢復預設服務", "resetConfirmDescription": "此操作將恢復所有預設伺服器，同時保留您自訂的伺服器。對預設伺服器的任何修改將會丟失。", "resetConfirm": "恢復", "builtIn": "內建", "builtInServerCannotBeRemoved": "內置服務不能被刪除，僅支持修改參數和環境變量", "builtInServers": "內置服務", "cannotRemoveBuiltIn": "無法刪除內置服務", "customServers": "自定義服務", "removeDefault": "移除默認", "marketplace": "前往MCP市場一鍵安裝", "maxDefaultServersReached": "最多只能設置3個默認服務器", "removeDefaultFirst": "請先移除一些默認服務器", "higressMarket": "前往 Higress MCP 安裝", "npmRegistry": {"title": "NPM源設定", "currentSource": "目前來源", "cached": "快取", "lastChecked": "上次檢測", "refresh": "重新整理", "advanced": "進階", "advancedSettings": "進階設定", "advancedSettingsDesc": "設定NPM源的進階選項，包括自動檢測和自訂來源設定", "autoDetect": "自動檢測最佳來源", "autoDetectDesc": "啟動時自動檢測並使用最快的NPM源", "customSource": "自訂來源", "customSourcePlaceholder": "輸入自訂NPM源位址", "currentCustom": "目前自訂來源", "justNow": "剛剛", "minutesAgo": "{minutes}分鐘前", "hoursAgo": "{hours}小時前", "daysAgo": "{days}天前", "refreshSuccess": "NPM源重新整理成功", "refreshSuccessDesc": "已重新檢測並更新最佳NPM源", "refreshFailed": "NPM源重新整理失敗", "autoDetectUpdated": "自動檢測設定已更新", "autoDetectEnabled": "已啟用自動檢測最佳NPM源", "autoDetectDisabled": "已停用自動檢測，將使用預設來源", "updateFailed": "設定更新失敗", "customSourceSet": "自訂來源已設定", "customSourceSetDesc": "已設定自訂NPM源：{registry}", "customSourceCleared": "自訂來源已清除", "customSourceClearedDesc": "已清除自訂NPM源，將使用自動檢測", "invalidUrl": "無效的URL", "invalidUrlDesc": "請輸入有效的HTTP或HTTPS位址", "testing": "正在測試NPM源", "testingDesc": "正在測試來源 {registry} 的連線狀態...", "testFailed": "NPM源測試失敗", "testFailedDesc": "無法連線到 {registry}，錯誤：{error}。請檢查URL是否正確或網路連線。", "redetectingOptimal": "正在重新檢測最佳NPM源...", "redetectComplete": "重新檢測完成", "redetectCompleteDesc": "已檢測並設定目前最佳的NPM源", "redetectFailed": "重新檢測失敗", "redetectFailedDesc": "無法重新檢測最佳來源，將使用預設設定"}, "technicalDetails": "技術詳情", "httpServer": "HTTP伺服器", "localProcess": "本機程序", "restartServer": "重新啟動伺服器", "viewLogs": "查看記錄", "starting": "啟動中", "error": "錯誤"}, "about": {"title": "關於我們", "version": "版本", "checkUpdate": "檢查更新", "checking": "檢查中...", "latestVersion": "最新版本"}, "display": {"fontSize": "文字大小", "text-2xl": "超大", "text-base": "預設", "text-lg": "大", "text-sm": "小", "text-xl": "特大", "floatingButton": "懸浮按鈕", "floatingButtonDesc": "在桌面顯示一個懸浮按鈕，可以快速喚起應用程式視窗"}, "shortcuts": {"title": "快捷鍵設置", "pressKeys": "輸入快捷鍵", "pressEnterToSave": "按Enter保存，Esc取消", "noModifierOnly": "不能僅使用修飾鍵作為快捷鍵", "keyConflict": "快捷鍵衝突，請選擇其他組合", "clearShortcut": "清除快捷鍵", "cleanHistory": "清除聊天歷史", "deleteConversation": "刪除會話", "goSettings": "打開設置", "hideWindow": "隱藏窗口", "quitApp": "退出程序", "zoomIn": "放大字體", "zoomOut": "縮小字體", "zoomReset": "重置字體", "closeTab": "關閉當前標籤頁", "newTab": "新建標籤頁", "newWindow": "打開新窗口", "showHideWindow": "顯示/隱藏窗口", "newConversation": "新會話", "lastTab": "切換到最後一個標籤頁", "previousTab": "切換到上一個標籤頁", "specificTab": "切換到指定標籤頁 (1-8)", "nextTab": "切換到下一個標籤頁"}, "rateLimit": {"title": "速率限制", "description": "控制請求時間間隔，避免超出 API 限制", "intervalLimit": "請求間隔", "intervalUnit": "秒", "intervalHelper": "兩個請求之間的最小間隔，不需要時請關閉速率限制", "lastRequestTime": "最後請求", "queueLength": "佇列長度", "nextAllowedTime": "下次允許請求", "never": "從未", "justNow": "剛才", "secondsAgo": "秒前", "minutesAgo": "分鐘前", "immediately": "立即", "secondsLater": "秒後", "confirmDisableTitle": "確認關閉速率限制", "confirmDisableMessage": "該值不能小於或等於0，是否關閉速率限制功能？", "confirmDisable": "關閉限制", "disabled": "速率限制已關閉", "disabledDescription": "速率限制功能已關閉"}}