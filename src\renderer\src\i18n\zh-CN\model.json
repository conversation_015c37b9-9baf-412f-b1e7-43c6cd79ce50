{"search": {"placeholder": "搜索模型..."}, "error": {"loadFailed": "加载模型失败"}, "type": {"custom": "自定义模型", "official": "官方模型"}, "add": {"namePlaceholder": "模型名称", "idPlaceholder": "模型ID", "contextLengthPlaceholder": "上下文长度", "maxTokensPlaceholder": "最大token"}, "actions": {"add": "添加模型", "enableAll": "全部启用", "disableAll": "全部禁用"}, "tags": {"reasoning": "推理", "chat": "对话", "code": "代码", "writing": "写作", "analysis": "分析"}, "categories": {"all": "全部", "chat": "对话", "reasoning": "推理", "vision": "视觉", "network": "联网", "free": "免费", "rerank": "重排", "tool": "工具", "embedding": "嵌入", "imageGeneration": "图像生成"}}