{"title": "設置", "common": {"title": "通用設置", "modelSettings": "模型設置", "searchSettings": "搜索設置", "networkSettings": "網絡設置", "interfaceSettings": "界面與互動", "personalizationSettings": "個人化設置", "securitySettings": "安全與隱私", "operationSettings": "操作設置", "resetData": "重置數據", "language": "語言", "languageSelect": "選擇語言", "searchEngine": "搜索引擎", "searchEngineDesc": "選擇搜索引擎用於網絡搜索增強，支持谷歌、百度等主流搜索引擎，也可添加自定義搜索引擎", "searchEngineSelect": "選擇搜索引擎", "searchPreview": "搜索預覽", "searchPreviewDesc": "開啟後在搜索結果頁面顯示預覽圖，可快速瀏覽搜索結果內容", "searchAssistantModel": "助手模型", "searchAssistantModelDesc": "選擇用於處理搜索結果並生成回答的模型，影響搜索增強的品質和速度", "selectModel": "選擇模型", "visionModel": "視覺模型", "visionModelDesc": "選擇用於處理圖像和視覺內容的模型，用於圖像描述、分析等功能", "visionModelNotSelectedWarning": "未選擇視覺模型，使用圖像功能時將提示您先進行設定", "proxyMode": "代理模式", "proxyModeDesc": "設置網絡代理模式，可選擇系統代理、不使用代理或自定義代理，用於訪問國外服務", "proxyModeSelect": "選擇代理模式", "proxyModeSystem": "系統代理", "proxyModeNone": "不使用代理", "proxyModeCustom": "自定義代理", "customProxyUrl": "自定義代理地址", "customProxyUrlPlaceholder": "例如: http://127.0.0.1:7890", "invalidProxyUrl": "無效的代理地址，請輸入有效的 http/https URL", "addCustomSearchEngine": "添加自訂搜尋引擎", "addCustomSearchEngineDesc": "添加一個新的搜尋引擎，需要提供名稱和搜尋URL。URL中必須包含{\"{\"}query{\"}\"}作為查詢佔位符。", "searchEngineName": "搜尋引擎名稱", "searchEngineNamePlaceholder": "請輸入搜尋引擎名稱", "searchEngineUrl": "搜尋URL", "searchEngineUrlPlaceholder": "如: https://a.com/search?q={\"{\"}query{\"}\"}", "searchEngineUrlError": "URL必須包含{\"{\"}query{\"}\"}作為查詢占位符", "deleteCustomSearchEngine": "刪除自定義搜索引擎", "deleteCustomSearchEngineDesc": "確定要刪除自定義搜索引擎 \"{name}\" 嗎？此操作無法撤銷。", "testSearchEngine": "測試搜索引擎", "testSearchEngineDesc": "即將使用 {engine} 搜索引擎進行測試搜索，將搜索關鍵詞 \"天氣\"。", "testSearchEngineNote": "如果搜索頁面需要登錄或其他操作，您可以在測試窗口中進行。完成測試後請關閉測試窗口。", "theme": "主題", "themeSelect": "選擇主題", "closeToQuit": "點擊關閉按鈕時退出程序", "closeToQuitDesc": "選擇點擊關閉按鈕時是退出程序還是隱藏到系統順，隱藏後可從順中恢復", "contentProtection": "投屏保護", "contentProtectionDialogTitle": "確認切換投屏保護", "contentProtectionEnableDesc": "開啟投屏保護可以防止投屏軟件捕獲DeepChat主窗口，用來保護您的內容隱私。請注意，此功能不會徹底隱藏所有界面，請合理合規使用。並且，並不是所有投屏軟件都遵守用戶隱私設定，該功能可能在一些不遵守隱私設定的投屏軟件上失效，且部分環境中可能會殘留一個黑色窗體。", "contentProtectionDisableDesc": "關閉投屏保護將允許投屏軟件捕獲DeepChat窗口。", "contentProtectionRestartNotice": "切換此設置將導致程序重啟，請確認是否繼續？", "soundEnabled": "啟用音效", "soundEnabledDesc": "啟用訊息接收、錯誤提示等系統音效，提供更好的交互體驗", "copyWithCotEnabled": "複製COT資訊", "copyWithCotEnabledDesc": "複製訊息時包含思維鏈（Chain of Thought）資訊，方便理解AI的推理過程", "loggingEnabled": "啟用日誌", "loggingEnabledDesc": "啟用日誌記錄可幫助診斷問題和改進應用程式，但可能包含敏感資訊，需要重新啟動生效", "devToolsAutoOpen": "自動打開開發者工具", "devToolsAutoOpenDesc": "在建立視窗和標籤頁時自動打開開發者工具，方便除錯和開發", "loggingDialogTitle": "確認日誌設定變更", "loggingEnableDesc": "啟用日誌將幫助我們診斷問題並改進應用程式。日誌檔案可能包含敏感資訊。", "loggingDisableDesc": "停用日誌將停止收集應用程式日誌。", "loggingRestartNotice": "切換此設定將導致程序重啟，請確認是否繼續？", "devToolsDialogTitle": "確認開發者工具設定變更", "devToolsEnableDesc": "啟用開發者工具自動打開將在建立新視窗和標籤頁時自動打開開發者工具。", "devToolsDisableDesc": "停用開發者工具自動打開將不再自動打開開發者工具。", "devToolsRestartNotice": "此設定將立即生效，在下次建立視窗時生效。", "openLogFolder": "打開日誌文件夾", "shortcut": {"newChat": "新建聊天", "title": "快捷鍵設置"}, "notifications": "系統通知", "notificationsDesc": "當 DeepChat 不在前台時，如有會話生成完畢會發送系統通知", "languageDesc": "選擇應用程式介面語言，支持中文、英文、日文等多種語言，修改後立即生效", "contentProtectionDesc": "防止螢幕共享軟體捕獲DeepChat視窗，保護聊天內容隱私，需要重新啟動生效"}, "data": {"title": "數據設置", "syncEnable": "啟用數據同步", "syncFolder": "同步資料夾", "openSyncFolder": "打開同步資料夾", "lastSyncTime": "最後同步時間", "never": "從未同步", "startBackup": "立即備份", "backingUp": "備份中...", "importData": "導入數據", "incrementImport": "增量導入", "overwriteImport": "覆蓋導入", "importConfirmTitle": "確認導入數據", "importConfirmDescription": "導入將會覆蓋所有當前數據，包括聊天記錄和設置。請確保已備份重要數據。導入後需要重新啟動應用程序。", "importing": "導入中...", "confirmImport": "確認導入", "importSuccessTitle": "導入成功", "importErrorTitle": "導入失敗", "resetData": "重置數據", "resetConfirmTitle": "確認重置數據", "resetConfirmDescription": "請選擇要重置的數據類型。此操作不可撤銷，重置後應用程式將自動重新啟動。", "resetChatData": "重置聊天數據", "resetChatDataDesc": "刪除所有聊天記錄和對話歷史", "resetKnowledgeData": "重置知識庫數據", "resetKnowledgeDataDesc": "刪除所有知識庫檔案和向量數據", "resetConfig": "重置配置", "resetConfigDesc": "刪除所有應用程式設置、模型配置和自定義提示詞", "resetAll": "完全重置", "resetAllDesc": "刪除所有數據，包括聊天記錄、配置和快取檔案", "resetting": "重置中...", "confirmReset": "確認重置", "resetCompleteDevTitle": "數據重置完成", "resetCompleteDevMessage": "開發環境下請手動重新啟動應用程式。請停止當前進程並重新執行 pnpm run dev"}, "model": {"title": "模型設置", "systemPrompt": {"label": "系統提示詞", "placeholder": "請輸入系統提示詞...", "description": "設置AI助手的系統提示詞，用於定義其行為和角色"}, "temperature": {"label": "模型溫度", "description": "控制輸出的隨機性，較高的值會產生更具創造性的響應"}, "contextLength": {"label": "上下文長度", "description": "設置對話上下文的最大長度"}, "responseLength": {"label": "返回文本長度", "description": "設置AI響應的最大長度"}, "artifacts": {"description": "啟用Artifacts功能允許AI生成更豐富的內容", "title": "Artifacts效果"}, "addModel": "添加模型", "configureModel": "配置模型", "modelList": "模型列表", "provider": "服務商", "providerSetting": "服務商設置", "selectModel": "選擇模型", "modelConfig": {"cancel": "取消", "contextLength": {"description": "設置模型能夠處理的上下文長度", "label": "上下文長度"}, "description": "請注意，此配置僅對當前模型有效，不會影響其他模型，請謹慎修改，錯誤的參數可能導致模型無法正常工作。", "functionCall": {"description": "模型是否原生支持函數調用(關閉這個選項後DeepChat會自動模擬函數調用)", "label": "函數調用"}, "maxTokens": {"description": "設置模型單次輸出的最大Token數量", "label": "最大輸出長度"}, "reasoning": {"description": "模型是否支持推理能力", "label": "推理能力"}, "thinkingBudget": {"label": "思考預算", "description": "限制模型思考長度", "dynamic": "動態思維", "range": "範圍: {min} - {max}", "onlySupported": "僅在 Gemini 2.5 Flash、2.5 Pro 和 2.5 Flash-Li<PERSON> 中受支援", "valueLabel": "思考預算值", "placeholder": "輸入思考預算值", "forceEnabled": "Gemini 2.5 系列模型強制開啟思考預算", "dynamicPrefix": "-1 = 動態思維", "notice": "注意：", "warnings": {"proNoDisable": "此模型不支援停用思考，最小值 128", "proCannotDisable": "Gemini 2.5 Pro 無法停用思考功能", "flashLiteCannotSetZero": "Gemini 2.5 Flash-Lite 不能設定為 0，最小值為 512", "modelCannotDisable": "此模型無法停用思考功能", "flashLiteMinValue": "思考預算設定為具體數值時不能小於 512（或使用 0 停用思考，-1 啟用動態思維）", "belowMin": "思考預算不能小於 {min}{hint}", "aboveMax": "思考預算不能大於 {max}"}, "hints": {"flashLiteDisable": "，0 = 停用思考，具體數值最小 512", "normalDisable": "，0 = 停用思考", "withZeroAndDynamic": "（或使用 0 停用思考，-1 啟用動態思維）", "withDynamic": "（或使用 -1 啟用動態思維）"}}, "resetConfirm": {"confirm": "確定重置", "message": "確定要重置此模型的配置為默認值嗎？\n此操作不可撤銷。", "title": "確認重置"}, "reasoningEffort": {"label": "推理努力程度", "description": "控制模型的推理深度，更高的努力程度會產生更好的結果但響應更慢", "placeholder": "選擇推理努力程度", "options": {"minimal": "Minimal - 最快響應", "low": "Low - 低努力", "medium": "Medium - 中等努力", "high": "High - 高努力"}}, "verbosity": {"label": "詳細程度", "description": "控制模型回答的詳細程度和長度", "placeholder": "選擇詳細程度", "options": {"low": "Low - 簡潔回答", "medium": "Medium - 平衡詳細", "high": "High - 詳細回答"}}, "resetToDefault": "重置為默認", "saveConfig": "保存配置", "useModelDefault": "使用模型預設配置", "currentUsingModelDefault": "目前使用模型預設配置", "temperature": {"description": "控制輸出的隨機性，大部分模型0-1，部分支持0-2之間，越高越隨機", "label": "溫度"}, "title": "自定義模型參數", "type": {"description": "選擇模型的類型", "label": "模型類型", "options": {"chat": "語言模型", "embedding": "嵌入模型", "imageGeneration": "圖像生成模型", "rerank": "重排序模型"}}, "validation": {"contextLengthMax": "上下文長度不能超過10000000", "contextLengthMin": "上下文長度必須大於0", "contextLengthRequired": "上下文長度不能為空", "maxTokensMax": "最大輸出長度不能超過1000000", "maxTokensMin": "最大輸出長度必須大於0", "maxTokensRequired": "最大輸出長度不能為空", "temperatureMax": "溫度必須小於等於2", "temperatureMin": "溫度必須大於等於0", "temperatureRequired": "溫度不能為空"}, "vision": {"description": "模型是否支持視覺能力", "label": "視覺能力"}}}, "provider": {"search": "搜尋服務商平台…", "enable": "開啟服務", "enabled": "已啟用", "disabled": "已禁用", "urlPlaceholder": "請輸入API URL", "keyPlaceholder": "請輸入API Key", "accessKeyIdPlaceholder": "請輸入AWS Access Key ID", "secretAccessKeyPlaceholder": "請輸入AWS Secret Access Key", "regionPlaceholder": "請輸入AWS區域", "verifyKey": "驗證密鑰", "howToGet": "如何獲取", "getKeyTip": "請前往", "getKeyTipEnd": "獲取API Key", "urlFormat": "API範例：{defaultUrl}", "modelList": "模型列表", "enableModels": "模型已經啟用", "verifyLink": "驗證鏈接", "syncModelsFailed": "同步模型失敗...", "addCustomProvider": "添加自定義服務商", "disableAllModels": "禁用全部模型", "delete": "刪除", "stopModel": "停止模型", "pulling": "拉取中...", "runModel": "運行模型", "dialog": {"disableModel": {"title": "確認禁用模型", "content": "是否確認禁用模型 \"{name}\"？", "confirm": "禁用"}, "configModels": {"title": "配置模型列表", "description": "選擇要啟用或禁用的模型"}, "disableAllModels": {"title": "確認禁用全部模型", "content": "是否確認禁用 \"{name}\" 的全部模型？", "confirm": "全部禁用"}, "verify": {"missingFields": "請輸入 API Key 和 API URL", "failed": "驗證失敗", "success": "驗證成功", "failedDesc": "API 密鑰或配置驗證失敗，請檢查配置信息", "successDesc": "API 密鑰和配置驗證成功，可以正常使用", "connectionError": "連接錯誤，請檢查網絡連接和 API 地址", "serverError": "服務器錯誤，請稍後重試", "unauthorized": "認證失敗，API Key 無效或已過期"}, "addCustomProvider": {"title": "添加自定義服務商", "description": "請填寫服務商的必要信息", "name": "名稱", "namePlaceholder": "請輸入服務商名稱", "apiType": "API類型", "apiTypePlaceholder": "請選擇API類型", "apiKey": "API密鑰", "apiKeyPlaceholder": "請輸入API密鑰", "baseUrl": "API基礎地址", "baseUrlPlaceholder": "請輸入API基礎地址", "enable": "啟用服務商"}, "deleteProvider": {"title": "確認刪除服務商", "content": "是否確認刪除服務商 \"{name}\"？此操作不可恢復。", "confirm": "刪除"}, "deleteModel": {"title": "確認刪除模型", "content": "是否確認刪除模型 \"{name}\"？此操作不可恢復。", "confirm": "刪除"}, "pullModel": {"title": "拉取模型", "description": "選擇要下載到本地的模型", "pull": "拉取"}, "modelCheck": {"checking": "測試中...", "description": "選擇一個模型進行連接性和可用性測試", "failed": "模型測試失敗", "model": "選擇模型", "modelPlaceholder": "請選擇要測試的模型", "noModels": "該服務商沒有可用的模型", "success": "模型測試成功", "test": "開始測試", "title": "模型檢查"}}, "pullModels": "拉取模型", "refreshModels": "刷新模型", "modelsRunning": "模型正在運行", "runningModels": "運行中的模型", "noRunningModels": "沒有運行中的模型", "deleteModel": "刪除模型", "deleteModelConfirm": "是否確認刪除模型 \"{name}\"？此操作不可恢復。", "noLocalModels": "沒有本地模型", "localModels": "本地模型", "modelsEnabled": "模型已經啟用", "noModelsEnabled": {"title": "暫無已啟用的模型", "description": "請點擊「啟用模型」按鈕手動選擇需要使用的模型。"}, "azureApiVersion": "API 版本", "safety": {"title": "安全設置", "blockHighest": "屏蔽高風險", "blockMost": "屏蔽中風險", "blockNone": "不屏蔽", "blockSome": "屏蔽低風險"}, "serverList": "服務器列表", "totalServers": "服務器總數", "addServer": "添加服務器", "autoStart": "自啟動", "githubCopilotAuth": "GitHub Copilot 認證", "githubCopilotConnected": "GitHub Copilot 已連接", "githubCopilotNotConnected": "GitHub Copilot 未連接", "loginWithGitHub": "用 GitHub 登入", "loggingIn": "登入中...", "githubCopilotLoginTip": "請授權 DeepChat 存取你的 GitHub Copilot 訂閱。需要 'read:user' 及 'read:org' 權限才能正常使用 Copilot API。", "loginSuccess": "登入成功", "loginFailed": "登入失敗", "tokenValid": "Token 有效", "tokenInvalid": "Token 無效", "disconnect": "斷開連接", "disconnected": "已成功斷開連接", "disconnectFailed": "斷開連接失敗", "keyStatus": {"remaining": "剩餘額度", "usage": "已使用"}, "refreshingModels": "刷新中...", "toast": {"modelRunning": "模型正在運行", "modelRunningDesc": "請先停止模型 {model}，然後再刪除。"}, "anthropicApiKeyTip": "請前往 Anthropic Console 獲取您的 API Key", "anthropicConnected": "Anthropic 已連接", "anthropicNotConnected": "Anthropic 未連接", "anthropicOAuthTip": "點擊授權 DeepChat 訪問您的 Anthropic 賬戶", "oauthLogin": "OAuth 登錄", "authMethod": "認證方式", "authMethodPlaceholder": "選擇認證方式", "apiKeyLabel": "API Key", "apiUrlLabel": "API URL", "anthropicOAuthFlowTip": "系統將自動打開授權窗口，授權後請回來輸入授權碼", "anthropicBrowserOpened": "外部瀏覽器已打開", "anthropicCodeInstruction": "請在外部瀏覽器中完成授權，然後將獲得的授權碼粘貼到下方輸入框中", "browserOpenedSuccess": "外部瀏覽器已打開，請完成授權", "codeRequired": "請輸入授權碼", "inputOAuthCode": "輸入授權碼", "codeExchangeFailed": "授權碼交換失敗", "invalidCode": "授權碼無效", "oauthCodeHint": "請在外部瀏覽器中完成授權後，將獲得的授權碼粘貼到此處", "oauthCodePlaceholder": "請輸入授權碼...", "verifyConnection": "驗證連接", "manageModels": "管理模型", "anthropicOAuthActiveTip": "OAuth 認證已啟用，您可以直接使用 Anthropic 服務", "oauthVerifySuccess": "OAuth 連接驗證成功", "oauthVerifyFailed": "OAuth 連接驗證失敗", "configurationSaved": "配置已保存", "configurationUpdated": "配置已更新", "dataRefreshed": "數據已刷新", "modelscope": {"apiKey": "API 密鑰", "apiKeyHelper": "在 ModelScope 控制台獲取您的 API Key", "apiKeyPlaceholder": "請輸入 ModelScope API Key", "baseUrl": "API 地址", "baseUrlHelper": "ModelScope API 服務地址", "connected": "已連接", "connecting": "連接中...", "description": "ModelScope 是阿里巴巴達摩院推出的模型即服務共享平台", "details": {"apiConfig": "API 配置", "mcpSync": "MCP 同步", "modelManagement": "模型管理", "operationalDescription": "同步 ModelScope 平台上可直接使用的 MCP 服務器", "operationalServers": "運營服務器", "rateLimitConfig": "速率限製配置", "safetySettings": "安全設置", "specialConfig": "特殊配置", "syncFromModelScope": "從 ModelScope 同步", "title": "提供商設置詳情"}, "invalidKey": "無效的 API Key", "keyRequired": "請輸入 API Key", "mcpSync": {"authenticationFailed": "認證失敗，請檢查 API Key", "convertingServers": "正在轉換服務器配置...", "description": "從 ModelScope 同步 MCP 服務器到本地配置，可以快速添加常用的 MCP 工具。\n所有服務默認禁用，導入後可手動啟用。", "errorDetails": "錯誤詳情", "errors": "錯誤 {count} 個", "fetchingServers": "正在獲取 MCP 服務器列表...", "imported": "已導入 {count} 個服務", "importingServers": "正在導入服務器配置...", "invalidServerData": "無效的服務器數據", "noApiKey": "請先配置 ModelScope API Key", "noOperationalUrls": "未找到可用的運營地址", "noServersFound": "未找到可用的 MCP 服務", "pageNumber": "頁碼", "pageNumberPlaceholder": "請輸入頁碼", "pageSize": "每頁數量", "serverAlreadyExists": "服務器已存在，跳過導入", "skipped": "跳過 {count} 個服務", "sync": "開始同步", "syncComplete": "同步完成", "syncing": "同步中...", "title": "同步 MCP 服務"}, "name": "ModelScope", "networkError": "網絡連接錯誤", "notConnected": "未連接", "verifyFailed": "驗證失敗", "verifySuccess": "驗證成功"}, "operationFailed": "操作失敗", "operationSuccess": "操作成功", "settingsApplied": "設置已應用", "bedrockLimitTip": "* 僅支持 Anthropic Claude（包括 Opus，Sonnet，Haiku 模型）", "bedrockVerifyTip": "DeepChat 使用 Claude 3.5 Sonnet 模型驗證，若您無此模型的調用權限驗證將失敗。這不會影響其他模型的使用。"}, "knowledgeBase": {"fastgptTitle": "FastGPT知識庫", "fastgptDescription": "FastGPT是一個強大的知識庫管理系統，支援多種檢索方式和文件管理功能。", "addFastGptConfig": "新增FastGPT設定", "editFastGptConfig": "編輯FastGPT設定", "title": "知識庫設置", "addKnowledgeBase": "添加知識庫", "selectKnowledgeBaseType": "請選擇要添加的知識庫類型", "difyDescription": "Dify知識庫可以幫助您管理和使用文檔數據", "comingSoon": "即將推出", "featureNotAvailable": "該功能暫未開放，敬請期待", "addDifyConfig": "添加Dify配置", "apiKey": "API密鑰", "datasetId": "數據集ID", "endpoint": "API地址", "configAdded": "配置已添加", "configAddedDesc": "{name}配置已成功添加", "addConfig": "添加配置", "moreComingSoon": "更多知識庫類型即將推出", "configUpdated": "配置已更新", "configUpdatedDesc": "{name}配置已成功更新", "descriptionPlaceholder": "例如：公司產品文檔知識庫", "ragflowTitle": "RAGFlow 知識庫", "ragflowDescription": "RAGFlow 是一個強大的知識庫管理系統，支援多種檢索方式和文件管理功能。", "addRagflowConfig": "新增 RAGFlow 設定", "editRagflowConfig": "編輯 RAGFlow 設定", "dify": "Dify知識庫", "editDifyConfig": "修改Dify配置", "builtInKnowledgeDescription": "內置知識庫提供了一些簡單實現，能夠在離線環境下實現部分基礎功能。", "builtInKnowledgeTitle": "內置知識庫", "addBuiltinKnowledgeConfig": "添加內置知識庫配置", "editBuiltinKnowledgeConfig": "編輯內置知識庫配置", "chunkSize": "分塊大小", "chunkSizeHelper": "將文檔切割分段，每段的大小，不能超過模型上下文限制", "chunkOverlap": "重疊大小", "chunkOverlapHelper": "相鄰文本塊之間重複的內容量，確保分段後的文本塊之間仍然有上下文聯繫，提升模型處理長文本的整體效果", "selectEmbeddingModel": "選擇嵌入模型", "modelNotFound": "服務商 {provider} 或模型 {model} 未找到", "modelNotFoundDesc": "請確保已正確配置模型，並且模型處於啟用狀態。\n您可以在服務商設置中檢查模型配置。", "removeBuiltinKnowledgeConfirmDesc": "刪除內置知識庫配置將會刪除所有相關數據，且無法恢復，請謹慎操作。", "removeBuiltinKnowledgeConfirmTitle": "確認刪除內置知識庫 {name} 嗎？", "descriptionDesc": "知識庫的描述，以便 AI 決定是否檢索此知識庫", "advanced": "高級選項", "autoDetectDimensions": "自動檢測嵌入維度", "autoDetectHelper": "自動檢測嵌入維度，會消耗少量 Tokens", "chunkOverlapPlaceholder": "默認值，不建議修改", "chunkSizePlaceholder": "默認值，不建議修改", "dimensions": "嵌入維度", "dimensionsPlaceholder": "嵌入維度大小，如 1024", "selectEmbeddingModelHelper": "嵌入模型在知識庫創建後禁止修改", "dimensionsHelper": "請確保模型支持所設置的嵌入維度大小", "autoDetectDimensionsError": "自動檢測嵌入維度失敗", "fragmentsNumber": "請求文檔片段數量", "fragmentsNumberHelper": "請求文檔片段數量越多，附帶的信息越多，但需要消耗的token也越多", "selectRerankModel": "選擇重排序模型", "rerankModel": "重排序模型", "embeddingModel": "嵌入模型", "return": "返回", "uploadHelper": "點擊上傳或拖拽文件到此處", "fileSupport": "支持 {accept} 等 {count} 種格式", "searchKnowledge": "搜索知識庫", "searchKnowledgePlaceholder": "請輸入查詢內容", "noData": "暫無數據", "file": "文件", "uploadProcessing": "上傳中", "uploadCompleted": "上傳完成", "reAdd": "重新上傳", "uploadError": "上傳失敗", "delete": "刪除", "reason": "原因", "deleteSuccess": "刪除成功", "copy": "複製", "copySuccess": "複製成功", "source": "來源", "normalized": "L2 歸一化", "normalizedHelper": "請確認模型支持對輸出向量進行 L2 歸一化", "dialog": {"beforequit": {"cancel": "取消", "confirm": "確認", "title": "退出確認", "description": "有正在運行的知識庫任務，是否確認退出軟件？\n被中止的任務可在重啟軟件後恢復。"}}, "searchError": "查詢失敗", "processing": "正在上傳", "paused": "上傳暫停", "unknown": "未知狀態", "reAddFile": {"title": "重新上傳確認", "content": "是否確認重新上傳文件 \"{fileName}\"？"}, "deleteFile": {"title": "刪除文件確認", "content": "是否確認刪除文件 \"{fileName}\"？\n此操作不可恢復。"}, "resumeAllPausedTasks": "一鍵恢復", "pauseAllRunningTasks": "一鍵暫停", "separators": "塊分隔符", "separatorsHelper": "文檔切分分隔符，單個分隔符用半角符號雙引號(\"\")包裹，分隔符之間用半角符號逗號(,)分割", "invalidSeparators": "無效的分隔符", "selectLanguage": "選擇預設", "separatorsPreset": "加載預設", "promptManagement": {"title": "Prompt管理", "description": "管理和使用自定義Prompt模板，幫助您更好地與AI互動"}}, "mcp": {"title": "MCP設置", "description": "管理和配置MCP（Model Context Protocol）服務器和工具", "enabledTitle": "啟用MCP", "enabledDescription": "啟用或禁用MCP功能和工具", "enableToAccess": "請先啟用MCP以訪問配置選項", "tabs": {"servers": "服務器", "tools": "工具", "prompts": "提示詞", "resources": "資源"}, "serverList": "服務器列表", "totalServers": "服務器總數", "addServer": "添加服務器", "running": "運行中", "stopped": "已停止", "stopServer": "停止服務器", "startServer": "啟動服務器", "noServersFound": "未找到服務器", "addServerDialog": {"title": "添加服務器", "description": "配置新的MCP服務器"}, "editServerDialog": {"title": "編輯服務器", "description": "編輯MCP服務器配置"}, "serverForm": {"name": "伺服器名稱", "namePlaceholder": "輸入伺服器名稱", "nameRequired": "伺服器名稱不能為空", "type": "伺服器類型", "typePlaceholder": "選擇伺服器類型", "typeStdio": "標準輸入輸出(Stdio)", "typeSse": "服務器發送事件(SSE)", "typeInMemory": "記憶體", "baseUrl": "基礎URL", "baseUrlPlaceholder": "輸入伺服器基礎URL（如：http://localhost:3000）", "command": "命令", "commandPlaceholder": "輸入命令", "commandRequired": "命令不能為空", "args": "參數", "argsPlaceholder": "輸入參數，用空格分隔", "argsRequired": "參數不能為空", "env": "環境變量", "envPlaceholder": "輸入JSON格式的環境變量", "envInvalid": "環境變量必須是有效的JSON格式", "description": "描述", "descriptionPlaceholder": "輸入伺服器描述", "descriptions": "描述", "descriptionsPlaceholder": "輸入伺服器描述", "icon": "圖標", "iconPlaceholder": "輸入圖標", "icons": "圖標", "iconsPlaceholder": "輸入圖標", "autoApprove": "自動授權", "autoApproveAll": "全部", "autoApproveRead": "讀取", "autoApproveWrite": "寫入", "autoApproveHelp": "選擇需要自動授權的操作類型，無需用戶確認即可執行", "submit": "提交", "add": "添加", "update": "更新", "cancel": "取消", "jsonConfigIntro": "您可以直接粘貼JSON配置或選擇手動配置伺服器。", "jsonConfig": "JSON配置", "jsonConfigPlaceholder": "請粘貼MCP伺服器的JSON格式配置", "jsonConfigExample": "JSON配置示例", "parseSuccess": "配置解析成功", "configImported": "配置導入成功", "parseError": "解析錯誤", "skipToManual": "跳過至手動配置", "parseAndContinue": "解析並繼續", "jsonParseError": "JSON解析失敗", "typeHttp": "可流式傳輸的HTTP請求(HTTP)", "browseMarketplace": "瀏覽MCP服務市場", "imageModel": "選擇視覺模型", "customHeadersParseError": "自定義Header解析失敗", "customHeaders": "自定義請求頭", "invalidKeyValueFormat": "錯誤的請求頭格式，請檢查輸入是否正確", "npmRegistry": "自定義NPM Registry", "npmRegistryPlaceholder": "設置自定義NPM Registry,留空系統會自動選擇最快的一個", "browseHigress": "瀏覽 Higress MCP 市場", "selectFolderError": "文件夾選擇錯誤", "folders": "允許訪問的文件夾", "addFolder": "添加文件夾", "noFoldersSelected": "未選擇任何文件夾", "useE2B": "啟用E2B沙盒", "e2bDescription": "使用E2B沙盒執行Python代碼", "e2bApiKey": "E2B ApiKey", "e2bApiKeyPlaceholder": "這裡輸入你的E2B Api Keys，如 e2b_1111xx*****", "e2bApiKeyHelp": "前往 e2b.dev 獲取你的 ApiKey", "e2bApiKeyRequired": "啟用E2B功能必須要輸入 ApiKey", "clickToEdit": "點擊編輯以查看完整內容"}, "deleteServer": "刪除伺服器", "editServer": "編輯伺服器", "setDefault": "設為默認", "removeDefault": "移除默認", "isDefault": "默認伺服器", "default": "默認", "setAsDefault": "設為默認", "removeServer": "刪除伺服器", "autoStart": "自啟動", "confirmRemoveServer": "確定要刪除伺服器 {name} 嗎？此操作無法撤銷。", "removeServerDialog": {"title": "刪除伺服器"}, "confirmDelete": {"title": "確認刪除", "description": "確定要刪除伺服器 {name} 嗎？此操作無法撤銷。", "confirm": "刪除", "cancel": "取消"}, "resetToDefault": "恢復預設服務", "resetConfirmTitle": "恢復預設服務", "resetConfirmDescription": "此操作將恢復所有預設伺服器，同時保留您自定義的伺服器。對預設伺服器的任何修改將會丟失。", "resetConfirm": "恢復", "builtInServers": "內置服務", "customServers": "自定義服務", "builtIn": "內置", "cannotRemoveBuiltIn": "無法刪除內置服務", "builtInServerCannotBeRemoved": "內置服務不能被刪除，僅支持修改參數和環境變量", "marketplace": "前往MCP市場一鍵安裝", "maxDefaultServersReached": "最多只能設置3個默認服務器", "removeDefaultFirst": "請先移除一些默認服務器", "higressMarket": "前往 Higress MCP 安裝", "npmRegistry": {"title": "NPM源配置", "currentSource": "當前源", "cached": "緩存", "lastChecked": "上次檢測", "refresh": "刷新", "advanced": "高級", "advancedSettings": "高級設置", "advancedSettingsDesc": "配置NPM源的高級選項，包括自動檢測和自定義源設置", "autoDetect": "自動檢測最優源", "autoDetectDesc": "啟動時自動檢測並使用最快的NPM源", "customSource": "自定義源", "customSourcePlaceholder": "輸入自定義NPM源地址", "currentCustom": "當前自定義源", "justNow": "剛剛", "minutesAgo": "{minutes}分鐘前", "hoursAgo": "{hours}小時前", "daysAgo": "{days}天前", "refreshSuccess": "NPM源刷新成功", "refreshSuccessDesc": "已重新檢測並更新最優NPM源", "refreshFailed": "NPM源刷新失敗", "autoDetectUpdated": "自動檢測設置已更新", "autoDetectEnabled": "已啟用自動檢測最優NPM源", "autoDetectDisabled": "已禁用自動檢測，將使用默認源", "updateFailed": "設置更新失敗", "customSourceSet": "自定義源已設置", "customSourceSetDesc": "已設置自定義NPM源：{registry}", "customSourceCleared": "自定義源已清除", "customSourceClearedDesc": "已清除自定義NPM源，將使用自動檢測", "invalidUrl": "無效的URL", "invalidUrlDesc": "請輸入有效的HTTP或HTTPS地址", "testing": "正在測試NPM源", "testingDesc": "正在測試源 {registry} 的連通性...", "testFailed": "NPM源測試失敗", "testFailedDesc": "無法連接到 {registry}，錯誤：{error}。請檢查URL是否正確或網絡連接。", "redetectingOptimal": "正在重新檢測最優NPM源...", "redetectComplete": "重新檢測完成", "redetectCompleteDesc": "已檢測並設置當前最優的NPM源", "redetectFailed": "重新檢測失敗", "redetectFailedDesc": "無法重新檢測最優源，將使用默認配置"}, "technicalDetails": "技術詳情", "httpServer": "HTTP服務器", "localProcess": "本地進程", "restartServer": "重啟服務器", "viewLogs": "查看日誌", "starting": "啟動中", "error": "錯誤"}, "about": {"title": "關於我們", "version": "版本", "checkUpdate": "檢查更新", "checking": "檢查中...", "latestVersion": "已是最新版本"}, "display": {"fontSize": "文字大小", "text-2xl": "超大", "text-base": "預設", "text-lg": "大", "text-sm": "小", "text-xl": "特大", "floatingButton": "懸浮按鈕", "floatingButtonDesc": "在桌面顯示一個懸浮按鈕，可以快速喚起應用程式視窗"}, "shortcuts": {"title": "快捷鍵設置", "pressKeys": "輸入快捷鍵", "pressEnterToSave": "按Enter保存，Esc取消", "noModifierOnly": "不能僅使用修飾鍵作為快捷鍵", "keyConflict": "快捷鍵衝突，請選擇其他組合", "clearShortcut": "清除快捷鍵", "cleanHistory": "清除聊天歷史", "deleteConversation": "刪除對話", "goSettings": "打開設置", "hideWindow": "隱藏窗口", "quitApp": "退出程序", "zoomIn": "放大字體", "zoomOut": "縮小字體", "zoomReset": "重置字體", "closeTab": "關閉當前標籤頁", "newTab": "新建標籤頁", "newWindow": "打開新窗口", "showHideWindow": "顯示/隱藏窗口", "newConversation": "新會話", "lastTab": "切換到最後一個標籤頁", "previousTab": "切換到上一個標籤頁", "specificTab": "切換到指定標籤頁 (1-8)", "nextTab": "切換到下一個標籤頁"}, "rateLimit": {"title": "速率限制", "description": "控制請求時間間隔，避免超出 API 限制", "intervalLimit": "請求間隔", "intervalUnit": "秒", "intervalHelper": "兩個請求之間的最小間隔，不需要時請關閉速率限制", "lastRequestTime": "最後請求", "queueLength": "佇列長度", "nextAllowedTime": "下次允許請求", "never": "從未", "justNow": "剛才", "secondsAgo": "秒前", "minutesAgo": "分鐘前", "immediately": "立即", "secondsLater": "秒後", "confirmDisableTitle": "確認關閉速率限制", "confirmDisableMessage": "該值不能小於或等於0，是否關閉速率限制功能？", "confirmDisable": "關閉限制", "disabled": "速率限制已關閉", "disabledDescription": "速率限制功能已關閉"}}