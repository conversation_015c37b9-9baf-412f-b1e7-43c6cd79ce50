{"title": "Paramètres", "common": {"title": "Paramètres généraux", "modelSettings": "Paramètres du modèle", "searchSettings": "Paramètres de recherche", "networkSettings": "Paramètres réseau", "interfaceSettings": "Interface et interaction", "personalizationSettings": "Paramètres de personnalisation", "securitySettings": "Sécurité et confidentialité", "operationSettings": "Paramètres d'opération", "resetData": "Réinitialiser les données", "language": "<PERSON><PERSON>", "languageSelect": "Sélectionner la langue", "searchEngine": "Moteur de recherche", "searchEngineDesc": "Sélectionnez un moteur de recherche pour l'amélioration de la recherche web. Prend en charge les principaux moteurs de recherche comme Google, Bing et les moteurs de recherche personnalisés", "searchAssistantModel": "<PERSON><PERSON><PERSON><PERSON> adjoint", "searchAssistantModelDesc": "Choisissez le modèle pour traiter les résultats de recherche et générer des réponses. Affecte la qualité et la vitesse de l'amélioration de la recherche", "searchPreview": "Aperçu de la recherche", "searchPreviewDesc": "Activez les vignettes d'aperçu dans les pages de résultats de recherche pour une navigation rapide du contenu de recherche", "proxyMode": "Mode proxy", "proxyModeDesc": "Configurez les paramètres de proxy r<PERSON><PERSON>. Choisis<PERSON>z le proxy système, aucun proxy ou un proxy personnalisé pour accéder aux services étrangers", "searchEngineSelect": "Sé<PERSON><PERSON>ner le moteur de recherche", "selectModel": "Sélectionner le modèle", "proxyModeSelect": "Sélectionner le mode proxy", "proxyModeSystem": "Proxy système", "proxyModeNone": "Pas de proxy", "proxyModeCustom": "Proxy personnalis<PERSON>", "customProxyUrl": "URL du proxy personnalisé", "customProxyUrlPlaceholder": "Exemple : http://127.0.0.1:7890", "invalidProxyUrl": "URL de proxy invalide, veuil<PERSON>z entrer une URL http/https valide", "addCustomSearchEngine": "Ajouter un moteur de recherche personnalisé", "addCustomSearchEngineDesc": "Ajoutez un nouveau moteur de recherche en fournissant un nom et une URL de recherche. L'URL doit inclure {query} comme espace réservé pour la requête.", "searchEngineName": "Nom du moteur de recherche", "searchEngineNamePlaceholder": "Entrez le nom du moteur de recherche", "searchEngineUrl": "URL de recherche", "searchEngineUrlPlaceholder": "Ex : https://a.com/search?q={'{'}query{'}'}", "searchEngineUrlError": "L'URL doit inclure {'{'}query{'}'} comme espace réservé pour la requête", "deleteCustomSearchEngine": "Supprimer le moteur de recherche personnalisé", "deleteCustomSearchEngineDesc": "Êtes-vous sûr de vouloir supprimer le moteur de recherche personnalisé \"{name}\" ? Cette action ne peut pas être annulée.", "testSearchEngine": "Tester le moteur de recherche", "testSearchEngineDesc": "Une recherche test pour \"météo\" sera effectuée en utilisant le moteur de recherche {engine}.", "testSearchEngineNote": "Si la page de recherche nécessite une connexion ou d'autres actions, vous pouvez les effectuer dans la fenêtre de test. Veuillez fermer la fenêtre de test lorsque vous avez terminé.", "theme": "Thème", "themeSelect": "Sé<PERSON><PERSON><PERSON> le thème", "closeToQuit": "Quitter l'application lors de la fermeture de la fenêtre", "contentProtection": "Protection de l'écran", "contentProtectionDialogTitle": "Confirmer le changement de protection de l'écran", "contentProtectionEnableDesc": "L'activation de la protection de l'écran empêche les logiciels de partage d'écran de capturer la fenêtre DeepChat, protégeant ainsi la confidentialité de votre contenu. Notez que cette fonctionnalité ne masquera pas complètement toutes les interfaces. Veuillez utiliser cette fonctionnalité de manière responsable et conformément aux réglementations. De plus, tous les logiciels de partage d'écran ne prennent pas en charge cette fonctionnalité. Certains environnements peuvent laisser une fenêtre noire.", "contentProtectionDisableDesc": "La désactivation de la protection de l'écran permettra aux logiciels de partage d'écran de capturer la fenêtre DeepChat.", "contentProtectionRestartNotice": "La modification de ce paramètre redémarrera l'application. Voulez-vous continuer ?", "soundEnabled": "<PERSON><PERSON> le son", "copyWithCotEnabled": "Copier les infos COT", "loggingEnabled": "Activer la journalisation", "loggingDialogTitle": "Confirmer le changement de paramètre de journalisation", "loggingEnableDesc": "L'activation de la journalisation nous aidera à diagnostiquer les problèmes et à améliorer l'application. Les fichiers journaux peuvent contenir des informations sensibles.", "loggingDisableDesc": "La désactivation de la journalisation arrêtera la collecte des journaux d'application.", "loggingRestartNotice": "La modification de ce paramètre redémarrera l'application. Voulez-vous continuer ?", "openLogFolder": "<PERSON><PERSON><PERSON><PERSON><PERSON> le dossier des journaux", "shortcut": {"newChat": "<PERSON><PERSON><PERSON> une nouvelle conversation", "title": "Paramètres des raccourcis clavier"}, "notifications": "Notifications système", "notificationsDesc": "Quand Deep<PERSON>hat est en arrière‑plan, envoyer une notification à la fin d’une réponse"}, "data": {"title": "Paramètres des données", "syncEnable": "Activer la synchronisation des données", "syncFolder": "Dossier de synchronisation", "openSyncFolder": "Ou<PERSON><PERSON>r le dossier de synchronisation", "lastSyncTime": "Dernière synchronisation", "never": "<PERSON><PERSON>", "startBackup": "Sauvegarder maintenant", "backingUp": "Sauvegarde en cours...", "importData": "Importer des données", "incrementImport": "Import incrémental", "overwriteImport": "Import avec écrasement", "importConfirmTitle": "Confirmer l'importation des données", "importConfirmDescription": "L'importation écrasera toutes les données actuelles, y compris l'historique des conversations et les paramètres. Assurez-vous d'avoir sauvegardé les données importantes. Vous devrez redémarrer l'application après l'importation.", "importing": "Importation en cours...", "confirmImport": "Confirmer l'importation", "importSuccessTitle": "Importation réussie", "importErrorTitle": "Échec de l'importation", "resetData": "Réinitialiser les données", "resetConfirmTitle": "Confirmer la réinitialisation des données", "resetConfirmDescription": "Veuillez sélectionner le type de données à réinitialiser. Cette opération est irréversible et l'application redémarrera automatiquement après la réinitialisation.", "resetChatData": "Réinitialiser les données de chat", "resetChatDataDesc": "Supp<PERSON>er tout l'historique des conversations et les enregistrements de dialogue", "resetKnowledgeData": "Réinitialiser les données de base de connaissances", "resetKnowledgeDataDesc": "Supprimer tous les fichiers de base de connaissances et les données vectorielles", "resetConfig": "Réinitialiser la configuration", "resetConfigDesc": "Supprimer tous les paramètres de l'application, les configurations de modèles et les prompts personnalisés", "resetAll": "Réinitialisation complète", "resetAllDesc": "Supprimer toutes les données, y compris l'historique des conversations, les configurations et les fichiers de cache", "resetting": "Réinitialisation en cours...", "confirmReset": "Confirmer la réinitialisation", "resetCompleteDevTitle": "Réinitialisation des données terminée", "resetCompleteDevMessage": "En environnement de développement, ve<PERSON><PERSON>z redémarrer l'application manuellement. Arrêtez le processus actuel et exécutez à nouveau pnpm run dev"}, "model": {"title": "Paramètres du modèle", "systemPrompt": {"label": "Prompt système", "placeholder": "<PERSON><PERSON><PERSON><PERSON> entrer le prompt système...", "description": "Définissez le prompt système pour l'assistant IA afin de définir son comportement et son rôle"}, "temperature": {"label": "Température du modèle", "description": "Contrôle l'aléatoire de la sortie ; des valeurs plus élevées produisent des réponses plus créatives"}, "contextLength": {"label": "<PERSON><PERSON><PERSON> du contexte", "description": "Définir la longueur maximale du contexte de la conversation"}, "responseLength": {"label": "Longueur de la réponse", "description": "Définir la longueur maximale de la réponse de l'IA"}, "artifacts": {"description": "Activer Artifacts pour permettre à l’IA de générer un contenu plus riche", "title": "Artifacts"}, "addModel": "Ajouter un modèle", "configureModel": "Configurer le modèle", "modelList": "Liste des modèles", "provider": "Fournisseur de service", "providerSetting": "Paramètres du fournisseur", "selectModel": "Sélectionner un modèle", "visionModel": "Mod<PERSON>le de vision", "visionModelDesc": "Sélectionnez un modèle pour traiter les images et le contenu visuel, utilisé pour la description d'images, l'analyse et d'autres fonctionnalités", "visionModelNotSelectedWarning": "Aucun modèle de vision sélectionné. Vous serez invité à en configurer un lors de l'utilisation des fonctionnalités d'image", "modelConfig": {"cancel": "Annuler", "contextLength": {"description": "Définir la longueur de contexte que le modèle peut gérer", "label": "<PERSON><PERSON><PERSON> du contexte"}, "description": "Veuillez noter que cette configuration n'est valable que pour le modèle actuel et n'affectera pas d'autres modèles. Veuillez le modifier avec prudence. Des paramètres incorrects peuvent faire en sorte que le modèle ne fonctionne pas correctement.", "functionCall": {"description": "Si le modèle prend en charge les appels de fonction nativement (Deepchat simulera automatiquement les appels de fonction après avoir désactivé cette option)", "label": "Appels de fonction"}, "maxTokens": {"description": "Définissez le nombre maximum de jetons pour une seule sortie du modèle", "label": "Longueur de sortie maximale"}, "reasoning": {"description": "Le modèle prend-il en charge la capacité de raisonnement?", "label": "Capacité de raisonnement"}, "thinkingBudget": {"label": "Budget de Réflexion", "description": "Limiter la longueur de réflexion du modèle", "dynamic": "Réflexion Dynamique", "range": "Plage: {min} - {max}", "onlySupported": "Pris en charge uniquement dans Gemini 2.5 Flash, 2.5 Pro et 2.5 Flash-Lite", "valueLabel": "Valeur du Budget de Réflexion", "placeholder": "Entrez la valeur du budget de réflexion", "forceEnabled": "Les modèles de la série Gemini 2.5 activent de force le budget de réflexion", "dynamicPrefix": "-1 = Réflexion Dynamique", "notice": "Attention : ", "warnings": {"proNoDisable": "Ce modèle ne prend pas en charge la désactivation de la réflexion, valeur minimale 128", "proCannotDisable": "Gemini 2.5 Pro ne peut pas désactiver la fonction de réflexion", "flashLiteCannotSetZero": "Gemini 2.5 Flash-<PERSON><PERSON> ne peut pas être défini à 0, la valeur minimale est 512", "modelCannotDisable": "Ce modèle ne peut pas désactiver la fonction de réflexion", "flashLiteMinValue": "Le budget de réflexion ne peut pas être inférieur à 512 lorsqu'il est défini sur une valeur spécifique (ou utilisez 0 pour désactiver la réflexion, -1 pour activer la réflexion dynamique)", "belowMin": "Le budget de réflexion ne peut pas être inférieur à {min}{hint}", "aboveMax": "Le budget de réflexion ne peut pas être supérieur à {max}"}, "hints": {"flashLiteDisable": ", 0 = d<PERSON><PERSON><PERSON> la réflexion, valeur spécifique minimum 512", "normalDisable": ", 0 = d<PERSON><PERSON>r la réflexion", "withZeroAndDynamic": "(ou utilisez 0 pour désactiver la réflexion, -1 pour activer la réflexion dynamique)", "withDynamic": "(ou utilisez -1 pour activer la réflexion dynamique)"}}, "resetConfirm": {"confirm": "Confirmer la réinitialisation", "message": "Êtes-vous sûr de souhaiter réinitialiser la configuration de ce modèle par défaut? Cette opération est irrévocable.", "title": "Confirmer la réinitialisation"}, "reasoningEffort": {"label": "E<PERSON>ort de raisonnement", "description": "Contrôle la profondeur du raisonnement du modèle ; un effort plus élevé produit de meilleurs résultats mais des réponses plus lentes", "placeholder": "Sélectionner l'effort de raisonnement", "options": {"minimal": "Minimal - Réponse la plus rapide", "low": "Low - Faible effort", "medium": "Medium - <PERSON><PERSON><PERSON> m<PERSON>", "high": "<PERSON> <PERSON> <PERSON><PERSON><PERSON>"}}, "verbosity": {"label": "Verbosité", "description": "Contrôle le niveau de détail et la longueur des réponses du modèle", "placeholder": "Sélectionner le niveau de verbosité", "options": {"low": "Low - Réponses concises", "medium": "Medium - Détail <PERSON>", "high": "High - Réponses détaillées"}}, "resetToDefault": "Réinitialiser à la valeur par défaut", "saveConfig": "Enregistrer la configuration", "useModelDefault": "Utiliser la configuration par défaut du modèle", "currentUsingModelDefault": "Utilise actuellement la configuration par défaut du modèle", "temperature": {"description": "Contrôle l’aléatoire de la sortie. Valeur plus élevée = plus aléatoire.", "label": "Température"}, "title": "Paramètres du modèle personnalisé", "type": {"description": "Sélectionnez le type de modèle", "label": "Type de modèle", "options": {"chat": "<PERSON><PERSON><PERSON><PERSON>", "embedding": "Mod<PERSON>le d'intégration", "imageGeneration": "Modèle de génération d'images", "rerank": "<PERSON><PERSON><PERSON><PERSON>"}}, "validation": {"contextLengthMax": "La longueur du contexte ne peut pas dépasser 10000000", "contextLengthMin": "La longueur du contexte doit être supérieure à 0", "contextLengthRequired": "La longueur du contexte ne peut pas être vide", "maxTokensMax": "La longueur de sortie maximale ne peut pas dépasser 1000000", "maxTokensMin": "La longueur de sortie maximale doit être supérieure à 0", "maxTokensRequired": "La longueur de sortie maximale ne peut pas être vide", "temperatureMax": "La température doit être inférieure ou égale à 2", "temperatureMin": "La température doit être supérieure ou égale à 0", "temperatureRequired": "La température ne peut pas être vide"}, "vision": {"description": "Le modèle soutient-il la capacité visuelle?", "label": "Capacité visuelle"}}}, "provider": {"search": "Rechercher des plateformes de fournisseurs…", "enable": "Activer le service", "enabled": "Activé", "disabled": "Désactivé", "urlPlaceholder": "Veuillez entrer l'URL de l'API", "keyPlaceholder": "Veuillez entrer la clé API", "accessKeyIdPlaceholder": "Veuillez entrer l'AWS Access Key ID", "secretAccessKeyPlaceholder": "Veuillez entrer l'AWS Secret Access Key", "regionPlaceholder": "Veuillez entrer la région AWS", "verifyKey": "Vérifier la clé", "howToGet": "Comment obtenir", "getKeyTip": "<PERSON><PERSON><PERSON><PERSON> visiter", "getKeyTipEnd": "pour obtenir la clé API", "urlFormat": "Exemple d'API : {defaultUrl}", "modelList": "Liste des modèles", "enableModels": "<PERSON><PERSON> les modèles", "disableAllModels": "Désactiver tous les modèles", "modelsEnabled": "Les modèles ont été activés", "noModelsEnabled": {"title": "Aucun modèle activé", "description": "Veuillez cliquer sur le bouton « Activer les modèles » pour sélectionner manuellement les modèles que vous souhaitez utiliser."}, "verifyLink": "Vérifier le lien", "syncModelsFailed": "Échec de la synchronisation des modèles...", "addCustomProvider": "Ajouter un fournisseur personnalisé", "delete": "<PERSON><PERSON><PERSON><PERSON>", "stopModel": "<PERSON><PERSON><PERSON><PERSON> le modè<PERSON>", "pulling": "Récupération...", "runModel": "Exécuter le modèle", "dialog": {"disableModel": {"title": "Confirmer la désactivation du modèle", "content": "Êtes-vous sûr de vouloir dés<PERSON>r le modèle \"{name}\" ?", "confirm": "Désactiver"}, "disableAllModels": {"title": "Confirmer la désactivation de tous les modèles", "content": "Êtes-vous sûr de vouloir dés<PERSON>r tous les modèles ?", "confirm": "<PERSON><PERSON><PERSON><PERSON> tout"}, "configModels": {"title": "Configurer la liste des modèles", "description": "Sélectionner les modèles à activer ou désactiver"}, "verify": {"missingFields": "Veuillez entrer la clé API et l'URL de l'API", "failed": "Échec de la vérification", "success": "Vérification réussie", "failedDesc": "La vérification de la clé API ou de la configuration a échoué, veuillez vérifier vos paramètres", "successDesc": "La clé API et la configuration ont été vérifiées avec succès, prêt à utiliser", "connectionError": "Erreur de connexion, veuillez vérifier la connexion réseau et l'adresse de l'API", "serverError": "<PERSON><PERSON><PERSON> du serveur, ve<PERSON><PERSON><PERSON> rées<PERSON>er plus tard", "unauthorized": "Échec de l'authentification, la clé API n'est pas valide ou expirée"}, "addCustomProvider": {"title": "Ajouter un fournisseur personnalisé", "description": "<PERSON><PERSON><PERSON>z remplir les informations nécessaires pour le fournisseur", "name": "Nom", "namePlaceholder": "Veuillez entrer le nom du fournisseur", "apiType": "Type d'API", "apiTypePlaceholder": "Veuillez sélectionner le type d'API", "apiKey": "Clé API", "apiKeyPlaceholder": "Veuillez entrer la clé API", "baseUrl": "URL de base de l'API", "baseUrlPlaceholder": "Veuillez entrer l'URL de base de l'API", "enable": "<PERSON><PERSON> le four<PERSON>"}, "deleteProvider": {"title": "Confirmer la <PERSON> du fournisseur", "content": "Êtes-vous sûr de vouloir supprimer le fournisseur \"{name}\" ? Cette action ne peut pas être annulée.", "confirm": "<PERSON><PERSON><PERSON><PERSON>"}, "deleteModel": {"title": "Confirmer la suppression du modèle", "content": "Êtes-vous sûr de vouloir supprimer le modèle \"{name}\" ? Cette action ne peut pas être annulée.", "confirm": "<PERSON><PERSON><PERSON><PERSON>"}, "pullModel": {"title": "Récupérer le modèle", "description": "Sélectionner les modèles à télécharger localement", "pull": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "modelCheck": {"checking": "<PERSON><PERSON><PERSON>...", "description": "Sélectionnez un modèle pour les tests de connectivité et d'utilisation", "failed": "Échec du test du modèle", "model": "Sélectionnez un modèle", "modelPlaceholder": "Veuillez sélectionner le modèle à tester", "noModels": "Il n'y a aucun modèle disponible pour ce fournisseur de services", "success": "Le test du modèle a réussi", "test": "Commencer le test", "title": "<PERSON><PERSON><PERSON> de modè<PERSON>"}}, "pullModels": "Récupérer les modèles", "refreshModels": "Actualiser les modèles", "modelsRunning": "Modèles en cours d'exécution", "runningModels": "Modèles en cours d'exécution", "noRunningModels": "Aucun modèle en cours d'exécution", "deleteModel": "Supp<PERSON><PERSON> le modèle", "deleteModelConfirm": "Êtes-vous sûr de vouloir supprimer le modèle \"{name}\" ? Cette action ne peut pas être annulée.", "noLocalModels": "Aucun modèle local", "localModels": "<PERSON><PERSON><PERSON><PERSON>", "azureApiVersion": "Version API", "safety": {"title": "Paramètres de sécurité", "blockHighest": "Bloquer un risque élevé", "blockMost": "Bloquer les risques moyens", "blockNone": "Non bloqué", "blockSome": "Bloquer un faible risque"}, "serverList": "Liste des serveurs", "totalServers": "Total des serveurs", "addServer": "Ajouter un serveur", "autoStart": "S'opiniter", "githubCopilotAuth": "Authentification GitHub Copilot", "githubCopilotConnected": "GitHub Copilot connecté", "githubCopilotNotConnected": "GitHub Copilot non connecté", "loginWithGitHub": "Se connecter avec GitHub", "loggingIn": "Connexion en cours...", "githubCopilotLoginTip": "Autorisez DeepChat à accéder à votre abonnement GitHub Copilot. Les autorisations 'read:user' et 'read:org' sont requises pour accéder à l'API Copilot.", "loginSuccess": "Connexion réussie", "loginFailed": "Échec de la connexion", "tokenValid": "Le jeton est valide", "tokenInvalid": "Le jeton n'est pas valide", "disconnect": "Déconnecter", "disconnected": "Déconnecté avec succès", "disconnectFailed": "Échec de la déconnexion", "keyStatus": {"remaining": "<PERSON><PERSON> restant", "usage": "<PERSON><PERSON><PERSON><PERSON>"}, "refreshingModels": "Rafraî<PERSON>ssant...", "toast": {"modelRunning": "Le modèle est en cours d'exécution", "modelRunningDesc": "Veuillez d'abord arrêter le modèle {model}, puis le supprimer."}, "anthropicApiKeyTip": "Veuillez aller à la console anthropique pour obtenir votre clé API", "anthropicConnected": "Connecté anthropique", "anthropicNotConnected": "Anthropique non connecté", "anthropicOAuthTip": "Cliquez sur Autoriser Deepchat pour accéder à votre compte anthropique", "oauthLogin": "Connexion OAuth", "authMethod": "Méthode de certification", "authMethodPlaceholder": "Sélectionner la méthode d'authentification", "apiKeyLabel": "Clé API", "apiUrlLabel": "URL API", "anthropicOAuthFlowTip": "Le système ouvrira automatiquement la fenêtre d'autorisation. Veuillez revenir et saisir le code d'autorisation après l'autorisation.", "anthropicBrowserOpened": "Le navigateur externe est ouvert", "anthropicCodeInstruction": "Veuillez compléter l'autorisation dans un navigateur externe et coller le code d'autorisation obtenu dans la zone d'entrée ci-dessous", "browserOpenedSuccess": "Le navigateur externe est ouvert, veuillez compléter l'autorisation", "codeRequired": "Veuillez saisir le code d'autorisation", "inputOAuthCode": "Entrez le code d'autorisation", "codeExchangeFailed": "Échange de code d'autorisation Échec", "invalidCode": "Code d'autorisation non valide", "oauthCodeHint": "<PERSON><PERSON><PERSON>z coller le code d'autorisation ici après avoir terminé l'autorisation dans un navigateur externe", "oauthCodePlaceholder": "Veuillez saisir le code d'autorisation ...", "verifyConnection": "Vérifier la connexion", "manageModels": "<PERSON><PERSON><PERSON> les modèles", "anthropicOAuthActiveTip": "L'authentification OAuth est activée, vous pouvez utiliser les services Anthropic directement", "oauthVerifySuccess": "Connexion OAuth vérifiée avec succès", "oauthVerifyFailed": "Échec de la vérification de la connexion OAuth", "configurationSaved": "Configuration enregistrée", "configurationUpdated": "Configuration mise à jour", "dataRefreshed": "Les données ont été rafraîchies", "modelscope": {"apiKey": "Clé API", "apiKeyHelper": "Obtenez votre clé API dans la console Modelcope", "apiKeyPlaceholder": "Veuillez saisir la clé de l'API Modelscope", "baseUrl": "Adresse API", "baseUrlHelper": "Adresse du service API Modelcope", "connected": "Connecté", "connecting": "De liaison...", "description": "Modelcope est une plate-forme de partage modèle en tant que service lancée par Alibaba Damo Academy", "details": {"apiConfig": "Configuration de l'API", "mcpSync": "Synchronisation MCP", "modelManagement": "Gestion des modèles", "operationalDescription": "Synchroniser les serveurs MCP qui peuvent être utilisés directement sur la plate-forme Modelcope", "operationalServers": "Exploitation d'un serveur", "rateLimitConfig": "Configuration de la limite de taux", "safetySettings": "Paramètres de sécurité", "specialConfig": "Configuration spéciale", "syncFromModelScope": "Synchronisation de Modelscope", "title": "Détails des paramètres du fournisseur"}, "invalidKey": "Clé API non valide", "keyRequired": "Veuillez saisir la clé de l'API", "mcpSync": {"authenticationFailed": "Échec de l'authentification, veuillez vérifier la clé de l'API", "convertingServers": "Conversion de la configuration du serveur ...", "description": "La synchronisation d'un serveur MCP de Modelcope aux configurations locales vous permet d'ajouter rapidement des outils MCP couramment utilisés. Tous les services sont désactivés par défaut et peuvent être activés manuellement après l'importation.", "errorDetails": "<PERSON><PERSON><PERSON> d'erreur", "errors": "Erreur {count}", "fetchingServers": "Obtenir la liste des serveurs MCP ...", "imported": "{comte} Les services ont été importés", "importingServers": "Configuration d'importation de serveur ...", "invalidServerData": "Données de serveur non valides", "noApiKey": "Veuillez d'abord configurer la clé de l'API Modelcope", "noOperationalUrls": "Aucune adresse de fonctionnement disponible trouvée", "noServersFound": "Aucun service MCP disponible trouvé", "pageNumber": "numéro de page", "pageNumberPlaceholder": "Veuillez saisir le numéro de page", "pageSize": "Quantité par page", "serverAlreadyExists": "Le serveur existe déjà, sautez l'importation", "skipped": "Skip {count} Services", "sync": "Commencer à se synchroniser", "syncComplete": "Achèvement synchrone", "syncing": "Synchronisation...", "title": "Synchroniser les services MCP"}, "name": "Modelcope", "networkError": "Erreur de connexion réseau", "notConnected": "Non connecté", "verifyFailed": "Échec de la vérification", "verifySuccess": "La vérification est réussie"}, "operationFailed": "L'opération a échoué", "operationSuccess": "L'opération est réussie", "settingsApplied": "Paramètres appliqués", "bedrockLimitTip": "* Prend uniquement en charge Anthrop<PERSON> (y compris les modèles Opus, Sonnet, Haiku)", "bedrockVerifyTip": "DeepChat utilise Claude 3.5 Sonnet pour la vérification. Si vous n'avez pas l'autorisation de l'invoquer, la vérification échouera. Cela n'affectera pas l'utilisation des autres modèles."}, "knowledgeBase": {"title": "Paramètres de la base de connaissances", "addKnowledgeBase": "Ajouter une base de connaissances", "selectKnowledgeBaseType": "Veuillez sélectionner le type de base de connaissances à ajouter", "difyDescription": "La base de connaissances Dify vous aide à gérer et utiliser les données documentaires", "comingSoon": "Bientôt disponible", "featureNotAvailable": "Cette fonctionnalité n'est pas encore disponible", "addDifyConfig": "Ajouter une configuration Dify", "apiKey": "Clé API", "datasetId": "ID du jeu de données", "endpoint": "Point de terminaison API", "configAdded": "Configuration ajoutée", "configAddedDesc": "{name} La configuration a été ajoutée avec succès", "addConfig": "Ajouter une configuration", "moreComingSoon": "Plus de types de bases de connaissances à venir", "configUpdated": "Configuration mise à jour", "configUpdatedDesc": "{name} La configuration a été mise à jour avec succès", "descriptionPlaceholder": "Exemple : Base de connaissances des documents produits de l'entreprise", "ragflowTitle": "Base de connaissances RAGFlow", "ragflowDescription": "RAGFlow est un système puissant de gestion de base de connaissances qui prend en charge plusieurs méthodes de recherche et fonctionnalités de gestion de documents.", "addRagflowConfig": "Ajouter une configuration RAGFlow", "editRagflowConfig": "Modifier la configuration RAGFlow", "dify": "Base de connaissances Dify", "editDifyConfig": "Modifier la configuration dify", "fastgptTitle": "Base de connaissances FastGPT", "fastgptDescription": "FastGPT est un puissant système de gestion des connaissances prenant en charge plusieurs méthodes de recherche et des fonctionnalités de gestion de documents.", "addFastGptConfig": "Ajouter la configuration FastGPT", "editFastGptConfig": "Éditer la configuration FastGPT", "builtInKnowledgeDescription": "La base de connaissances intégrée fournit des implémentations simples qui permettent certaines fonctions de base dans un environnement hors ligne.", "builtInKnowledgeTitle": "Base de connaissances intégrée", "addBuiltinKnowledgeConfig": "Ajouter une configuration de base de connaissances intégrée", "editBuiltinKnowledgeConfig": "Modifier la configuration de base de connaissances intégrée", "chunkSize": "<PERSON><PERSON> de <PERSON>", "chunkSizeHelper": "Coupez le document en segments, la taille de chaque segment ne peut pas dépasser la limite de contexte du modèle", "chunkOverlap": "<PERSON><PERSON> de chevauche<PERSON>", "chunkOverlapHelper": "La quantité de contenu répété entre les blocs de texte adjacents garantit qu'il existe toujours une connexion contextuelle entre les blocs de texte segmentés, améliorant l'effet global du traitement du modèle de textes longs", "selectEmbeddingModel": "Sélectionnez un modèle intégré", "modelNotFound": "Fournisseur de services {provider} ou modèle {model} introuvable", "modelNotFoundDesc": "Assurez-vous que le modèle est configuré correctement et que le modèle est activé. Vous pouvez vérifier la configuration du modèle dans les paramètres du fournisseur de services.", "removeBuiltinKnowledgeConfirmDesc": "La suppression de la configuration de base de connaissances intégrée supprimera toutes les données pertinentes et ne peut pas être restaurée. Soyez prudent.", "removeBuiltinKnowledgeConfirmTitle": "Confirmer pour supprimer la base de connaissances intégrée {name}?", "descriptionDesc": "Description de la base de connaissances afin que l'IA décide de récupérer cette base de connaissances", "advanced": "Options avancées", "autoDetectDimensions": "Détecter automatiquement les dimensions intégrées", "autoDetectHelper": "Détecter automatiquement les dimensions intégrées, consomme une petite quantité de jetons", "chunkOverlapPlaceholder": "Valeur par défaut, aucune modification n'est recommandée", "chunkSizePlaceholder": "Valeur par défaut, aucune modification n'est recommandée", "dimensions": "Dimensions de l'incorporation", "dimensionsPlaceholder": "Taille de dimension intégrée, comme 1024", "selectEmbeddingModelHelper": "Les modèles d'intégration sont interdits après la création d'une base de connaissances", "dimensionsHelper": "Assurez-vous que le modèle prend en charge la taille de la dimension intégrée de l'ensemble", "autoDetectDimensionsError": "Détecter automatiquement la panne de dimension intégrée", "fragmentsNumber": "Nombre de fragments de documents demandés", "fragmentsNumberHelper": "Plus il y a de fragments de document demandé, plus il comporte d'informations, mais plus il doit être consommé de jetons", "selectRerankModel": "Sélectionnez le modèle de réorganisation", "rerankModel": "Réorganiser le modèle", "embeddingModel": "Mod<PERSON>le d'intégration", "return": "retour", "uploadHelper": "Cliquez pour télécharger ou faire glisser le fichier ici", "fileSupport": "Prend en charge {accept} et {count} autres formats", "searchKnowledge": "Rechercher la base de connaissances", "searchKnowledgePlaceholder": "Veuillez saisir le contenu de la requête", "noData": "Pas encore de données", "file": "document", "uploadProcessing": "Téléchargement", "uploadCompleted": "Téléchargement terminé", "reAdd": "Télécharger à nouveau", "uploadError": "Le téléchargement a échoué", "delete": "supprimer", "reason": "raison", "deleteSuccess": "Supprimer avec succès", "copy": "copie", "copySuccess": "<PERSON><PERSON><PERSON> avec succès", "source": "source", "normalized": "Normalisation L2", "normalizedHelper": "<PERSON><PERSON><PERSON>z confirmer que le modèle prend en charge la normalisation L2 des vecteurs de sortie", "dialog": {"beforequit": {"cancel": "Annuler", "confirm": "confirmer", "title": "Confirmation de sortie", "description": "Il y a une tâche de base de connaissances en cours. Êtes-vous sûr de quitter le logiciel? Les tâches abandonnées peuvent être restaurées après le redémarrage du logiciel."}}, "searchError": "La requête a échoué", "processing": "Téléchargement", "paused": "Téléchargement en pause", "unknown": "Statut inconnu", "reAddFile": {"title": "Re-télécharger la confirmation", "content": "Êtes-vous sûr de vouloir re-téléverser le fichier \"{fileName}\" ?"}, "deleteFile": {"title": "Supp<PERSON>er la confirmation du fichier", "content": "Êtes-vous sûr de vouloir supprimer le fichier \"{fileName}\" ? Cette action est irréversible."}, "resumeAllPausedTasks": "Récupération en un clic", "pauseAllRunningTasks": "Pause en un clic", "separators": "Séparateur de blocage", "separatorsHelper": "Document Séliquant séparateur, le séparateur unique est enveloppé dans des citations doubles à demi-largeur (\"\"), et le séparateur est divisé par une virgule à demi-largeur (,)", "invalidSeparators": "Séparateur non valide", "selectLanguage": "Sélectionnez un préréglage", "separatorsPreset": "Chargement des préréglages", "promptManagement": {"title": "Gestion des prompts", "description": "Gérer et utiliser des modèles de prompt personnalisés pour améliorer l'interaction avec l'IA"}}, "mcp": {"title": "Paramètres MCP", "description": "Gérer et configurer les serveurs et outils MCP (Model Context Protocol)", "enabledTitle": "Activer MCP", "enabledDescription": "<PERSON>r ou désactiver les fonctionnalités et outils MCP", "enableToAccess": "Veuillez activer MCP pour accéder aux options de configuration", "tabs": {"servers": "Serveurs", "tools": "Outils", "prompts": "Prompts", "resources": "Resources"}, "serverList": "Liste des serveurs", "totalServers": "Total des serveurs", "addServer": "Ajouter un serveur", "running": "En cours d'exécution", "stopped": "<PERSON><PERSON><PERSON><PERSON>", "stopServer": "<PERSON><PERSON><PERSON><PERSON> le <PERSON>", "startServer": "<PERSON><PERSON><PERSON><PERSON> le serveur", "noServersFound": "<PERSON><PERSON><PERSON> serveur trouvé", "addServerDialog": {"title": "Ajouter un serveur", "description": "Configurer un nouveau serveur MCP"}, "editServerDialog": {"title": "Modifier le serveur", "description": "Modifier la configuration du serveur MCP"}, "serverForm": {"name": "Nom du serveur", "namePlaceholder": "Entrer le nom du serveur", "nameRequired": "Le nom du serveur est requis", "type": "Type de serveur", "typePlaceholder": "Sélectionner le type de serveur", "typeStdio": "Entrée et sortie standard (Stdio)", "typeSse": "Server Send Events (SSE)", "typeInMemory": "En mémoire", "baseUrl": "URL de base", "baseUrlPlaceholder": "Entrer l'URL de base du serveur (ex : http://localhost:3000)", "command": "Commande", "commandPlaceholder": "Entrer la commande", "commandRequired": "La commande est requise", "args": "Arguments", "argsPlaceholder": "Entrer les arguments séparés par des espaces", "argsRequired": "Les arguments sont requis", "env": "Variables d'environnement", "envPlaceholder": "Entrer les variables d'environnement au format JSON", "envInvalid": "Les variables d'environnement doivent être au format JSON valide", "description": "Description", "descriptionPlaceholder": "Entrer la description du serveur", "descriptions": "Description", "descriptionsPlaceholder": "Entrer la description du serveur", "icon": "Icône", "iconPlaceholder": "Entrer l'icône", "icons": "Icône", "iconsPlaceholder": "Entrer l'icône", "autoApprove": "Approbation automatique", "autoApproveAll": "<PERSON>ut", "autoApproveRead": "Lecture", "autoApproveWrite": "Écriture", "autoApproveHelp": "Sélectionner les types d'opérations à approuver automatiquement sans confirmation de l'utilisateur", "submit": "So<PERSON><PERSON><PERSON>", "add": "Ajouter", "update": "Mettre à jour", "cancel": "Annuler", "jsonConfigIntro": "V<PERSON> pouvez coller une configuration JSON directement ou choisir de configurer le serveur manuellement.", "jsonConfig": "Configuration JSON", "jsonConfigPlaceholder": "Collez votre configuration de serveur MCP au format JSON", "jsonConfigExample": "Exemple de configuration JSON", "parseSuccess": "Configuration analysée", "configImported": "Configuration importée avec succès", "parseError": "<PERSON><PERSON><PERSON> d'analyse", "skipToManual": "Passer à la configuration manuelle", "parseAndContinue": "Analyser et continuer", "jsonParseError": "Échec de l'analyse JSON", "typeHttp": "Requêtes HTTP en streaming (HTTP)", "browseMarketplace": "Allez sur MCP Market et installez-le en un seul clic", "imageModel": "Sélectionnez un modèle visuel", "customHeadersParseError": "L'analyse de l'en-tête personnalisée a échoué", "customHeaders": "<PERSON>-tête de demande personnalisé", "invalidKeyValueFormat": "Format d'en-tête de demande incorrect, veuillez vérifier si l'entrée est correcte.", "npmRegistry": "Registre NPM personnalisé", "npmRegistryPlaceholder": "Configurez un registre NPM personnalisé, laissez le système pour sélectionner automatiquement le plus rapide", "browseHigress": "Voir Higress MCP Marketplace", "selectFolderError": "Erreur de sélection du dossier", "folders": "Dossiers autorisés à accéder", "addFolder": "Ajouter un dossier", "noFoldersSelected": "Aucun dossier n'a été sélectionné", "useE2B": "Activer E2B Sandbox", "e2bDescription": "Exécuter le code Python à l'aide de Sandbox E2B", "e2bApiKey": "E2B APIKEY", "e2bApiKeyPlaceholder": "Entrez ici vos touches API E2B, comme E2B_1111XX *******", "e2bApiKeyHelp": "Allez sur e2b.dev pour obtenir votre apikey", "e2bApiKeyRequired": "Apikey doit être entré pour activer la fonction E2B", "clickToEdit": "Cliquez sur Modifier pour afficher le contenu complet"}, "builtIn": "intégré", "builtInServerCannotBeRemoved": "Les services intégrés ne peuvent pas être supprimés, seuls les paramètres de modification et les variables d'environnement sont prises en charge.", "builtInServers": "Service intégré", "cannotRemoveBuiltIn": "Impossible de supprimer les services intégrés", "confirmDelete": {"cancel": "Annuler", "confirm": "supprimer", "description": "Êtes-vous sûr de vouloir supprimer le serveur {nom}? Cette opération ne peut pas être annulée.", "title": "Confirmer la <PERSON>"}, "confirmRemoveServer": "Êtes-vous sûr de vouloir supprimer le serveur {name} ? Cette action ne peut pas être annulée.", "customServers": "Services personnalisés", "default": "<PERSON><PERSON><PERSON><PERSON>", "deleteServer": "<PERSON><PERSON><PERSON><PERSON> le serveur", "editServer": "Modifier le serveur", "isDefault": "Serveur par défaut", "removeDefault": "Supprimer par défaut", "removeServer": "<PERSON><PERSON><PERSON><PERSON> le serveur", "autoStart": "Démarrage automatique", "removeServerDialog": {"title": "<PERSON><PERSON><PERSON><PERSON> le serveur"}, "resetConfirm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "resetConfirmDescription": "Cette action restaure tous les serveurs par défaut tout en conservant vos serveurs personnalisés. Toutes les modifications du serveur par défaut seront perdues.", "resetConfirmTitle": "Restaurer le service par défaut", "resetToDefault": "Restaurer le service par défaut", "setAsDefault": "Définir en tant que serveur par défaut", "setDefault": "Définir par défaut", "marketplace": "Allez sur MCP Market et installez-le en un seul clic", "maxDefaultServersReached": "Seuls 3 serveurs par défaut peuvent être définis au maximum", "removeDefaultFirst": "Veuillez supprimer d'abord certains serveurs par défaut", "higressMarket": "Aller à l'installation de Higress MCP", "npmRegistry": {"title": "Configuration des sources NPM", "currentSource": "Source actuelle", "cached": "En cache", "lastChecked": "Dernière vérification", "refresh": "Actualiser", "advanced": "<PERSON><PERSON><PERSON>", "advancedSettings": "Paramètres avancés", "advancedSettingsDesc": "Configurer les options avancées des sources NPM, y compris la détection automatique et les paramètres de source personnalisée", "autoDetect": "Détecter automatiquement la source optimale", "autoDetectDesc": "Détecter et utiliser automatiquement la source NPM la plus rapide au démarrage", "customSource": "Source personnalisée", "customSourcePlaceholder": "Entrer l'adresse de la source NPM personnalisée", "currentCustom": "Source personnalisée actuelle", "justNow": "À l'instant", "minutesAgo": "Il y a {minutes} minutes", "hoursAgo": "Il y a {hours} heures", "daysAgo": "Il y a {days} jours", "refreshSuccess": "Actualisation de la source NPM réussie", "refreshSuccessDesc": "Source NPM optimale redétectée et mise à jour", "refreshFailed": "Échec de l'actualisation de la source NPM", "autoDetectUpdated": "Paramètres de détection automatique mis à jour", "autoDetectEnabled": "Détection automatique de la source NPM optimale activée", "autoDetectDisabled": "Détection automatique désactivée, utilisation de la source par défaut", "updateFailed": "Échec de la mise à jour des paramètres", "customSourceSet": "Source personnalisée définie", "customSourceSetDesc": "Source NPM personnalisée définie : {registry}", "customSourceCleared": "Source personnalisée effacée", "customSourceClearedDesc": "Source NPM personnalisée effacée, utilisation de la détection automatique", "invalidUrl": "URL invalide", "invalidUrlDesc": "Veuillez entrer une adresse HTTP ou HTTPS valide", "testing": "Test de la source NPM en cours", "testingDesc": "Test de la connectivité de la source {registry}...", "testFailed": "Échec du test de la source NPM", "testFailedDesc": "Impossible de se connecter à {registry}, erreur : {error}. Veuillez vérifier si l'URL est correcte ou la connexion réseau.", "redetectingOptimal": "Redétection de la source NPM optimale en cours...", "redetectComplete": "Redétection terminée", "redetectCompleteDesc": "Source NPM optimale actuelle détectée et configurée", "redetectFailed": "Échec de la redétection", "redetectFailedDesc": "Impossible de redétecter la source optimale, utilisation de la configuration par défaut"}, "technicalDetails": "Détails techniques", "httpServer": "Serveur HTTP", "localProcess": "Processus local", "restartServer": "<PERSON><PERSON><PERSON><PERSON> le serveur", "viewLogs": "Voir les journaux", "starting": "Démarrage", "error": "<PERSON><PERSON><PERSON>"}, "about": {"checkUpdate": "Vérifier les mises à jour", "checking": "Vérification...", "latestVersion": "C'est la dernière version", "title": "à propos de nous", "version": "Version"}, "display": {"fontSize": "<PERSON><PERSON> de texte", "text-2xl": "Extrêmement grand", "text-base": "<PERSON><PERSON><PERSON><PERSON>", "text-lg": "grand", "text-sm": "<PERSON>", "text-xl": "Extrêmement grand", "floatingButton": "Bouton flottant", "floatingButtonDesc": "Afficher un bouton flottant sur le bureau pour activer rapidement la fenêtre de l'application"}, "shortcuts": {"title": "Paramètres des raccourcis clavier", "pressKeys": "Appuyez sur les touches", "pressEnterToSave": "Appuyez sur Entrée pour enregistrer, Échap pour annuler", "noModifierOnly": "Impossible d'utiliser uniquement une touche de modification comme raccourci", "keyConflict": "Conflit de rac<PERSON><PERSON><PERSON> clavier, veuillez choisir une autre combinaison", "clearShortcut": "<PERSON><PERSON><PERSON><PERSON> le rac<PERSON>ci", "cleanHistory": "Historique de chat clair", "deleteConversation": "Supprimer la conversation", "goSettings": "Paramètres ouvrir", "hideWindow": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "quitApp": "Quitter l'application", "zoomIn": "<PERSON>mer", "zoomOut": "Zoom out", "zoomReset": "Réinitialiser le zoom", "closeTab": "Fermez la page d'onglet actuelle", "newTab": "<PERSON><PERSON>er un nouvel onglet", "newWindow": "Ouvrez une nouvelle fenêtre", "showHideWindow": "Afficher/Masquer la fenêtre", "newConversation": "Nouvelle conversation", "lastTab": "Passez à la dernière onglet", "nextTab": "Passez à l'onglet suivant", "previousTab": "Passez à l'onglet précédent", "specificTab": "Passez à la page d'onglet spécifiée (1-8)"}, "rateLimit": {"title": "Limitation de débit", "description": "Contrôler l'intervalle de temps des requêtes, <PERSON><PERSON><PERSON> <PERSON> d<PERSON>er les limites API", "intervalLimit": "Intervalle de requête", "intervalUnit": "secondes", "intervalHelper": "Intervalle minimum entre deux requêtes, désactivez la limitation si pas nécessaire", "lastRequestTime": "<PERSON><PERSON><PERSON> requête", "queueLength": "<PERSON><PERSON><PERSON> de la file", "nextAllowedTime": "Prochaine requête autorisée", "never": "<PERSON><PERSON>", "justNow": "À l'instant", "secondsAgo": "secondes passées", "minutesAgo": "minutes passées", "immediately": "Immédiatement", "secondsLater": "secondes plus tard", "confirmDisableTitle": "Confirmer la désactivation de la limitation de débit", "confirmDisableMessage": "La valeur ne peut pas être inférieure ou égale à 0. Désactiver la limitation de débit ?", "confirmDisable": "Désactiver la limitation", "disabled": "Limitation de débit désactivée", "disabledDescription": "La limitation de débit a été désactivée"}}