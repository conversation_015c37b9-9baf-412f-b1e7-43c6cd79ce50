{"title": "تنظیمات", "common": {"title": "تنظیمات عمومی", "modelSettings": "تنظیمات مدل", "searchSettings": "تنظیمات جستجو", "networkSettings": "تنظیمات شبکه", "interfaceSettings": "رابط و تعامل", "personalizationSettings": "تنظیمات شخصی‌سازی", "securitySettings": "امنیت و حریم خصوصی", "operationSettings": "تنظیمات عملیات", "resetData": "بازنشانی داده‌ها", "language": "زبان", "languageSelect": "انتخاب زبان", "searchEngine": "موتور جستجو", "searchEngineDesc": "موتور جستجو برای بهبود جستجوی وب انتخاب کنید. از موتورهای جستجوی اصلی مانند Google، Bing و موتورهای جستجوی سفارشی پشتیبانی می‌کند", "searchEngineSelect": "انتخاب موتور جستجو", "searchPreview": "پیش‌نمایش جستجو", "searchPreviewDesc": "تصاویر کوچک پیش‌نمایش را در صفحات نتایج جستجو فعال کنید تا محتوای جستجو را به سرعت مرور کنید", "searchAssistantModel": "مدل دستیار", "searchAssistantModelDesc": "مدل را برای پردازش نتایج جستجو و تولید پاسخ انتخاب کنید. بر کیفیت و سرعت بهبود جستجو تأثیر می‌گذارد", "selectModel": "انتخاب مدل", "visionModel": "مدل بینایی", "visionModelDesc": "مدلی را برای پردازش تصاویر و محتوای بصری انتخاب کنید، که برای توصیف تصاویر، تحلیل و سایر عملکردها استفاده می‌شود", "visionModelNotSelectedWarning": "هیچ مدل بینایی انتخاب نشده است. هنگام استفاده از عملکردهای تصویری، به شما پیشنهاد خواهد شد که ابتدا آن را تنظیم کنید", "proxyMode": "حالت پراکسی", "proxyModeDesc": "تنظیمات پراکسی شبکه را پیکربندی کنید. پراکسی سیستم، بدون پراکسی یا پراکسی سفارشی برای دسترسی به خدمات خارجی انتخاب کنید", "proxyModeSelect": "انتخاب حالت پراکسی", "proxyModeSystem": "پراکسی سامانه", "proxyModeNone": "بدون پراکسی", "proxyModeCustom": "پراکسی دلخواه", "customProxyUrl": "نشانی پراکسی دلخواه", "customProxyUrlPlaceholder": "نمونه: http://127.0.0.1:7890", "invalidProxyUrl": "نشانی پراکسی نادرست است، لطفاً نشانی http/https معتبر وارد کنید", "addCustomSearchEngine": "افزودن موتور جستجوی دلخواه", "addCustomSearchEngineDesc": "با فراهم کردن نام و نشانی جستجو، موتور جستجوی دلخواه بیافزایید. نشانی باید دارای {query} به‌عنوان جایگاه پرسش باشد.", "searchEngineName": "نام موتور جستجو", "searchEngineNamePlaceholder": "نام موتور جستجو را وارد کنید", "searchEngineUrl": "نشانی جستجو", "searchEngineUrlPlaceholder": "مثال: https://a.com/search?q={query}", "searchEngineUrlError": "نشانی باید دارای {query} به‌عنوان جایگاه پرسش باشد", "deleteCustomSearchEngine": "پاک کردن موتور جستجوی دلخواه", "deleteCustomSearchEngineDesc": "آیا مطمئن هستید که می‌خواهید موتور جستجوی دلخواه \"{name}\" را پاک کنید؟ این کنش بازگشت‌پذیر نیست.", "testSearchEngine": "آزمایش موتور جستجو", "testSearchEngineDesc": "جستجویی آزمایشی برای \"آب‌وهوا\" با به‌کارگیری موتور جستجوی {engine} انجام خواهد شد.", "testSearchEngineNote": "اگر صفحه جستجو نیاز به ورود یا کنش‌های دیگر دارد، می‌توانید آن‌ها را در پنجره آزمایشی انجام دهید. لطفاً پس از اتمام، پنجره آزمایشی را ببندید.", "theme": "پوسته", "themeSelect": "انتخاب پوسته", "closeToQuit": "خروج از برنامه هنگام بستن پنجره", "closeToQuitDesc": "انتخاب کنید که آیا هنگام کلیک روی دکمه بستن از برنامه خارج شوید یا در سینی سیستم مخفی شوید. برنامه مخفی شده را می‌توان از سینی بازیابی کرد", "contentProtection": "حفاظت نمایشگر", "contentProtectionDialogTitle": "پذیرش تغییر حفاظت نمایشگر", "contentProtectionEnableDesc": "روشن کردن حفاظت نمایشگر از ضبط پنجره دیپ‌چت به‌دست برنامه‌های هم‌رسانی نمایشگر جلوگیری میکند و از حریم خصوصی محتوای شما محافظت می‌کند. توجه داشته باشید که این ویژگی همه رابط‌ها را کاملاً مخفی نمی‌کند. لطفاً از این ویژگی به‌طور مسئولانه و مطابق با مقررات استفاده کنید. همچنین، برخی برنامه‌های هم‌رسانی نمایشگر ممکن است از این ویژگی پشتیبانی نکنند. در برخی محیط‌ها ممکن است پنجره‌ای سیاه باقی بماند.", "contentProtectionDisableDesc": "خاموش کردن حفاظت نمایشگر به برنامه‌های هم‌رسانی نمایشگر اجازه می‌دهد پنجره دیپ‌چت را ضبط کنند.", "contentProtectionRestartNotice": "تغییر این تنظیم برنامه را بازراه‌اندازی می‌کند. آیا می‌خواهید ادامه دهید؟", "soundEnabled": "روشن کردن جلوه‌های صوتی", "soundEnabledDesc": "جلوه‌های صوتی سیستم را برای اعلان‌های پیام، هشدارهای خطا و سایر تعاملات فعال کنید تا تجربه کاربری بهتری داشته باشید", "copyWithCotEnabled": "رونوشت دارای جزئیات COT", "copyWithCotEnabledDesc": "اطلاعات زنجیره تفکر را هنگام کپی کردن پیام‌ها شامل کنید تا درک فرآیند استدلال هوش مصنوعی کمک کند", "loggingEnabled": "روشن کردن ثبت رخدادها", "loggingEnabledDesc": "ثبت رخدادها را برای تشخیص مسائل و بهبود برنامه فعال کنید. ممکن است حاوی اطلاعات حساس باشد و برای اعمال نیاز به راه‌اندازی مجدد دارد", "loggingDialogTitle": "پذیرش تغییر تنظیم ثبت رخدادها", "loggingEnableDesc": "روشن کردن ثبت رخدادها به ما کمک می‌کند مشکلات را عیب‌یابی کرده و برنامه را بهبود دهیم. پرونده‌های رخدادها ممکن است دارای اطلاعات حساس باشند.", "loggingDisableDesc": "خاموش کردن ثبت رخدادها، جمع‌آوری رخداد‌های برنامه را متوقف می‌کند.", "loggingRestartNotice": "تغییر این تنظیم برنامه را بازراه‌اندازی می‌کند. آیا می‌خواهید ادامه دهید؟", "openLogFolder": "باز کردن پوشه رخدادها", "shortcut": {"newChat": "ساختن گفت‌وگوی جدید", "title": "تنظیمات کلید میانبر"}, "notifications": "آگاه‌ساز سامانه", "notificationsDesc": "هنگامی که دیپ‌چت در پیش‌زمینه نیست، اگر نشستی تولید شود، یک آگاه‌ساز سامانه فرستاده خواهد شد", "languageDesc": "زبان رابط برنامه را انتخاب کنید. از چندین زبان از جمله فارسی، انگلیسی، چینی و غیره پشتیبانی می‌کند. تغییرات فوراً اعمال می‌شوند", "contentProtectionDesc": "از ضبط پنجره دیپ‌چت توسط برنامه‌های اشتراک‌گذاری صفحه نمایش برای حفاظت از حریم خصوصی محتوای چت جلوگیری کنید. برای اعمال نیاز به راه‌اندازی مجدد دارد"}, "data": {"title": "تنظیمات داده", "syncEnable": "روشن کردن همگام‌سازی داده", "syncFolder": "پوشه همگام‌سازی", "openSyncFolder": "باز کردن پوشه همگام‌سازی", "lastSyncTime": "آخرین زمان همگام‌سازی", "never": "هر<PERSON><PERSON>", "startBackup": "همین حالا پشتیبان‌گیری کن", "backingUp": "در حال پشتیبان‌گیری...", "importData": "وارد کردن داده", "incrementImport": "وروردی افزایشی باشد", "overwriteImport": "ورودی بازنویسی کند", "importConfirmTitle": "پذیرش وارد کردن داده", "importConfirmDescription": "وارد کردن داده همه داده‌های کنونی، دارای تاریخچه گفت‌وگو و تنظیمات را بازنویسی می‌کند. مطمئن شوید که از داده‌های مهم پشتیبان گرفته‌اید. پس از وارد کردن، نیاز به بازراه‌اندازی برنامه است.", "importing": "در حال وارد کردن...", "confirmImport": "پذیرش وارد کردن", "importSuccessTitle": "وارد کردن موفق", "importErrorTitle": "وارد کردن ناموفق", "resetData": "بازنشانی داده‌ها", "resetConfirmTitle": "پذیرش بازنشانی داده‌ها", "resetConfirmDescription": "لطفاً نوع داده‌هایی که می‌خواهید بازنشانی کنید را انتخاب کنید. این عملیات غیرقابل بازگشت است و پس از بازنشانی، برنامه به طور خودکار مجدداً راه‌اندازی خواهد شد.", "resetChatData": "بازنشانی داده‌های گفت‌وگو", "resetChatDataDesc": "حذف تمام تاریخچه گفت‌وگوها و سوابق مکالمات", "resetKnowledgeData": "بازنشانی داده‌های پایگاه دانش", "resetKnowledgeDataDesc": "حذف تمام فایل‌های پایگاه دانش و داده‌های برداری", "resetConfig": "بازنشانی پیکربندی", "resetConfigDesc": "حذف تمام تنظیمات برنامه، پیکربندی‌های مدل و دستورکارهای سفارشی", "resetAll": "بازنشانی کامل", "resetAllDesc": "حذف تمام داده‌ها شامل تاریخچه گفت‌وگوها، پیکربندی‌ها و فایل‌های کش", "resetting": "در حال بازنشانی...", "confirmReset": "پذیرش بازنشانی", "resetCompleteDevTitle": "بازنشانی داده‌ها تکمیل شد", "resetCompleteDevMessage": "در محیط توسعه لطفاً برنامه را به صورت دستی مجدداً راه‌اندازی کنید. فرآیند فعلی را متوقف کرده و pnpm run dev را دوباره اجرا کنید"}, "model": {"title": "تنظیمات مدل", "systemPrompt": {"label": "دستورکار سامانه", "placeholder": "لطفاً دستورکار سامانه را وارد کنید...", "description": "دستورکار سامانه را برای دستیار هوش مصنوعی تنظیم کنید تا رفتار و نقش آن را تعریف کند"}, "temperature": {"label": "دم<PERSON><PERSON> مدل", "description": "تصادفی‌<PERSON>ودن خروجی را هدایت می‌کند؛ مقادیر بالاتر پاسخ‌های خلاقانه‌تری تولید می‌کنند"}, "contextLength": {"label": "طول زمینه", "description": "بیشینه طول زمینه گفت‌وگو را تنظیم کنید"}, "responseLength": {"label": "طول پاسخ", "description": "بیشینه طول پاسخ هوش مصنوعی را تنظیم کنید"}, "artifacts": {"description": "روشن کردن ویژگی محصولات امکان تولید محتوای غنی‌تر توسط هوش مصنوعی را فراهم می‌کند", "title": "جلوه‌های محصولات"}, "addModel": "افزو<PERSON>ن مدل", "configureModel": "پیکربندی مدل", "modelList": "فهرست مدل‌ها", "provider": "فراهم‌کننده خدمات", "providerSetting": "تنظیمات فراهم‌کننده خدمات", "selectModel": "انتخاب مدل", "modelConfig": {"cancel": "لغو کردن", "contextLength": {"description": "طول زمینه ای را که مدل می تواند انجام دهد تنظیم کنید", "label": "طول متن"}, "description": "لطفاً توجه داشته باشید که این پیکربندی فقط برای مدل فعلی معتبر است و بر سایر مدل ها تأثیر نمی گذارد. لطفاً آن را با احتیاط اصلاح کنید. پارامترهای نادرست ممکن است باعث شود که مدل به درستی کار نکند.", "functionCall": {"description": "این که آیا مدل از عملکردهای عملکردی پشتیبانی می کند (DeepChat پس از خاموش کردن این گزینه ، به طور خودکار تماس های عملکردی را شبیه سازی می کند)", "label": "تماسهای عملکردی"}, "maxTokens": {"description": "حداکثر تعداد نشانه ها را برای یک خروجی واحد از مدل تنظیم کنید", "label": "حداکثر طول خروجی"}, "reasoning": {"description": "آیا مدل از توانایی استدلال پشتیبانی می کند؟", "label": "توانایی استدلال"}, "thinkingBudget": {"label": "بودجه تفکر", "description": "محدود کردن طول تفکر مدل", "dynamic": "تفکر پویا", "range": "محدوده: {min} - {max}", "onlySupported": "فقط در Gemini 2.5 Flash، 2.5 Pro و 2.5 Flash-<PERSON><PERSON> پشتیبانی می‌شود", "valueLabel": "مقدار بودجه تفکر", "placeholder": "مقدار بودجه تفکر را وارد کنید", "forceEnabled": "مدل‌های سری Gemini 2.5 بودجه تفکر را به صورت اجباری فعال می‌کنند", "dynamicPrefix": "-1 = تفکر پویا", "notice": "توجه: ", "warnings": {"proNoDisable": "این مدل از غیرفعال کردن تفکر پشتیبانی نمی‌کند، حداقل مقدار 128", "proCannotDisable": "Gemini 2.5 Pro نمی‌تواند عملکرد تفکر را غیرفعال کند", "flashLiteCannotSetZero": "Gemini 2.5 Flash-<PERSON><PERSON> نمی‌تواند روی 0 تنظیم شود، حداقل مقدار 512 است", "modelCannotDisable": "این مدل نمی‌تواند عملکرد تفکر را غیرفعال کند", "flashLiteMinValue": "بودجه تفکر نمی‌تواند کمتر از 512 باشد هنگام تنظیم روی مقدار مشخص (یا از 0 برای غیرفعال کردن تفکر، -1 برای فعال کردن تفکر پویا استفاده کنید)", "belowMin": "بودجه تفکر نمی‌تواند کمتر از {min} باشد{hint}", "aboveMax": "بودجه تفکر نمی‌تواند بیشتر از {max} باشد"}, "hints": {"flashLiteDisable": "، 0 = غیرفعال کردن تفکر، مقدار مشخص حداقل 512", "normalDisable": "، 0 = غیرفعال کردن تفکر", "withZeroAndDynamic": "(یا از 0 برای غیرفعال کردن تفکر، -1 برای فعال کردن تفکر پویا استفاده کنید)", "withDynamic": "(یا از -1 برای فعال کردن تفکر پویا استفاده کنید)"}}, "resetConfirm": {"confirm": "ت<PERSON><PERSON><PERSON><PERSON> مجدد", "message": "آیا مطمئن هستید که می خواهید پیکربندی این مدل را به صورت پیش فرض تنظیم کنید؟ این عمل غیرقابل برگشت است.", "title": "ت<PERSON><PERSON><PERSON><PERSON> مجدد"}, "reasoningEffort": {"label": "تلاش استدلال", "description": "عمق استدلال مدل را کنترل می‌کند؛ تلاش بیشتر نتایج بهتری تولید می‌کند اما پاسخ‌های کندتری دارد", "placeholder": "انتخاب تلاش استدلال", "options": {"minimal": "Minimal - سریع‌ترین پاسخ", "low": "Low - تلاش کم", "medium": "Medium - تلاش متوسط", "high": "High - تلاش زیاد"}}, "verbosity": {"label": "پرگویی", "description": "سطح جزئیات و طول پاسخ‌های مدل را کنترل می‌کند", "placeholder": "انتخاب سطح پرگویی", "options": {"low": "Low - پاسخ‌های مختصر", "medium": "Medium - جزئیات متعادل", "high": "High - پاسخ‌های تفصیلی"}}, "resetToDefault": "تنظیم مجدد به طور پیش فرض", "saveConfig": "پیکربندی را ذخیره کنید", "useModelDefault": "استفاده از پیکربندی پیش‌فرض مدل", "currentUsingModelDefault": "در حال حاضر از پیکربندی پیش‌فرض مدل استفاده می‌کند", "temperature": {"description": "تصادفی بودن خروجی را کنترل کنید. بیشتر مدل ها 0-1 و برخی از پشتیبانی بین 0-2 هستند. هرچه تصادفی بالاتر باشد.", "label": "درجه حرارت"}, "title": "پارامترهای مدل سفارشی", "type": {"description": "نوع مدل را انتخاب کنید", "label": "نوع مدل", "options": {"chat": "مد<PERSON> زبان", "embedding": "مدل", "imageGeneration": "مدل تولید تصویر", "rerank": "مدل را دوباره مرتب کنید"}}, "validation": {"contextLengthMax": "طول زمینه نمی تواند از 10000000 فراتر رود", "contextLengthMin": "طول زمینه باید بیشتر از 0 باشد", "contextLengthRequired": "طول زمینه نمی تواند خالی باشد", "maxTokensMax": "حداکثر طول خروجی نمی تواند از 1000000 فراتر رود", "maxTokensMin": "حداکثر طول خروجی باید بیشتر از 0 باشد", "maxTokensRequired": "حداکثر طول خروجی نمی تواند خالی باشد", "temperatureMax": "دما باید کمتر از یا برابر با 2 باشد", "temperatureMin": "دما باید بیشتر یا برابر با 0 باشد", "temperatureRequired": "درجه حرارت نمی تواند خالی باشد"}, "vision": {"description": "آیا مدل از توانایی بصری پشتیبانی می کند؟", "label": "توانایی بصری"}}}, "provider": {"search": "جستجوی پلتفرم‌های ارائه‌دهنده…", "enable": "روشن کردن خدمات", "enabled": "فعال", "disabled": "غیرفعال", "urlPlaceholder": "لطفاً نشانی API را وارد کنید", "keyPlaceholder": "لطفاً کلید API را وارد کنید", "accessKeyIdPlaceholder": "لطفاً AWS Access Key ID را وارد کنید", "secretAccessKeyPlaceholder": "لطفاً AWS Secret Access Key را وارد کنید", "regionPlaceholder": "لطفاً منطقه AWS را وارد کنید", "verifyKey": "پذ<PERSON><PERSON><PERSON> کلید", "howToGet": "چگونه دریافت کنیم", "getKeyTip": "لطفاً به", "getKeyTipEnd": "بروید تا کلید API را دریافت کنید", "urlFormat": "نمونه API: {defaultUrl}", "modelList": "فهرست مدل‌ها", "enableModels": "روشن کردن مدل‌ها", "disableAllModels": "خاموش کردن همه مدل‌ها", "modelsEnabled": "مدل‌ها روشن شده‌اند", "noModelsEnabled": {"title": "هیچ مدلی روشن نشده است", "description": "لطفاً دکمه «روشن کردن مدل‌ها» را کلیک کنید تا مدل‌هایی که می‌خواهید استفاده کنید را به صورت دستی انتخاب کنید."}, "verifyLink": "پیوند راستی‌آزمایی", "syncModelsFailed": "همگام‌سازی مدل‌ها ناموفق بود...", "addCustomProvider": "افزودن فراهم‌کننده دلخواه", "delete": "پاک کردن", "stopModel": "ایست مدل", "pulling": "در حال دریافت...", "runModel": "اجرای مدل", "dialog": {"disableModel": {"title": "پذیرش خاموش کردن مدل", "content": "آیا مطمئن هستید که می‌خواهید مدل \"{name}\" را خاموش کنید؟", "confirm": "خاموش کردن"}, "disableAllModels": {"title": "پذیرش خاموش کردن همه مدل‌ها", "content": "آیا مطمئن هستید که می‌خواهید همه مدل‌ها را خاموش کنید؟", "confirm": "خاموش کردن همه"}, "configModels": {"title": "پیکربندی فهرست مدل‌ها", "description": "مدل‌هایی را که می‌خواهید فعال یا غیرفعال کنید انتخاب کنید"}, "verify": {"missingFields": "لطفاً کلید API و نشانی API را وارد کنید", "failed": "راستی‌آزمایی ناموفق", "success": "راستی‌آزمایی موفق", "failedDesc": "راستی‌آزمایی کلید API یا پیکربندی ناموفق بود، لطفاً تنظیمات را بررسی کنید", "successDesc": "کلید API و پیکربندی با موفقیت راستی‌آزمایی شد، آماده استفاده است", "connectionError": "خطای اتصال ، لطفا اتصال شبکه و آدرس API را بررسی کنید", "serverError": "خطای سرور ، لطفاً بعداً دوباره امتحان کنید", "unauthorized": "احراز هویت انجام نشد ، کلید API نامعتبر یا منقضی شده است"}, "addCustomProvider": {"title": "افزودن فراهم‌کننده دلخواه", "description": "لطفاً اطلاعات لازم برای فراهم‌کننده را پر کنید", "name": "نام", "namePlaceholder": "لطفاً نام فراهم‌کننده را وارد کنید", "apiType": "نوع API", "apiTypePlaceholder": "لطفاً نوع API را انتخاب کنید", "apiKey": "کلید API", "apiKeyPlaceholder": "لطفاً کلید API را وارد کنید", "baseUrl": "نشانی پایه API", "baseUrlPlaceholder": "لطفاً نشانی پایه API را وارد کنید", "enable": "روشن کردن فراهم‌کننده"}, "deleteProvider": {"title": "پذیرش پاک کردن فراهم‌کننده", "content": "آیا مطمئن هستید که می‌خواهید فراهم‌کننده \"{name}\" را پاک کنید؟ این کنش بازگشت‌پذیر نیست.", "confirm": "پاک کردن"}, "deleteModel": {"title": "پذیرش پاک کردن مدل", "content": "آیا مطمئن هستید که می‌خواهید مدل \"{name}\" را پاک کنید؟ این کنش بازگشت‌پذیر نیست.", "confirm": "پاک کردن"}, "pullModel": {"title": "دریا<PERSON>ت مدل", "description": "مدل‌هایی را که می‌خواهید به صورت محلی دانلود کنید انتخاب کنید", "pull": "دریافت"}, "modelCheck": {"checking": "تست ...", "description": "مدلی را برای تست اتصال و قابلیت استفاده انتخاب کنید", "failed": "آزمون مدل انجام نشد", "model": "یک مدل را انتخاب کنید", "modelPlaceholder": "لطفاً مدل را برای آزمایش انتخاب کنید", "noModels": "هیچ مدلی برای این ارائه دهنده خدمات در دسترس نیست", "success": "تست مدل موفق شد", "test": "آزمون را شروع کنید", "title": "بررسی مدل"}}, "pullModels": "دریافت مدل‌ها", "refreshModels": "تازه‌سازی مدل‌ها", "modelsRunning": "مدل‌های در حال اجرا", "runningModels": "مدل‌های در حال اجرا", "noRunningModels": "بدون مدل در حال اجرا", "deleteModel": "پاک کردن مدل", "deleteModelConfirm": "آیا مطمئن هستید که می‌خواهید مدل \"{name}\" را پاک کنید؟ این کنش بازگشت‌پذیر نیست.", "noLocalModels": "بدون مدل محلی", "localModels": "مد<PERSON>‌<PERSON><PERSON><PERSON> محلی", "azureApiVersion": "نگارش API", "safety": {"title": "تنظیمات ایمنی", "blockHighest": "ریسک بالا", "blockMost": "خطرات متوسط ​​را مسدود کنید", "blockNone": "مسدود نشده", "blockSome": "بلوک ریسک کم"}, "serverList": "فهرست کارسازها", "totalServers": "مجموع کارسازها", "addServer": "افزودن کارساز", "autoStart": "آ<PERSON><PERSON><PERSON> خودکار", "githubCopilotAuth": "احراز هویت گیت‌هاب کوپایلت", "githubCopilotConnected": "گیت‌هاب کوپایلت پیوند داده شده", "githubCopilotNotConnected": "گیت‌هاب کوپایلت پیوند داده نشده", "loginWithGitHub": "ورود با گیت‌هاب", "loggingIn": "در حال ورود...", "githubCopilotLoginTip": "دیپ‌چت را برای دسترسی به اشتراک گیت‌هاب کوپایلت خود مجاز کنید. دسترسی‌های 'read:user' و 'read:org' برای دسترسی به API کوپایلت لازم است.", "loginSuccess": "ورود موفق", "loginFailed": "ورود ناموفق", "tokenValid": "توکن معتبر است", "tokenInvalid": "توکن نامعتبر است", "disconnect": "جدا کردن", "disconnected": "جدا شدن موفق", "disconnectFailed": "جدا شدن ناموفق", "keyStatus": {"remaining": "مقدار باقی مانده", "usage": "مورد استفاده"}, "refreshingModels": "طراوت ...", "toast": {"modelRunning": "مدل در حال اجرا است", "modelRunningDesc": "لطفاً ابتدا مدل {مدل را متوقف کرده و سپس آن را حذف کنید."}, "anthropicApiKeyTip": "لطفاً برای دریا<PERSON>ت کلید API خود به کنسول انسان شناسی بروید", "anthropicConnected": "انسان شناسی متصل", "anthropicNotConnected": "انسان شناسی متصل نیست", "anthropicOAuthTip": "برای دسترسی به حساب انسان شناسی خود ، روی مجوز DeepChat کلیک کنید", "oauthLogin": "ورود به سیستم", "authMethod": "روش صدور گواهینامه", "authMethodPlaceholder": "روش احراز هویت را انتخاب کنید", "apiKeyLabel": "کلید API", "apiUrlLabel": "آدرس API", "anthropicOAuthFlowTip": "سیستم به طور خودکار پنجره مجوز را باز می کند. لطفا برگردید و پس از مجوز کد مجوز را وارد کنید.", "anthropicBrowserOpened": "مرورگر خارجی باز است", "anthropicCodeInstruction": "لطفاً مجوز را در یک مرورگر خارجی تکمیل کرده و کد مجوز به دست آمده را در کادر ورودی زیر بچسبانید", "browserOpenedSuccess": "مرورگر خارجی باز است ، لطفاً مجوز را تکمیل کنید", "codeRequired": "لطفاً کد مجوز را وارد کنید", "inputOAuthCode": "کد مجوز را وارد کنید", "codeExchangeFailed": "مباد<PERSON>ه کد مجوز انجام نشد", "invalidCode": "کد مجوز نامعتبر", "oauthCodeHint": "لطفاً پس از تکمیل مجوز در یک مرورگر خارجی ، کد مجوز را در اینجا بچسبانید", "oauthCodePlaceholder": "لطفاً کد مجوز را وارد کنید ...", "verifyConnection": "ت<PERSON><PERSON><PERSON>د اتصال", "manageModels": "مدیریت مدل‌ها", "anthropicOAuthActiveTip": "احراز هویت OAuth فعال است، می‌توانید مستقیماً از سرویس‌های Anthropic استفاده کنید", "oauthVerifySuccess": "اتصال OAuth با موفقیت تأیید شد", "oauthVerifyFailed": "تأیید اتصال OAuth ناموفق بود", "configurationSaved": "پیکربندی ذخیره شده", "configurationUpdated": "پیکربندی به روز شد", "dataRefreshed": "داده ها تازه شده است", "modelscope": {"apiKey": "کلید API", "apiKeyHelper": "کلید API خود را در کنسول ModelCope دریافت کنید", "apiKeyPlaceholder": "لطفاً کلید API Modelcope را وارد کنید", "baseUrl": "آدرس API", "baseUrlHelper": "آدرس خدمات API Modelcope", "connected": "متصل", "connecting": "اتصال ...", "description": "Modelcope یک بستر اشتراک گذاری مدل به عنوان یک سرویس است که توسط آکادمی Alibaba Damo راه اندازی شده است", "details": {"apiConfig": "پیکربندی API", "mcpSync": "همگام سازی MCP", "modelManagement": "مدی<PERSON><PERSON>ت مدل", "operationalDescription": "سرورهای MCP را همگام سازی کنید که می توانند مستقیماً در سیستم عامل ModelCope استفاده شوند", "operationalServers": "کار کردن یک سرور", "rateLimitConfig": "پیکربندی حد نرخ", "safetySettings": "تنظیمات امنیتی", "specialConfig": "پیکربندی خاص", "syncFromModelScope": "همگام سازی از Modelcope", "title": "جزئیات تنظیمات ارائه دهنده"}, "invalidKey": "کلید API نامعتبر", "keyRequired": "لطفا کلید API را وارد کنید", "mcpSync": {"authenticationFailed": "احراز هویت انجام نشد ، لطفاً کلید API را بررسی کنید", "convertingServers": "تبدیل پیکربندی سرور ...", "description": "همگام سازی یک سرور MCP از ModelScope به تنظیمات محلی به شما امکان می دهد تا به سرعت ابزارهای MCP متداول را اضافه کنید. کلیه خدمات به طور پیش فرض غیرفعال می شوند و پس از وارد کردن می توانند به صورت دستی فعال شوند.", "errorDetails": "جزئیا<PERSON> خطا", "errors": "خطا {تعداد}", "fetchingServers": "دریافت لیست سرورهای MCP ...", "imported": "{تعداد خدمات وارد شده است", "importingServers": "وارد کردن پیکربندی سرور ...", "invalidServerData": "داده های سرور نامعتبر", "noApiKey": "لطفاً ابتدا کلید API Modelcope را پیکربندی کنید", "noOperationalUrls": "هیچ آدرس عملیاتی موجود یافت نمی شود", "noServersFound": "هیچ سرویس MCP در دسترس یافت نشد", "pageNumber": "شماره صفحه", "pageNumberPlaceholder": "لطفا شماره صفحه را وارد کنید", "pageSize": "مقدار در هر صفحه", "serverAlreadyExists": "سرور در حال حاضر وجود دارد ، واردات را پرش کنید", "skipped": "خدمات SKIP {COUNT", "sync": "همگام سازی را شروع کنید", "syncComplete": "اتمام همزمان", "syncing": "هماهنگ سازی ...", "title": "همگام سازی خدمات MCP"}, "name": "مد<PERSON><PERSON><PERSON><PERSON> مدل", "networkError": "خطای اتصال شبکه", "notConnected": "متصل نیست", "verifyFailed": "تأیید انجام نشد", "verifySuccess": "ت<PERSON><PERSON>ید موفقیت آ<PERSON>یز است"}, "operationFailed": "عملیات شکست خورد", "operationSuccess": "عملیات موفقیت آ<PERSON>یز است", "settingsApplied": "تنظیمات اعمال شده", "bedrockLimitTip": "* فقط از Anthropic Claude (شام<PERSON> مدل‌های Opus، Sonnet، Haiku) پشتیبانی می‌کند", "bedrockVerifyTip": "DeepChat از Claude 3.5 Sonnet برای تأیید استفاده می‌کند. اگر مجوز فراخوانی نداشته باشید، تأیید انجام نخواهد شد. این موضوع تاثیری بر استفاده از مدل‌های دیگر نخواهد داشت."}, "knowledgeBase": {"title": "تنظیمات پایگاه دانش", "addKnowledgeBase": "افزودن پایگاه دانش", "selectKnowledgeBaseType": "لطفاً نوع پایگاه دانش برای افزودن را انتخاب کنید", "difyDescription": "پایگاه دانش دیفای به شما کمک می‌کند داده‌های سند را مدیریت و استفاده کنید", "comingSoon": "به‌زو<PERSON>ی", "featureNotAvailable": "این ویژگی هنوز در دسترس نیست", "addDifyConfig": "افزودن پیکربندی دیفای", "apiKey": "کلید API", "datasetId": "شناسه مجموعه‌داده", "endpoint": "نشانی دسترسی", "configAdded": "پیکربندی افزوده شد", "configAddedDesc": "{name} پیکربندی با موفقیت اضافه شده است", "addConfig": "افزودن پیکربندی", "moreComingSoon": "انواع پایگاه دانش بیشتر به‌زودی", "configUpdated": "پیکربندی به‌روزرسانی شد", "configUpdatedDesc": "{name} پیکربندی با موفقیت به روز شده است", "descriptionPlaceholder": "مثال: پایگاه دانش اسناد محصولات شرکت", "ragflowTitle": "پایگاه دانش رگ‌فلو", "ragflowDescription": "رگ‌فلو یک سامانه مدیریت پایگاه دانش قدرتمند است که از روش‌های بازیابی بسیار و ویژگی‌های مدیریت سند پشتیبانی می‌کند.", "addRagflowConfig": "افزودن پیکربندی رگ‌فلو", "editRagflowConfig": "ویرایش پیکربندی رگ‌فلو", "dify": "پایگاه دانش دیفای", "editDifyConfig": "ویرایش پیکربندی دیفای", "fastgptTitle": "پایگاه دانش فست‌جی‌پی‌تی", "fastgptDescription": "فست‌جی‌پی‌تی یک سامانه مدیریت پایگاه دانش قدرتمند است که از روش‌های بازیابی بسیار و ویژگی‌های مدیریت سند پشتیبانی می‌کند.", "addFastGptConfig": "افزودن پیکربندی فست‌جی‌پی‌تی", "editFastGptConfig": "ویرایش پیکربندی فست‌جی‌پی‌تی", "builtInKnowledgeDescription": "پایگاه دانش داخلی برخی از پیاده سازی های ساده را ارائه می دهد که برخی از کارکردهای اساسی را در یک محیط آفلاین امکان پذیر می کند.", "builtInKnowledgeTitle": "پایگاه دانش داخلی", "addBuiltinKnowledgeConfig": "پیکربندی پایه دانش داخلی را اضافه کنید", "editBuiltinKnowledgeConfig": "پیکربندی پایه دانش داخلی را ویرایش کنید", "chunkSize": "اندازه بلوک", "chunkSizeHelper": "سند را به بخش ها برش دهید ، اندازه هر بخش نمی تواند از حد زمینه مدل فراتر رود", "chunkOverlap": "اندازه همپوشانی", "chunkOverlapHelper": "میزان محتوای تکرار شده بین بلوک های متنی مجاور ، تضمین می کند که هنوز یک ارتباط متنی بین بلوک های متنی تقسیم شده وجود دارد ، و تأثیر کلی پردازش مدل متون طولانی را بهبود می بخشد", "selectEmbeddingModel": "یک مدل تعبیه شده را انتخاب کنید", "modelNotFound": "ارائه دهنده خدمات {provider} یا مدل {model} یافت نشد", "modelNotFoundDesc": "اطمینان حاصل کنید که مدل به درستی پیکربندی شده و مدل فعال شده است. می توانید پیکربندی مدل را در تنظیمات ارائه دهنده خدمات بررسی کنید.", "removeBuiltinKnowledgeConfirmDesc": "حذف پیکربندی پایه دانش داخلی ، تمام داده های مربوطه را حذف می کند و قابل ترمیم نیست. لطفا محتاط باشید", "removeBuiltinKnowledgeConfirmTitle": "تایید می‌کنید که پایگاه دانش داخلی {name} حذف شود؟", "descriptionDesc": "شرح پایگاه دانش به گونه ای که هوش مصنوعی تصمیم بگیرد که آیا این پایگاه دانش را بازیابی کند", "advanced": "گزینه های پیشرفته", "autoDetectDimensions": "به طور خودکار ابعاد تعبیه شده را تشخیص دهید", "autoDetectHelper": "به طور خودکار ابعاد تعبیه شده را تشخیص دهید ، مقدار کمی از نشانه ها را مصرف می کند", "chunkOverlapPlaceholder": "مقدار پیش فرض ، هیچ اصلاح توصیه نمی شود", "chunkSizePlaceholder": "مقدار پیش فرض ، هیچ اصلاح توصیه نمی شود", "dimensions": "ابعاد جاسازی شده", "dimensionsPlaceholder": "اندازه ابعاد تعبیه شده ، مانند 1024", "selectEmbeddingModelHelper": "مدل های تعبیه شده پس از ایجاد پایگاه دانش ممنوع است", "dimensionsHelper": "اطمینان حاصل کنید که مدل از اندازه ابعاد تعبیه شده پشتیبانی می کند", "autoDetectDimensionsError": "به طور خودکار خرابی بعد تعبیه شده را تشخیص دهید", "fragmentsNumber": "تعداد قطعات سند درخواست شده", "fragmentsNumberHelper": "هرچه بخش های بیشتر از سند درخواست شده بیشتر باشد ، اطلاعات بیشتری به آن می رسد ، اما نشانه های بیشتری برای مصرف آن وجود دارد", "selectRerankModel": "مدل تنظیم مجدد را انتخاب کنید", "rerankModel": "مدل را دوباره مرتب کنید", "embeddingModel": "مدل", "return": "بازگشت", "uploadHelper": "برای بارگذاری یا کشیدن پرونده در اینجا کلیک کنید", "fileSupport": "پشتیبانی از {accept} و {count} فرمت‌های دیگر", "searchKnowledge": "پایگاه دانش را جستجو کنید", "searchKnowledgePlaceholder": "لطفاً محتوای پرس و جو را وارد کنید", "noData": "هنوز اطلاعاتی وجود ندارد", "file": "مدرک", "uploadProcessing": "بارگذاری", "uploadCompleted": "بارگذاری کامل", "reAdd": "بارگذاری مجدد", "uploadError": "بارگذاری ناموفق بود", "delete": "<PERSON><PERSON><PERSON> کردن", "reason": "دلیل", "deleteSuccess": "با موفقیت حذف کنید", "copy": "ک<PERSON>ی کردن", "copySuccess": "با موفقیت کپی کنید", "source": "منبع", "normalized": "عادی سازی L2", "normalizedHelper": "لطفاً تأیید کنید که این مدل از نرمال سازی L2 بردارهای خروجی پشتیبانی می کند", "dialog": {"beforequit": {"cancel": "لغو کردن", "confirm": "ت<PERSON><PERSON><PERSON>د کردن", "title": "ت<PERSON><PERSON><PERSON><PERSON> خروج", "description": "یک کار پایه دانش در حال اجرا وجود دارد. آیا مطمئناً از نرم افزار خارج می شوید؟ کارهای سقط شده را می توان پس از شروع مجدد نرم افزار بازگرداند."}}, "searchError": "پرس و جو انجام نشد", "processing": "بارگذاری", "paused": "مکث", "unknown": "وضعیت ناشناخته", "reAddFile": {"title": "ت<PERSON><PERSON><PERSON><PERSON> مجدد", "content": "آیا مطمئن هستید که می‌خواهید پرونده \"{fileName}\" را دوباره بارگذاری کنید؟"}, "deleteFile": {"title": "تأیید پرونده را حذف کنید", "content": "آیا مطمئن هستید که می‌خواهید پرونده \"{fileName}\" را حذف کنید؟ این عملیات ترمیم نشده است."}, "resumeAllPausedTasks": "بازیا<PERSON>ی یک کلیک", "pauseAllRunningTasks": "مکث یک کلیک", "separators": "جدا کننده مسدود کننده", "separatorsHelper": "جداکننده تقسیم‌بندی سند، یک جداکننده منفرد داخل نقل قول دوتایی (\"\") قرار می‌گیرد و جداکننده‌ها با کاما (,) از هم جدا می‌شوند", "invalidSeparators": "جداکننده نامعتبر", "selectLanguage": "از پیش تعیین شده را انتخاب کنید", "separatorsPreset": "از پیش تنظیمات در حال بارگیری", "promptManagement": {"title": "مدیریت دستورکارها", "description": "مدیریت و استفاده از قالب‌های دستورکار سفارشی برای تعامل بهتر با هوش مصنوعی"}}, "mcp": {"title": "تنظیمات MCP", "description": "مدیریت و پیکربندی کارسازها و ابزارهای MCP (پروتکل کنترل مدل)", "enabledTitle": "روشن کردن MCP", "enabledDescription": "روشن یا خاموش کردن امکانات و ابزارهای MCP", "enableToAccess": "لطفاً MCP را روشن کنید تا به گزینه‌های پیکربندی دسترسی پیدا کنید", "marketplace": "رفتن به بازار MCP برای نصب با یک دکمه", "technicalDetails": "جزئیات فنی", "httpServer": "کارساز HTTP", "localProcess": "فر<PERSON><PERSON><PERSON><PERSON> محلی", "restartServer": "بازراه‌اندازی کارساز", "viewLogs": "مشاهده رخدادها", "starting": "در حال راه‌اند<PERSON>زی", "error": "حطا", "tabs": {"servers": "کارسازها", "tools": "ابزارها", "prompts": "دستورکارها", "resources": "منابع"}, "serverList": "فهرست کارسازها", "totalServers": "کل کارسازها", "addServer": "افزودن کارساز", "running": "در حال اجرا", "stopped": "ایستاده", "stopServer": "ایست کارساز", "startServer": "راه‌اندازی کارساز", "noServersFound": "کارسازی یافت نشد", "addServerDialog": {"title": "افزودن کارساز", "description": "پیکربندی کارساز جدید MCP"}, "editServerDialog": {"title": "ویرایش کارساز", "description": "ویرایش پیکربندی کارساز MCP"}, "serverForm": {"name": "نام کارساز", "namePlaceholder": "نام کارساز را وارد کنید", "nameRequired": "نام کارساز لازم است", "type": "نوع کارساز", "typePlaceholder": "نوع کارساز را انتخاب کنید", "typeStdio": "ورودی و خروجی استاندارد (Stdio)", "typeSse": "فرستاده رویدادهای کارساز (SSE)", "typeInMemory": "در حافظه", "typeHttp": "درخواست‌های HTTP جریانی (HTTP)", "baseUrl": "نشانی پایه", "baseUrlPlaceholder": "نشانی پایه کارساز را وارد کنید (مثال: http://localhost:3000)", "command": "دستور", "commandPlaceholder": "دستور را وارد کنید", "commandRequired": "دستور لازم است", "args": "ورودی‌ها", "argsPlaceholder": "ورودی‌ها را با فاصله وارد کنید", "argsRequired": "ورودی‌ها لازم اند", "env": "متغیرهای محیط", "envPlaceholder": "متغیرهای محیط را در قالب JSON وارد کنید", "envInvalid": "متغیرهای محیط باید در قالب JSON معتبر باشند", "description": "توصیف", "descriptionPlaceholder": "توصیف کارساز را وارد کنید", "descriptions": "توصیف", "descriptionsPlaceholder": "توصیف کارساز را وارد کنید", "icon": "نماد", "iconPlaceholder": "نماد را وارد کنید", "icons": "نماد", "iconsPlaceholder": "نماد را وارد کنید", "autoApprove": "پذی<PERSON><PERSON> خودکار", "autoApproveAll": "همه", "autoApproveRead": "<PERSON>و<PERSON><PERSON>ن", "autoApproveWrite": "نوشتن", "autoApproveHelp": "نوع کنش‌هایی که بدون پذیرش کاربر به‌صورت خودکار پذیرفته می‌شوند را انتخاب کنید", "submit": "فرستادن", "add": "افزودن", "update": "به‌روزرسانی", "cancel": "ر<PERSON> کر<PERSON>ن", "jsonConfigIntro": "می‌توانید پیکربندی JSON را مستقیم جای‌گذاری کنید یا کارساز را به‌صورت دستی پیکربندی کنید.", "jsonConfig": "پیکربندی JSON", "jsonConfigPlaceholder": "پیکربندی JSON کارساز MCP خود را در جای‌گذاری کنید", "jsonConfigExample": "نمونه پیکربندی JSON", "parseSuccess": "پیکربندی تجزیه شد", "configImported": "پیکربندی با موفقیت وارد شد", "parseError": "خطای تجزیه", "skipToManual": "پرش به پیکربندی دستی", "parseAndContinue": "تجزیه و ادامه", "jsonParseError": "تجزیه JSON ناموفق بود", "browseMarketplace": "مرور بازار MCP", "imageModel": "انتخاب مدل بصری", "customHeadersParseError": "تجزیه سربرگ دلخواه ناموفق بود", "customHeaders": "سربرگ درخواست دلخواه", "invalidKeyValueFormat": "قالب سربرگ درخواست نادرست است، لطفاً بررسی کنید که ورودی صحیح باشد.", "npmRegistry": "مخزن NPM دلخواه", "npmRegistryPlaceholder": "مخزن NPM دلخواه را تنظیم کنید، سامانه به‌صورت خودکار سریع‌ترین را انتخاب می‌کند", "browseHigress": "مشاهده بازار MCP هایگرس", "selectFolderError": "خطای انتخاب پوشه", "folders": "پوشه‌های مجاز برای دسترسی", "addFolder": "افزودن پوشه", "noFoldersSelected": "هیچ پوشه‌ای انتخاب نشده است", "useE2B": "ماسه جعبه E2B را فعال کنید", "e2bDescription": "کد پایتون را با استفاده از جعبه ماسه ای E2B اجرا کنید", "e2bApiKey": "e2b apikey", "e2bApiKeyPlaceholder": "کلیدهای API E2B خود را در اینجا وارد کنید ، مانند E2B_11111XX *******", "e2bApiKeyHelp": "برای بدست آوردن apikey خود به e2b.dev بروید", "e2bApiKeyRequired": "برای فعال کردن عملکرد E2B باید A<PERSON>key وارد شود", "clickToEdit": "برای مشاهده مطالب کامل ، روی ویرایش کلیک کنید"}, "deleteServer": "پاک کردن کارساز", "editServer": "ویرایش کارساز", "setDefault": "تنظیم به‌عنوان پیش‌فرض", "removeDefault": "برداشتن پیش‌فرض", "isDefault": "کارساز پیش‌فرض", "default": "پیش‌<PERSON><PERSON>ض", "setAsDefault": "تنظیم به‌عنوان پیش‌فرض", "removeServer": "پاک کردن کارساز", "autoStart": "آ<PERSON><PERSON><PERSON> خودکار", "confirmRemoveServer": "آیا مطمئن هستید که می‌خواهید کارساز {name} را پاک کنید؟ این کنش بازگشت‌پذیر نیست.", "removeServerDialog": {"title": "پاک کردن کارساز"}, "confirmDelete": {"title": "پذیرش پاک کردن", "description": "آیا مطمئن هستید که می‌خواهید کارساز {name} را پاک کنید؟ این کنش بازگشت‌پذیر نیست.", "confirm": "پاک کردن", "cancel": "ر<PERSON> کر<PERSON>ن"}, "resetToDefault": "بازنشانی به پیش‌فرض", "resetConfirmTitle": "بازنشانی به کارسازهای پیش‌فرض", "resetConfirmDescription": "این کار همه کارسازهای پیش‌فرض را بازیابی می‌کند در حالی که کارسازهای دلخواه شما نگه داشته می‌شوند. هرگونه دگرگونی در کارسازهای پیش‌فرض از دست خواهد رفت.", "resetConfirm": "بازنشانی", "builtInServers": "کارسازهای داخلی", "customServers": "کارسازهای دلخواه", "builtIn": "داخلی", "cannotRemoveBuiltIn": "نمی‌توان کارساز داخلی را پاک کرد", "builtInServerCannotBeRemoved": "کارسازهای داخلی پاک شدنی نیستند، فقط داده‌ها و متغیرهای محیط می‌توانند ویرایش شوند", "maxDefaultServersReached": "بیشینه ۳ کارساز پیش‌فرض می‌توان تنظیم کرد", "removeDefaultFirst": "لطفاً ابتدا برخی کارسازهای پیش‌فرض را بردارید", "higressMarket": "رفتن به نصب MCP هایگرس", "npmRegistry": {"title": "تنظیمات منابع NPM", "currentSource": "منبع فعلی", "cached": "ذخ<PERSON>ره شده", "lastChecked": "آخرین بررسی", "refresh": "تازه‌سازی", "advanced": "پیشرفته", "advancedSettings": "تنظیمات پیشرفته", "advancedSettingsDesc": "پیکربندی گزینه‌های پیشرفته منابع NPM، شامل تشخیص خودکار و تنظیمات منبع سفارشی", "autoDetect": "تشخیص خودکار منبع بهینه", "autoDetectDesc": "تشخیص و استفاده خودکار از سریع‌ترین منبع NPM هنگام راه‌اندازی", "customSource": "منبع سفارشی", "customSourcePlaceholder": "آدرس منبع NPM سفارشی را وارد کنید", "currentCustom": "منبع سفارشی فعلی", "justNow": "<PERSON><PERSON><PERSON><PERSON> الان", "minutesAgo": "{minutes} دقی<PERSON>ه پیش", "hoursAgo": "{hours} ساعت پیش", "daysAgo": "{days} روز پیش", "refreshSuccess": "تازه‌سازی منبع NPM موفق بود", "refreshSuccessDesc": "منبع بهینه NPM دوباره تشخیص داده شد و به‌روزرسانی شد", "refreshFailed": "تازه‌سازی منبع NPM ناموفق بود", "autoDetectUpdated": "تنظیمات تشخیص خودکار به‌روزرسانی شد", "autoDetectEnabled": "تشخیص خودکار منبع بهینه NPM فعال شد", "autoDetectDisabled": "تشخیص خودکار غیرفعال شد، از منبع پیش‌فرض استفاده خواهد شد", "updateFailed": "به‌روزرسانی تنظیمات ناموفق بود", "customSourceSet": "منبع سفارشی تنظیم شد", "customSourceSetDesc": "منبع NPM سفارشی تنظیم شد: {registry}", "customSourceCleared": "منبع سفارشی پاک شد", "customSourceClearedDesc": "منبع NPM سفارشی پاک شد، از تشخیص خودکار استفاده خواهد شد", "invalidUrl": "آدرس نامعتبر", "invalidUrlDesc": "لطفاً آدرس HTTP یا HTTPS معتبری وارد کنید", "testing": "آزمایش منبع NPM", "testingDesc": "آزمایش اتصال به منبع {registry}...", "testFailed": "آزمایش منبع NPM ناموفق بود", "testFailedDesc": "نمی‌توان به {registry} متصل شد، خطا: {error}. لطفاً بررسی کنید که آدرس درست است یا اتصال شبکه.", "redetectingOptimal": "تشخیص مجدد منبع بهینه NPM...", "redetectComplete": "تشخیص مجدد کامل شد", "redetectCompleteDesc": "منبع بهینه فعلی NPM تشخیص داده شد و پیکربندی شد", "redetectFailed": "تشخیص مجدد ناموفق بود", "redetectFailedDesc": "نمی‌توان منبع بهینه را مجدداً تشخیص داد، از پیکربندی پیش‌فرض استفاده خواهد شد"}}, "about": {"title": "درباره ما", "version": "نگارش", "checkUpdate": "بررسی به‌روزرسانی", "checking": "در حال بررسی...", "latestVersion": "آخرین نگارش"}, "display": {"fontSize": "اندازه قلم", "text-2xl": "بسیار  بسیار بزرگ", "text-base": "پیش‌<PERSON><PERSON>ض", "text-lg": "بزرگ", "text-sm": "کوچک", "text-xl": "بسیار بزرگ", "floatingButton": "دکمه شناور", "floatingButtonDesc": "نمایش یک دکمه شناور بر روی دسکتاپ برای فعال‌سازی سریع پنجره برنامه"}, "shortcuts": {"title": "تنظیمات کلید میانبر", "pressKeys": "کلیدها را فشار دهید", "pressEnterToSave": "برای نگهداری Enter را فشار دهید، برای رد کردن Esc را بزنید", "noModifierOnly": "نمی‌توان از کلید اصلاح‌کننده به‌تنهایی به‌عنوان میانبر استفاده کرد", "keyConflict": "ناسازگاری کلید میانبر، لطفاً ترکیب دیگری انتخاب کنید", "clearShortcut": "پاک کردن میانبر", "cleanHistory": "تمیز کردن تاریخچه گفت‌وگو", "deleteConversation": "پاک کردن گفت‌وگو", "goSettings": "باز کردن تنظیمات", "hideWindow": "مخ<PERSON>ی کردن پنجره", "quitApp": "خروج از برنامه", "zoomIn": "بزرگ‌نمایی", "zoomOut": "کوچک‌نمایی", "zoomReset": "بازنشانی بزرگ‌نمایی", "closeTab": "بستن برگه کنونی", "newTab": "ساختن برگه جدید", "newWindow": "باز کردن پنجره جدید", "showHideWindow": "نمایش/مخفی کردن پنجره", "newConversation": "گفت‌وگوی جدید", "nextTab": "رفتن به برگه بعدی", "previousTab": "رفتن به برگه پیشین", "specificTab": "رفتن به برگه خاص (1-8)", "lastTab": "رفتن به آخرین برگه"}, "rateLimit": {"title": "محدودیت نرخ", "description": "کنترل فاصله زمانی درخواست‌ها، جلوگیری از تجاوز محدودیت‌های API", "intervalLimit": "فاصله درخواست", "intervalUnit": "ثانیه", "intervalHelper": "حداقل فاصله زمانی بین دو درخواست، در صورت عدم نیاز محدودیت نرخ را غیرفعال کنید", "lastRequestTime": "آخرین درخواست", "queueLength": "طول صف", "nextAllowedTime": "درخواست بعدی مجاز", "never": "هر<PERSON><PERSON>", "justNow": "<PERSON><PERSON><PERSON><PERSON> الان", "secondsAgo": "ثانیه پیش", "minutesAgo": "دقی<PERSON>ه پیش", "immediately": "فوری", "secondsLater": "ثانیه بعد", "confirmDisableTitle": "تأیید غیرفعال کردن محدودیت نرخ", "confirmDisableMessage": "مقدار نمی‌تواند کمتر یا مساوی 0 باشد. آیا محدودیت نرخ غیرفعال شود؟", "confirmDisable": "غیرفعال کردن محدودیت", "disabled": "محدودیت نرخ غیرفعال شد", "disabledDescription": "محدودیت نرخ غیرفعال شده است"}}