# Deepchat 数据同步功能说明文档

## 功能概述

数据同步功能允许用户将应用程序的数据（包括聊天记录和配置信息）备份到指定的同步文件夹中，并可以在其他设备上导入这些数据。这个功能主要用于以下场景：

1. 数据备份：防止数据丢失
2. 跨设备同步：在多台设备之间共享相同的聊天记录和配置
3. 迁移数据：更换设备或重装系统时迁移数据

## 技术实现

### 核心组件

1. **SyncPresenter**：负责处理同步逻辑的主要类，包括备份和导入功能
2. **ConfigPresenter扩展**：添加了同步相关的配置项
3. **DataSettings.vue**：提供用户界面，允许用户控制同步设置

### 数据流程

#### 备份流程

1. 用户启用同步功能并设置同步文件夹
2. 当数据发生变化时（如添加/修改消息、创建会话等），系统会在一段时间无变更后（默认60秒）自动触发备份
3. 备份过程中会创建临时文件，完成后再替换最终文件，避免导入过程中的文件冲突
4. 备份的数据包括：
   - 数据库文件（chat.db）
   - 配置文件（xxx.json,provider-models/xxx.json）

#### 导入流程

1. 用户在设置界面中点击"导入数据"
2. 系统会检查同步文件夹中是否存在有效的备份文件
3. 导入前会先备份当前数据，以便导入失败时恢复
4. 导入完成后需要重启应用以应用更改

### 安全措施

1. 备份过程使用临时文件，避免导入过程中的文件冲突
2. 导入前会备份当前数据，确保可以恢复
3. 同步文件夹路径不会被同步，避免在不同设备上路径冲突
4. 备份和导入操作有完整的错误处理和状态反馈

## 用户界面

数据同步功能的用户界面位于设置页面的"数据"选项卡中，包括以下功能：

1. 同步功能开关：启用/禁用同步功能
2. 同步文件夹设置：选择数据备份的目标文件夹
3. 打开同步文件夹：直接打开文件浏览器查看同步文件夹
4. 上次同步时间：显示最近一次成功备份的时间
5. 手动备份：手动触发数据备份
6. 导入数据：从同步文件夹导入数据（默认增量导入）
   - 增量导入：聊天数据增量导入，不覆盖当前聊天数据，对同一聊天数据不会重复导入。如：导入1月1日备份的数据后再次导入1月2日的备份，不会导致1月1日的对话出现两次
   - 覆盖导入：覆盖当前聊天数据，导入新数据

## 事件系统

系统使用事件机制来通知备份和导入的状态变化：

1. `sync:backup-started`：备份开始
2. `sync:backup-completed`：备份完成
3. `sync:backup-error`：备份出错
4. `sync:import-started`：导入开始
5. `sync:import-completed`：导入完成
6. `sync:import-error`：导入出错

这些事件在主进程和渲染进程之间传递，确保用户界面能够实时反映同步状态。

## 配置项

同步功能相关的配置项包括：

1. `syncEnabled`：是否启用同步功能（布尔值）
2. `syncFolderPath`：同步文件夹路径（字符串）
3. `lastSyncTime`：上次同步时间（时间戳）

这些配置项存储在应用程序的配置文件中，但在备份时会过滤掉`syncFolderPath`，避免在不同设备上路径冲突。

## 使用建议

1. 选择稳定的存储位置作为同步文件夹，如云盘同步目录
2. 定期检查备份状态，确保数据正常备份
3. 导入数据前先确保应用程序没有重要的未保存数据
4. 导入完成后重启应用程序以确保所有更改生效

## 限制和注意事项

1. 同步功能不会实时同步数据，而是在数据变更后的一段时间（默认60秒）进行备份(暂时禁用，会导致比较多的io，目前性价比较低)
2. 导入操作会替换当前的所有数据，包括聊天记录和配置
3. 同步文件夹路径不会被同步，需要在每台设备上单独设置
4. 导入数据后需要重启应用程序才能完全应用更改
