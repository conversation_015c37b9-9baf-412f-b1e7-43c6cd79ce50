name: Deploy to Qiniu CDN

on:
  workflow_dispatch:
    inputs:
      workflow_id:
        description: 'Build workflow run ID to use for artifacts'
        required: true
        type: string
      version:
        description: 'Version number for CDN directory (e.g., 1.0.0)'
        required: true
        type: string

jobs:
  deploy-cdn:
    runs-on: ubuntu-latest
    steps:
      - name: Download artifacts from workflow
        uses: dawidd6/action-download-artifact@v6
        with:
          workflow_conclusion: success
          run_id: ${{ github.event.inputs.workflow_id }}
          path: artifacts

      - name: List downloaded artifacts
        run: find artifacts -type f | sort

      - name: Prepare CDN upload structure
        run: |
          mkdir -p cdn_upload/upgrade2/v${{ github.event.inputs.version }}/{linuxx64,macarm,macx64,winx64,winarm}

          # Process Linux x64 artifacts
          if [ -d "artifacts/deepchat-linux-x64" ]; then
            echo "Processing Linux x64 artifacts..."
            cp -r artifacts/deepchat-linux-x64/* cdn_upload/upgrade2/v${{ github.event.inputs.version }}/linuxx64/ 2>/dev/null || true
          fi

          # Process Mac arm64 artifacts
          if [ -d "artifacts/deepchat-mac-arm64" ]; then
            echo "Processing Mac arm64 artifacts..."
            cp -r artifacts/deepchat-mac-arm64/* cdn_upload/upgrade2/v${{ github.event.inputs.version }}/macarm/ 2>/dev/null || true
          fi

          # Process Mac x64 artifacts
          if [ -d "artifacts/deepchat-mac-x64" ]; then
            echo "Processing Mac x64 artifacts..."
            cp -r artifacts/deepchat-mac-x64/* cdn_upload/upgrade2/v${{ github.event.inputs.version }}/macx64/ 2>/dev/null || true
          fi

          # Process Windows x64 artifacts
          if [ -d "artifacts/deepchat-win-x64" ]; then
            echo "Processing Windows x64 artifacts..."
            cp -r artifacts/deepchat-win-x64/* cdn_upload/upgrade2/v${{ github.event.inputs.version }}/winx64/ 2>/dev/null || true
          fi

          # Process Windows arm64 artifacts
          if [ -d "artifacts/deepchat-win-arm64" ]; then
            echo "Processing Windows arm64 artifacts..."
            cp -r artifacts/deepchat-win-arm64/* cdn_upload/upgrade2/v${{ github.event.inputs.version }}/winarm/ 2>/dev/null || true
          fi

          echo "CDN upload structure prepared:"
          find cdn_upload -type f | sort

      - name: Prepare download2 structure (executables only)
        run: |
          mkdir -p cdn_upload/download2/v${{ github.event.inputs.version }}

          # Process Linux x64 artifacts - only tar.gz and AppImage
          if [ -d "artifacts/deepchat-linux-x64" ]; then
            echo "Processing Linux x64 executables..."
            cp artifacts/deepchat-linux-x64/*.tar.gz cdn_upload/download2/v${{ github.event.inputs.version }}/ 2>/dev/null || true
            cp artifacts/deepchat-linux-x64/*.AppImage cdn_upload/download2/v${{ github.event.inputs.version }}/ 2>/dev/null || true
          fi

          # Process Mac arm64 artifacts - only dmg
          if [ -d "artifacts/deepchat-mac-arm64" ]; then
            echo "Processing Mac arm64 executables..."
            cp artifacts/deepchat-mac-arm64/*.dmg cdn_upload/download2/v${{ github.event.inputs.version }}/ 2>/dev/null || true
          fi

          # Process Mac x64 artifacts - only dmg
          if [ -d "artifacts/deepchat-mac-x64" ]; then
            echo "Processing Mac x64 executables..."
            cp artifacts/deepchat-mac-x64/*.dmg cdn_upload/download2/v${{ github.event.inputs.version }}/ 2>/dev/null || true
          fi

          # Process Windows x64 artifacts - only exe
          if [ -d "artifacts/deepchat-win-x64" ]; then
            echo "Processing Windows x64 executables..."
            cp artifacts/deepchat-win-x64/*.exe cdn_upload/download2/v${{ github.event.inputs.version }}/ 2>/dev/null || true
          fi

          # Process Windows arm64 artifacts - only exe
          if [ -d "artifacts/deepchat-win-arm64" ]; then
            echo "Processing Windows arm64 executables..."
            cp artifacts/deepchat-win-arm64/*.exe cdn_upload/download2/v${{ github.event.inputs.version }}/ 2>/dev/null || true
          fi

          echo "Download2 structure prepared (executables only):"
          find cdn_upload/download2 -type f | sort

      - name: Upload to Qiniu CDN
        uses: hujiulong/action-qiniu-upload@master
        with:
          # Your qiniu access key, required.
          access_key: ${{ secrets.QINIU_CDN_AK }}

          # Your qiniu secret key, required.
          secret_key: ${{ secrets.QINIU_CDN_SK }}

          # Bucket name, required.
          bucket: ${{ secrets.QINIU_BUCKET }}

          # The local directory you want to upload to bucket.
          source_dir: 'cdn_upload'

          # The directory inside of the bucket you want to upload to.
          dest_dir: '/'

          # Whether to ignore source maps.
          ignore_source_map: false

      - name: Upload Summary
        run: |
          echo "✅ CDN 部署完成！"
          echo "📦 版本: v${{ github.event.inputs.version }}"
          echo ""
          echo "🗂️ CDN 目录结构:"
          echo "📁 upgrade2/v${{ github.event.inputs.version }}/ (完整产物)"
          echo "  - linuxx64/ (所有 Linux x64 文件)"
          echo "  - macarm/ (所有 Mac ARM64 文件)"
          echo "  - macx64/ (所有 Mac x64 文件)"
          echo "  - winx64/ (所有 Windows x64 文件)"
          echo "  - winarm/ (所有 Windows ARM64 文件)"
          echo ""
          echo "📁 download2/v${{ github.event.inputs.version }}/ (仅可执行文件)"
          echo "  - *.tar.gz (Linux 压缩包)"
          echo "  - *.AppImage (Linux 可执行文件)"
          echo "  - *.dmg (Mac 安装包)"
          echo "  - *.exe (Windows 可执行文件)"
