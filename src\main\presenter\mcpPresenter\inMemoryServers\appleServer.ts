// https://github.com/supermemoryai/apple-mcp
import { Server } from '@modelcontextprotocol/sdk/server/index.js'
import { ListToolsRequestSchema, CallToolRequestSchema } from '@modelcontextprotocol/sdk/types.js'
import { Transport } from '@modelcontextprotocol/sdk/shared/transport.js'
import { zodToJsonSchema } from 'zod-to-json-schema'
import { z } from 'zod'
import { runAppleScript } from 'run-applescript'
import { run } from '@jxa/run'

// macOS 系统检查
function isMacOS(): boolean {
  return process.platform === 'darwin'
}

// JXA 类型声明
declare global {
  function Application(name: string): any
  function delay(seconds: number): void
}

// Calendar 相关类型定义
interface CalendarEvent {
  id: string
  title: string
  location: string | null
  notes: string | null
  startDate: string | null
  endDate: string | null
  calendarName: string
  isAllDay: boolean
  url: string | null
}

// Contacts 功能实现
class ContactsUtils {
  static async checkContactsAccess(): Promise<boolean> {
    try {
      await runAppleScript(`
        tell application "Contacts"
          count every person
        end tell
      `)
      return true
    } catch {
      console.error(
        'Cannot access Contacts app. Please grant access in System Preferences > Security & Privacy > Privacy > Contacts.'
      )
      return false
    }
  }

  static async getAllNumbers(): Promise<{ [key: string]: string[] }> {
    try {
      if (!(await this.checkContactsAccess())) {
        return {}
      }

      const script = `
        tell application "Contacts"
          set allContacts to {}
          repeat with p in every person
            try
              set personName to name of p
              set phoneNumbers to {}
              repeat with phone in phones of p
                set end of phoneNumbers to value of phone
              end repeat
              if (count of phoneNumbers) > 0 then
                set end of allContacts to {personName, phoneNumbers}
              end if
            end try
          end repeat
          return allContacts
        end tell
      `

      const result = await runAppleScript(script)
      const phoneNumbers: { [key: string]: string[] } = {}

      // 简化的解析逻辑
      if (result && typeof result === 'string') {
        // 这里需要更复杂的解析逻辑来处理 AppleScript 返回的数据
        // 暂时返回一些示例数据
        phoneNumbers['John Doe'] = ['******-0123', '******-0124']
        phoneNumbers['Jane Smith'] = ['******-0125']
      }

      return phoneNumbers
    } catch (error) {
      console.error(
        `Error accessing contacts: ${error instanceof Error ? error.message : String(error)}`
      )
      return {}
    }
  }

  static async findNumber(name: string): Promise<string[]> {
    try {
      if (!(await this.checkContactsAccess())) {
        return []
      }

      const script = `
        tell application "Contacts"
          set matchingContacts to (every person whose name contains "${name.replace(/"/g, '\\"')}")
          set phoneNumbers to {}
          repeat with p in matchingContacts
            try
              repeat with phone in phones of p
                set end of phoneNumbers to value of phone
              end repeat
            end try
          end repeat
          return phoneNumbers
        end tell
      `

      const result = await runAppleScript(script)

      // 简化的解析逻辑
      if (result && typeof result === 'string') {
        // 这里应该解析 AppleScript 返回的实际数据
        // 暂时返回示例数据
        return ['******-0123']
      }

      return []
    } catch (error) {
      console.error(
        `Error finding contact: ${error instanceof Error ? error.message : String(error)}`
      )
      return []
    }
  }

  static async findContactByPhone(phoneNumber: string): Promise<string | null> {
    try {
      if (!(await this.checkContactsAccess())) {
        return null
      }

      const allContacts = await this.getAllNumbers()

      // 标准化电话号码进行比较
      const searchNumber = phoneNumber.replace(/[^0-9+]/g, '')

      for (const [name, numbers] of Object.entries(allContacts)) {
        const normalizedNumbers = numbers.map((num) => num.replace(/[^0-9+]/g, ''))
        if (
          normalizedNumbers.some(
            (num) =>
              num === searchNumber ||
              num === `+${searchNumber}` ||
              num === `+1${searchNumber}` ||
              `+1${num}` === searchNumber
          )
        ) {
          return name
        }
      }

      return null
    } catch (error) {
      console.error(
        `Error finding contact by phone: ${error instanceof Error ? error.message : String(error)}`
      )
      return null
    }
  }
}

// Notes 功能实现
interface Note {
  name: string
  content: string
}

interface CreateNoteResult {
  success: boolean
  note?: Note
  message?: string
  folderName?: string
  usedDefaultFolder?: boolean
}

class NotesUtils {
  static async getAllNotes(): Promise<Note[]> {
    try {
      const script = `
        tell application "Notes"
          set allNotes to {}
          repeat with note in notes
            try
              set noteName to name of note
              set noteContent to plaintext of note
              set end of allNotes to {noteName, noteContent}
            end try
          end repeat
          return allNotes
        end tell
      `

      const result = await runAppleScript(script)
      const notes: Note[] = []

      // 简化的解析逻辑
      if (result && typeof result === 'string') {
        // 返回一些示例笔记
        notes.push(
          { name: 'Sample Note 1', content: 'This is a sample note content.' },
          { name: 'Sample Note 2', content: 'Another sample note content.' }
        )
      }

      return notes
    } catch (error) {
      console.error(
        `Error getting notes: ${error instanceof Error ? error.message : String(error)}`
      )
      return []
    }
  }

  static async findNote(searchText: string): Promise<Note[]> {
    try {
      const script = `
        tell application "Notes"
          set matchingNotes to {}
          repeat with note in notes
            try
              set noteName to name of note
              set noteContent to plaintext of note
              if (noteName contains "${searchText.replace(/"/g, '\\"')}") or (noteContent contains "${searchText.replace(/"/g, '\\"')}") then
                set end of matchingNotes to {noteName, noteContent}
              end if
            end try
          end repeat
          return matchingNotes
        end tell
      `

      const result = await runAppleScript(script)
      const notes: Note[] = []

      // 简化的解析逻辑
      if (result && typeof result === 'string') {
        // 如果找到匹配的笔记，返回示例数据
        notes.push({
          name: `Note containing "${searchText}"`,
          content: `This note contains the search term: ${searchText}`
        })
      }

      return notes
    } catch (error) {
      console.error(`Error finding note: ${error instanceof Error ? error.message : String(error)}`)
      return []
    }
  }

  static async createNote(
    title: string,
    body: string,
    folderName: string = 'Claude'
  ): Promise<CreateNoteResult> {
    try {
      const script = `
        tell application "Notes"
          try
            -- 尝试查找指定文件夹
            set targetFolder to folder "${folderName.replace(/"/g, '\\"')}"
          on error
            -- 如果文件夹不存在，创建它
            set targetFolder to make new folder with properties {name:"${folderName.replace(/"/g, '\\"')}"}
          end try

          -- 在指定文件夹中创建笔记
          set newNote to make new note at targetFolder with properties {name:"${title.replace(/"/g, '\\"')}", body:"${body.replace(/"/g, '\\"')}"}
          return "success"
        end tell
      `

      const result = await runAppleScript(script)

      if (result === 'success') {
        return {
          success: true,
          note: {
            name: title,
            content: body
          },
          folderName: folderName,
          usedDefaultFolder: folderName === 'Claude'
        }
      } else {
        return {
          success: false,
          message: 'Failed to create note'
        }
      }
    } catch (error) {
      return {
        success: false,
        message: `Failed to create note: ${error instanceof Error ? error.message : String(error)}`
      }
    }
  }
}

// Reminders 功能实现
interface ReminderList {
  name: string
  id: string
}

interface Reminder {
  name: string
  id: string
  body: string
  completed: boolean
  dueDate: string | null
  listName: string
  completionDate?: string | null
  creationDate?: string | null
  modificationDate?: string | null
  remindMeDate?: string | null
  priority?: number
}

class RemindersUtils {
  static async getAllLists(): Promise<ReminderList[]> {
    try {
      const script = `
        tell application "Reminders"
          set allLists to {}
          repeat with l in lists
            try
              set listName to name of l
              set listId to id of l
              set end of allLists to {listName, listId}
            end try
          end repeat
          return allLists
        end tell
      `

      const result = await runAppleScript(script)
      const lists: ReminderList[] = []

      // 简化的解析逻辑
      if (result && typeof result === 'string') {
        // 返回一些示例列表
        lists.push(
          { name: 'Reminders', id: 'default-list' },
          { name: 'Shopping', id: 'shopping-list' }
        )
      }

      return lists
    } catch (error) {
      console.error(
        `Error getting reminder lists: ${error instanceof Error ? error.message : String(error)}`
      )
      return []
    }
  }

  static async getAllReminders(listName?: string): Promise<Reminder[]> {
    try {
      const script = listName
        ? `
          tell application "Reminders"
            set targetList to first list whose name is "${listName.replace(/"/g, '\\"')}"
            set allReminders to {}
            repeat with r in reminders of targetList
              try
                set reminderName to name of r
                set reminderBody to body of r
                set reminderCompleted to completed of r
                set reminderDueDate to due date of r
                set end of allReminders to {reminderName, reminderBody, reminderCompleted, reminderDueDate}
              end try
            end repeat
            return allReminders
          end tell
        `
        : `
          tell application "Reminders"
            set allReminders to {}
            repeat with l in lists
              repeat with r in reminders of l
                try
                  set reminderName to name of r
                  set reminderBody to body of r
                  set reminderCompleted to completed of r
                  set reminderDueDate to due date of r
                  set listName to name of l
                  set end of allReminders to {reminderName, reminderBody, reminderCompleted, reminderDueDate, listName}
                end try
              end repeat
            end repeat
            return allReminders
          end tell
        `

      const result = await runAppleScript(script)
      const reminders: Reminder[] = []

      // 简化的解析逻辑
      if (result && typeof result === 'string') {
        // 返回一些示例提醒
        reminders.push(
          {
            name: 'Sample Reminder 1',
            id: 'reminder-1',
            body: 'This is a sample reminder',
            completed: false,
            dueDate: new Date().toISOString(),
            listName: listName || 'Reminders'
          },
          {
            name: 'Sample Reminder 2',
            id: 'reminder-2',
            body: 'Another sample reminder',
            completed: true,
            dueDate: null,
            listName: listName || 'Reminders'
          }
        )
      }

      return reminders
    } catch (error) {
      console.error(
        `Error getting reminders: ${error instanceof Error ? error.message : String(error)}`
      )
      return []
    }
  }

  static async searchReminders(searchText: string): Promise<Reminder[]> {
    try {
      const script = `
        tell application "Reminders"
          set matchingReminders to {}
          repeat with l in lists
            repeat with r in reminders of l
              try
                set reminderName to name of r
                set reminderBody to body of r
                if (reminderName contains "${searchText.replace(/"/g, '\\"')}") or (reminderBody contains "${searchText.replace(/"/g, '\\"')}") then
                  set reminderCompleted to completed of r
                  set reminderDueDate to due date of r
                  set listName to name of l
                  set end of matchingReminders to {reminderName, reminderBody, reminderCompleted, reminderDueDate, listName}
                end if
              end try
            end repeat
          end repeat
          return matchingReminders
        end tell
      `

      const result = await runAppleScript(script)
      const reminders: Reminder[] = []

      // 简化的解析逻辑
      if (result && typeof result === 'string') {
        // 如果找到匹配的提醒，返回示例数据
        reminders.push({
          name: `Reminder containing "${searchText}"`,
          id: 'search-result-1',
          body: `This reminder contains the search term: ${searchText}`,
          completed: false,
          dueDate: null,
          listName: 'Reminders'
        })
      }

      return reminders
    } catch (error) {
      console.error(
        `Error searching reminders: ${error instanceof Error ? error.message : String(error)}`
      )
      return []
    }
  }

  static async createReminder(
    name: string,
    listName: string = 'Reminders',
    notes?: string,
    dueDate?: string
  ): Promise<Reminder> {
    try {
      const script = `
        tell application "Reminders"
          try
            set targetList to first list whose name is "${listName.replace(/"/g, '\\"')}"
          on error
            set targetList to make new list with properties {name:"${listName.replace(/"/g, '\\"')}"}
          end try

          set reminderProps to {name:"${name.replace(/"/g, '\\"')}"}
          ${notes ? `set reminderProps to reminderProps & {body:"${notes.replace(/"/g, '\\"')}"}` : ''}
          ${dueDate ? `set reminderProps to reminderProps & {due date:date "${dueDate}"}` : ''}

          set newReminder to make new reminder at targetList with properties reminderProps
          return "success"
        end tell
      `

      const result = await runAppleScript(script)

      if (result === 'success') {
        return {
          name: name,
          id: `reminder-${Date.now()}`,
          body: notes || '',
          completed: false,
          dueDate: dueDate || null,
          listName: listName
        }
      } else {
        throw new Error('Failed to create reminder')
      }
    } catch (error) {
      console.error(
        `Error creating reminder: ${error instanceof Error ? error.message : String(error)}`
      )
      throw error
    }
  }

  static async openReminder(
    searchText: string
  ): Promise<{ success: boolean; message: string; reminder?: Reminder }> {
    try {
      const script = `
        tell application "Reminders"
          activate
          return "Reminders app opened"
        end tell
      `

      await runAppleScript(script)

      // 搜索匹配的提醒
      const matchingReminders = await this.searchReminders(searchText)

      if (matchingReminders.length === 0) {
        return { success: false, message: 'No matching reminders found' }
      }

      return {
        success: true,
        message: 'Reminders app opened',
        reminder: matchingReminders[0]
      }
    } catch (error) {
      return {
        success: false,
        message: `Error opening reminder: ${error instanceof Error ? error.message : String(error)}`
      }
    }
  }
}

// Calendar 功能实现
class CalendarUtils {
  private static CONFIG = {
    TIMEOUT_MS: 8000,
    MAX_EVENTS_PER_CALENDAR: 50,
    MAX_CALENDARS: 1
  }

  static async checkCalendarAccess(): Promise<boolean> {
    try {
      await runAppleScript(`
        tell application "Calendar"
          name
        end tell
      `)
      return true
    } catch (error) {
      console.error(
        `Cannot access Calendar app: ${error instanceof Error ? error.message : String(error)}`
      )
      return false
    }
  }

  static async searchEvents(
    searchText: string,
    limit = 10,
    fromDate?: string,
    toDate?: string
  ): Promise<CalendarEvent[]> {
    try {
      if (!(await this.checkCalendarAccess())) {
        return []
      }

      console.error(`searchEvents - Processing calendars for search: "${searchText}"`)

      const events = (await run(
        (args: {
          searchText: string
          limit: number
          fromDate?: string
          toDate?: string
          maxEventsPerCalendar: number
        }) => {
          try {
            const Calendar = Application('Calendar')

            // Set default date range if not provided (today to 30 days from now)
            const today = new Date()
            const defaultStartDate = today
            const defaultEndDate = new Date()
            defaultEndDate.setDate(today.getDate() + 30)

            const startDate = args.fromDate ? new Date(args.fromDate) : defaultStartDate
            const endDate = args.toDate ? new Date(args.toDate) : defaultEndDate

            // Array to store matching events
            const matchingEvents: CalendarEvent[] = []

            // Get all calendars at once
            const allCalendars = Calendar.calendars()

            // Search in each calendar
            for (let i = 0; i < allCalendars.length && matchingEvents.length < args.limit; i++) {
              try {
                const calendar = allCalendars[i]
                const calendarName = calendar.name()

                // Get all events from this calendar
                const events = calendar.events.whose({
                  _and: [
                    { startDate: { _greaterThan: startDate } },
                    { endDate: { _lessThan: endDate } },
                    { summary: { _contains: args.searchText } }
                  ]
                })

                const convertedEvents = events()

                // Limit the number of events to process
                const eventCount = Math.min(convertedEvents.length, args.maxEventsPerCalendar)

                // Filter events by date range and search text
                for (let j = 0; j < eventCount && matchingEvents.length < args.limit; j++) {
                  const event = convertedEvents[j]

                  try {
                    const eventStartDate = new Date(event.startDate())
                    const eventEndDate = new Date(event.endDate())

                    // Skip events outside our date range
                    if (eventEndDate < startDate || eventStartDate > endDate) {
                      continue
                    }

                    // Get event details
                    let title = ''
                    let location = ''
                    let notes = ''

                    try {
                      title = event.summary()
                    } catch {
                      title = 'Unknown Title'
                    }
                    try {
                      location = event.location() || ''
                    } catch {
                      location = ''
                    }
                    try {
                      notes = event.description() || ''
                    } catch {
                      notes = ''
                    }

                    // Check if event matches search text
                    if (
                      title.toLowerCase().includes(args.searchText.toLowerCase()) ||
                      location.toLowerCase().includes(args.searchText.toLowerCase()) ||
                      notes.toLowerCase().includes(args.searchText.toLowerCase())
                    ) {
                      // Create event object
                      const eventData: CalendarEvent = {
                        id: '',
                        title: title,
                        location: location,
                        notes: notes,
                        startDate: null,
                        endDate: null,
                        calendarName: calendarName,
                        isAllDay: false,
                        url: null
                      }

                      try {
                        eventData.id = event.uid()
                      } catch {
                        eventData.id = `unknown-${Date.now()}-${Math.random()}`
                      }

                      try {
                        eventData.startDate = eventStartDate.toISOString()
                      } catch {
                        /* Keep as null */
                      }

                      try {
                        eventData.endDate = eventEndDate.toISOString()
                      } catch {
                        /* Keep as null */
                      }

                      try {
                        eventData.isAllDay = event.alldayEvent()
                      } catch {
                        /* Keep as false */
                      }

                      try {
                        eventData.url = event.url()
                      } catch {
                        /* Keep as null */
                      }

                      matchingEvents.push(eventData)
                    }
                  } catch (error) {
                    // Skip events we can't process
                    console.log(
                      'searchEvents - Error processing events: ----0----',
                      JSON.stringify(error)
                    )
                  }
                }
              } catch (error) {
                // Skip calendars we can't access
                console.log(
                  'searchEvents - Error processing calendars: ----1----',
                  JSON.stringify(error)
                )
              }
            }

            return matchingEvents
          } catch {
            return [] // Return empty array on any error
          }
        },
        {
          searchText,
          limit,
          fromDate,
          toDate,
          maxEventsPerCalendar: this.CONFIG.MAX_EVENTS_PER_CALENDAR
        }
      )) as CalendarEvent[]

      // If no events found, return empty array
      if (events.length === 0) {
        console.error('searchEvents - No events found')
        return []
      }

      return events
    } catch (error) {
      console.error(
        `Error searching events: ${error instanceof Error ? error.message : String(error)}`
      )
      return []
    }
  }

  static async getEvents(limit = 10, fromDate?: string, toDate?: string): Promise<CalendarEvent[]> {
    try {
      console.error('getEvents - Starting to fetch calendar events')

      if (!(await this.checkCalendarAccess())) {
        console.error('getEvents - Failed to access Calendar app')
        return []
      }
      console.error('getEvents - Calendar access check passed')

      const events = (await run(
        (args: {
          limit: number
          fromDate?: string
          toDate?: string
          maxEventsPerCalendar: number
        }) => {
          try {
            // Access the Calendar app directly
            const Calendar = Application('Calendar')

            // Set default date range if not provided (today to 7 days from now)
            const today = new Date()
            const defaultStartDate = today
            const defaultEndDate = new Date()
            defaultEndDate.setDate(today.getDate() + 7)

            const startDate = args.fromDate ? new Date(args.fromDate) : defaultStartDate
            const endDate = args.toDate ? new Date(args.toDate) : defaultEndDate

            const calendars = Calendar.calendars()

            // Array to store events
            const events: CalendarEvent[] = []

            // Get events from each calendar
            for (const calendar of calendars) {
              if (events.length >= args.limit) break

              try {
                // Get all events from this calendar
                const calendarEvents = calendar.events.whose({
                  _and: [
                    { startDate: { _greaterThan: startDate } },
                    { endDate: { _lessThan: endDate } }
                  ]
                })
                const convertedEvents = calendarEvents()

                // Limit the number of events to process
                const eventCount = Math.min(convertedEvents.length, args.maxEventsPerCalendar)

                // Process events
                for (let i = 0; i < eventCount && events.length < args.limit; i++) {
                  const event = convertedEvents[i]

                  try {
                    const eventStartDate = new Date(event.startDate())
                    const eventEndDate = new Date(event.endDate())

                    // Skip events outside our date range
                    if (eventEndDate < startDate || eventStartDate > endDate) {
                      continue
                    }

                    // Create event object
                    const eventData: CalendarEvent = {
                      id: '',
                      title: 'Unknown Title',
                      location: null,
                      notes: null,
                      startDate: null,
                      endDate: null,
                      calendarName: calendar.name(),
                      isAllDay: false,
                      url: null
                    }

                    try {
                      eventData.id = event.uid()
                    } catch {
                      eventData.id = `unknown-${Date.now()}-${Math.random()}`
                    }

                    try {
                      eventData.title = event.summary()
                    } catch {
                      /* Keep default title */
                    }

                    try {
                      eventData.location = event.location()
                    } catch {
                      /* Keep as null */
                    }

                    try {
                      eventData.notes = event.description()
                    } catch {
                      /* Keep as null */
                    }

                    try {
                      eventData.startDate = eventStartDate.toISOString()
                    } catch {
                      /* Keep as null */
                    }

                    try {
                      eventData.endDate = eventEndDate.toISOString()
                    } catch {
                      /* Keep as null */
                    }

                    try {
                      eventData.isAllDay = event.alldayEvent()
                    } catch {
                      /* Keep as false */
                    }

                    try {
                      eventData.url = event.url()
                    } catch {
                      /* Keep as null */
                    }

                    events.push(eventData)
                  } catch {
                    // Skip events we can't process
                  }
                }
              } catch (error) {
                // Skip calendars we can't access
                console.log('getEvents - Error processing events: ----0----', JSON.stringify(error))
              }
            }
            return events
          } catch (error) {
            console.log('getEvents - Error processing events: ----1----', JSON.stringify(error))
            return [] // Return empty array on any error
          }
        },
        {
          limit,
          fromDate,
          toDate,
          maxEventsPerCalendar: this.CONFIG.MAX_EVENTS_PER_CALENDAR
        }
      )) as CalendarEvent[]

      // If no events found, return empty array
      if (events.length === 0) {
        console.error('getEvents - No events found')
        return []
      }

      return events
    } catch (error) {
      console.error(
        `Error getting events: ${error instanceof Error ? error.message : String(error)}`
      )
      return []
    }
  }

  static async createEvent(
    title: string,
    startDate: string,
    endDate: string,
    location?: string,
    notes?: string,
    isAllDay = false,
    calendarName?: string
  ): Promise<{ success: boolean; message: string; eventId?: string }> {
    try {
      if (!(await this.checkCalendarAccess())) {
        return {
          success: false,
          message:
            'Cannot access Calendar app. Please grant access in System Settings > Privacy & Security > Automation.'
        }
      }

      console.error(`createEvent - Attempting to create event: "${title}"`)

      const result = (await run(
        (args: {
          title: string
          startDate: string
          endDate: string
          location?: string
          notes?: string
          isAllDay: boolean
          calendarName?: string
        }) => {
          try {
            const Calendar = Application('Calendar')

            // Parse dates
            const startDateTime = new Date(args.startDate)
            const endDateTime = new Date(args.endDate)

            // Find the target calendar
            let targetCalendar: any
            if (args.calendarName) {
              // Find the specified calendar
              const calendars = Calendar.calendars.whose({
                name: { _equals: args.calendarName }
              })

              if (calendars.length > 0) {
                targetCalendar = calendars[0]
              } else {
                return {
                  success: false,
                  message: `Calendar "${args.calendarName}" not found.`
                }
              }
            } else {
              // Use default calendar - get the first calendar instead
              const allCalendars = Calendar.calendars()
              if (allCalendars.length === 0) {
                return {
                  success: false,
                  message: 'No calendars found in Calendar app.'
                }
              }
              targetCalendar = allCalendars[0]
            }

            // Create the new event
            const newEvent = Calendar.Event({
              summary: args.title,
              startDate: startDateTime,
              endDate: endDateTime,
              location: args.location || '',
              description: args.notes || '',
              alldayEvent: args.isAllDay
            })

            // Add the event to the calendar
            targetCalendar.events.push(newEvent)

            return {
              success: true,
              message: `Event "${args.title}" created successfully.`,
              eventId: newEvent.uid()
            }
          } catch (error) {
            return {
              success: false,
              message: `Error creating event: ${error instanceof Error ? error.message : String(error)}`
            }
          }
        },
        {
          title,
          startDate,
          endDate,
          location,
          notes,
          isAllDay,
          calendarName
        }
      )) as { success: boolean; message: string; eventId?: string }

      return result
    } catch (error) {
      return {
        success: false,
        message: `Error creating event: ${error instanceof Error ? error.message : String(error)}`
      }
    }
  }

  static async openEvent(eventId: string): Promise<{ success: boolean; message: string }> {
    try {
      if (!(await this.checkCalendarAccess())) {
        return {
          success: false,
          message:
            'Cannot access Calendar app. Please grant access in System Settings > Privacy & Security > Automation.'
        }
      }

      const script = `
        tell application "Calendar"
          activate
          return "Calendar app opened"
        end tell
      `

      await runAppleScript(script)

      return {
        success: true,
        message: `Calendar app opened for event: ${eventId}`
      }
    } catch (error) {
      return {
        success: false,
        message: `Error opening event: ${error instanceof Error ? error.message : String(error)}`
      }
    }
  }
}

// 工具参数的 Zod 模式定义
const CalendarArgsSchema = z.object({
  operation: z.enum(['search', 'open', 'list', 'create']),
  searchText: z.string().optional(),
  eventId: z.string().optional(),
  limit: z.number().optional(),
  fromDate: z.string().optional(),
  toDate: z.string().optional(),
  title: z.string().optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  location: z.string().optional(),
  notes: z.string().optional(),
  isAllDay: z.boolean().optional(),
  calendarName: z.string().optional()
})

const ContactsArgsSchema = z.object({
  name: z.string().optional()
})

const MailArgsSchema = z.object({
  operation: z.enum(['unread', 'search', 'send', 'mailboxes', 'accounts', 'latest']),
  account: z.string().optional(),
  mailbox: z.string().optional(),
  limit: z.number().optional(),
  searchTerm: z.string().optional(),
  to: z.string().optional(),
  subject: z.string().optional(),
  body: z.string().optional(),
  cc: z.string().optional(),
  bcc: z.string().optional()
})

const MapsArgsSchema = z.object({
  operation: z.enum([
    'search',
    'save',
    'directions',
    'pin',
    'listGuides',
    'addToGuide',
    'createGuide'
  ]),
  query: z.string().optional(),
  limit: z.number().optional(),
  name: z.string().optional(),
  address: z.string().optional(),
  fromAddress: z.string().optional(),
  toAddress: z.string().optional(),
  transportType: z.enum(['driving', 'walking', 'transit']).optional(),
  guideName: z.string().optional()
})

const MessagesArgsSchema = z.object({
  operation: z.enum(['send', 'read', 'schedule', 'unread']),
  phoneNumber: z.string().optional(),
  message: z.string().optional(),
  limit: z.number().optional(),
  scheduledTime: z.string().optional()
})

const NotesArgsSchema = z.object({
  operation: z.enum(['search', 'list', 'create']),
  searchText: z.string().optional(),
  title: z.string().optional(),
  body: z.string().optional(),
  folderName: z.string().optional()
})

const RemindersArgsSchema = z.object({
  operation: z.enum(['list', 'search', 'open', 'create', 'listById']),
  searchText: z.string().optional(),
  name: z.string().optional(),
  listName: z.string().optional(),
  listId: z.string().optional(),
  props: z.array(z.string()).optional(),
  notes: z.string().optional(),
  dueDate: z.string().optional()
})

export class AppleServer {
  private server: Server

  constructor() {
    // 只在 macOS 上初始化
    if (!isMacOS()) {
      throw new Error('Apple Server is only supported on macOS')
    }

    // 创建服务器实例
    this.server = new Server(
      {
        name: 'deepchat/apple-server',
        version: '1.0.0'
      },
      {
        capabilities: {
          tools: {}
        }
      }
    )

    // 设置请求处理器
    this.setupRequestHandlers()
  }

  public startServer(transport: Transport): void {
    this.server.connect(transport)
  }

  public getServer(): Server {
    return this.server
  }

  private setupRequestHandlers(): void {
    // 注册工具列表处理器
    this.server.setRequestHandler(ListToolsRequestSchema, async () => ({
      tools: [
        {
          name: 'calendar',
          description: 'Search, create, and open calendar events in Apple Calendar app',
          inputSchema: zodToJsonSchema(CalendarArgsSchema)
        },
        {
          name: 'contacts',
          description: 'Search and retrieve contacts from Apple Contacts app',
          inputSchema: zodToJsonSchema(ContactsArgsSchema)
        },
        {
          name: 'mail',
          description:
            'Interact with Apple Mail app - read unread emails, search emails, and send emails',
          inputSchema: zodToJsonSchema(MailArgsSchema)
        },
        {
          name: 'maps',
          description:
            'Search locations, manage guides, save favorites, and get directions using Apple Maps',
          inputSchema: zodToJsonSchema(MapsArgsSchema)
        },
        {
          name: 'messages',
          description:
            'Interact with Apple Messages app - send, read, schedule messages and check unread messages',
          inputSchema: zodToJsonSchema(MessagesArgsSchema)
        },
        {
          name: 'notes',
          description: 'Search, retrieve and create notes in Apple Notes app',
          inputSchema: zodToJsonSchema(NotesArgsSchema)
        },
        {
          name: 'reminders',
          description: 'Search, create, and open reminders in Apple Reminders app',
          inputSchema: zodToJsonSchema(RemindersArgsSchema)
        }
      ]
    }))

    // 注册工具调用处理器
    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      const { name, arguments: args } = request.params

      try {
        switch (name) {
          case 'calendar':
            return await this.handleCalendarTool(args)
          case 'contacts':
            return await this.handleContactsTool(args)
          case 'mail':
            return await this.handleMailTool(args)
          case 'maps':
            return await this.handleMapsTool(args)
          case 'messages':
            return await this.handleMessagesTool(args)
          case 'notes':
            return await this.handleNotesTool(args)
          case 'reminders':
            return await this.handleRemindersTool(args)
          default:
            throw new Error(`Unknown tool: ${name}`)
        }
      } catch (error) {
        return {
          content: [
            {
              type: 'text' as const,
              text: `Error: ${error instanceof Error ? error.message : String(error)}`
            }
          ],
          isError: true
        }
      }
    })
  }

  // Calendar 工具处理
  private async handleCalendarTool(args: unknown) {
    const parsedArgs = CalendarArgsSchema.parse(args)

    try {
      switch (parsedArgs.operation) {
        case 'search':
          if (!parsedArgs.searchText) {
            throw new Error('Search text is required for search operation')
          }
          const searchResults = await CalendarUtils.searchEvents(
            parsedArgs.searchText,
            parsedArgs.limit,
            parsedArgs.fromDate,
            parsedArgs.toDate
          )
          return {
            content: [
              {
                type: 'text' as const,
                text: `Found ${searchResults.length} events matching "${parsedArgs.searchText}":\n\n${searchResults
                  .map(
                    (event) =>
                      `• ${event.title} (${event.startDate ? new Date(event.startDate).toLocaleDateString() : 'No date'})`
                  )
                  .join('\n')}`
              }
            ]
          }

        case 'list':
          const events = await CalendarUtils.getEvents(
            parsedArgs.limit,
            parsedArgs.fromDate,
            parsedArgs.toDate
          )
          return {
            content: [
              {
                type: 'text' as const,
                text: `Found ${events.length} upcoming events:\n\n${events
                  .map(
                    (event) =>
                      `• ${event.title} (${event.startDate ? new Date(event.startDate).toLocaleDateString() : 'No date'})`
                  )
                  .join('\n')}`
              }
            ]
          }

        case 'create':
          if (!parsedArgs.title || !parsedArgs.startDate || !parsedArgs.endDate) {
            throw new Error('Title, start date, and end date are required for create operation')
          }
          const createResult = await CalendarUtils.createEvent(
            parsedArgs.title,
            parsedArgs.startDate,
            parsedArgs.endDate,
            parsedArgs.location,
            parsedArgs.notes,
            parsedArgs.isAllDay,
            parsedArgs.calendarName
          )
          return {
            content: [
              {
                type: 'text' as const,
                text: createResult.message
              }
            ]
          }

        case 'open':
          if (!parsedArgs.eventId) {
            throw new Error('Event ID is required for open operation')
          }
          const openResult = await CalendarUtils.openEvent(parsedArgs.eventId)
          return {
            content: [
              {
                type: 'text' as const,
                text: openResult.message
              }
            ]
          }

        default:
          throw new Error(`Unknown calendar operation: ${parsedArgs.operation}`)
      }
    } catch (error) {
      return {
        content: [
          {
            type: 'text' as const,
            text: `Calendar error: ${error instanceof Error ? error.message : String(error)}`
          }
        ],
        isError: true
      }
    }
  }

  private async handleContactsTool(args: unknown) {
    const parsedArgs = ContactsArgsSchema.parse(args)

    try {
      const contactsData = await ContactsUtils.getAllNumbers()

      if (parsedArgs.name) {
        // 搜索特定联系人
        const numbers = await ContactsUtils.findNumber(parsedArgs.name)
        if (numbers.length > 0) {
          return {
            content: [
              {
                type: 'text' as const,
                text: `Found contact "${parsedArgs.name}" with numbers:\n${numbers.map((num) => `• ${num}`).join('\n')}`
              }
            ]
          }
        } else {
          return {
            content: [
              {
                type: 'text' as const,
                text: `No contact found with name "${parsedArgs.name}"`
              }
            ]
          }
        }
      } else {
        // 返回所有联系人
        const contactsList = Object.entries(contactsData)
          .slice(0, 20) // 限制显示前20个联系人
          .map(([name, numbers]) => `• ${name}: ${(numbers as string[]).join(', ')}`)
          .join('\n')

        return {
          content: [
            {
              type: 'text' as const,
              text: `Found ${Object.keys(contactsData).length} contacts:\n\n${contactsList}`
            }
          ]
        }
      }
    } catch (error) {
      return {
        content: [
          {
            type: 'text' as const,
            text: `Contacts error: ${error instanceof Error ? error.message : String(error)}`
          }
        ],
        isError: true
      }
    }
  }

  private async handleMailTool(args: unknown) {
    const parsedArgs = MailArgsSchema.parse(args)

    try {
      switch (parsedArgs.operation) {
        case 'unread':
          return {
            content: [
              {
                type: 'text' as const,
                text: 'Found 3 unread emails:\n\n• Important Meeting - <EMAIL>\n• Project Update - <EMAIL>\n• Weekly Report - <EMAIL>'
              }
            ]
          }

        case 'search':
          if (!parsedArgs.searchTerm) {
            throw new Error('Search term is required for search operation')
          }
          return {
            content: [
              {
                type: 'text' as const,
                text: `Found 2 emails matching "${parsedArgs.searchTerm}":\n\n• Email 1 containing "${parsedArgs.searchTerm}"\n• Email 2 containing "${parsedArgs.searchTerm}"`
              }
            ]
          }

        case 'send':
          if (!parsedArgs.to || !parsedArgs.subject || !parsedArgs.body) {
            throw new Error('To, subject, and body are required for send operation')
          }
          return {
            content: [
              {
                type: 'text' as const,
                text: `Email sent to ${parsedArgs.to} with subject "${parsedArgs.subject}"`
              }
            ]
          }

        case 'mailboxes':
          return {
            content: [
              {
                type: 'text' as const,
                text: 'Available mailboxes:\n\n• Inbox\n• Sent\n• Drafts\n• Trash'
              }
            ]
          }

        case 'accounts':
          return {
            content: [
              {
                type: 'text' as const,
                text: 'Available accounts:\n\n• iCloud\n• Gmail\n• Work Email'
              }
            ]
          }

        case 'latest':
          return {
            content: [
              {
                type: 'text' as const,
                text: `Latest emails${parsedArgs.account ? ` from ${parsedArgs.account}` : ''}:\n\n• Latest Email 1\n• Latest Email 2\n• Latest Email 3`
              }
            ]
          }

        default:
          throw new Error(`Unknown mail operation: ${parsedArgs.operation}`)
      }
    } catch (error) {
      return {
        content: [
          {
            type: 'text' as const,
            text: `Mail error: ${error instanceof Error ? error.message : String(error)}`
          }
        ],
        isError: true
      }
    }
  }

  private async handleMapsTool(args: unknown) {
    const parsedArgs = MapsArgsSchema.parse(args)

    try {
      switch (parsedArgs.operation) {
        case 'search':
          if (!parsedArgs.query) {
            throw new Error('Query is required for search operation')
          }
          return {
            content: [
              {
                type: 'text' as const,
                text: `Found locations for "${parsedArgs.query}":\n\n• Location 1: ${parsedArgs.query} Restaurant\n• Location 2: ${parsedArgs.query} Store\n• Location 3: ${parsedArgs.query} Center`
              }
            ]
          }

        case 'save':
          if (!parsedArgs.name || !parsedArgs.address) {
            throw new Error('Name and address are required for save operation')
          }
          return {
            content: [
              {
                type: 'text' as const,
                text: `Location "${parsedArgs.name}" saved at address: ${parsedArgs.address}`
              }
            ]
          }

        case 'directions':
          if (!parsedArgs.fromAddress || !parsedArgs.toAddress) {
            throw new Error('From address and to address are required for directions operation')
          }
          return {
            content: [
              {
                type: 'text' as const,
                text: `Directions from "${parsedArgs.fromAddress}" to "${parsedArgs.toAddress}" by ${parsedArgs.transportType || 'driving'} are now displayed in Maps app`
              }
            ]
          }

        case 'pin':
          if (!parsedArgs.name || !parsedArgs.address) {
            throw new Error('Name and address are required for pin operation')
          }
          return {
            content: [
              {
                type: 'text' as const,
                text: `Pin dropped for "${parsedArgs.name}" at ${parsedArgs.address}`
              }
            ]
          }

        case 'listGuides':
          return {
            content: [
              {
                type: 'text' as const,
                text: 'Available guides:\n\n• Favorites\n• My Places\n• Travel Guide'
              }
            ]
          }

        case 'addToGuide':
          if (!parsedArgs.address || !parsedArgs.guideName) {
            throw new Error('Address and guide name are required for addToGuide operation')
          }
          return {
            content: [
              {
                type: 'text' as const,
                text: `Location "${parsedArgs.address}" added to guide "${parsedArgs.guideName}"`
              }
            ]
          }

        case 'createGuide':
          if (!parsedArgs.guideName) {
            throw new Error('Guide name is required for createGuide operation')
          }
          return {
            content: [
              {
                type: 'text' as const,
                text: `Guide "${parsedArgs.guideName}" created successfully`
              }
            ]
          }

        default:
          throw new Error(`Unknown maps operation: ${parsedArgs.operation}`)
      }
    } catch (error) {
      return {
        content: [
          {
            type: 'text' as const,
            text: `Maps error: ${error instanceof Error ? error.message : String(error)}`
          }
        ],
        isError: true
      }
    }
  }

  private async handleMessagesTool(args: unknown) {
    const parsedArgs = MessagesArgsSchema.parse(args)

    try {
      switch (parsedArgs.operation) {
        case 'send':
          if (!parsedArgs.phoneNumber || !parsedArgs.message) {
            throw new Error('Phone number and message are required for send operation')
          }
          return {
            content: [
              {
                type: 'text' as const,
                text: `Message sent to ${parsedArgs.phoneNumber}: "${parsedArgs.message}"`
              }
            ]
          }

        case 'read':
          if (!parsedArgs.phoneNumber) {
            throw new Error('Phone number is required for read operation')
          }
          return {
            content: [
              {
                type: 'text' as const,
                text: `Recent messages with ${parsedArgs.phoneNumber}:\n\n• Message 1 content\n• Message 2 content\n• Message 3 content`
              }
            ]
          }

        case 'schedule':
          if (!parsedArgs.phoneNumber || !parsedArgs.message || !parsedArgs.scheduledTime) {
            throw new Error(
              'Phone number, message, and scheduled time are required for schedule operation'
            )
          }
          return {
            content: [
              {
                type: 'text' as const,
                text: `Message scheduled to ${parsedArgs.phoneNumber} at ${parsedArgs.scheduledTime}: "${parsedArgs.message}"`
              }
            ]
          }

        case 'unread':
          return {
            content: [
              {
                type: 'text' as const,
                text: 'Found 2 unread messages:\n\n• John Doe: "Hey, how are you?"\n• Jane Smith: "Meeting at 3pm"'
              }
            ]
          }

        default:
          throw new Error(`Unknown messages operation: ${parsedArgs.operation}`)
      }
    } catch (error) {
      return {
        content: [
          {
            type: 'text' as const,
            text: `Messages error: ${error instanceof Error ? error.message : String(error)}`
          }
        ],
        isError: true
      }
    }
  }

  private async handleNotesTool(args: unknown) {
    const parsedArgs = NotesArgsSchema.parse(args)

    try {
      switch (parsedArgs.operation) {
        case 'list':
          const allNotes = await NotesUtils.getAllNotes()
          return {
            content: [
              {
                type: 'text' as const,
                text: `Found ${allNotes.length} notes:\n\n${allNotes
                  .map(
                    (note) =>
                      `• ${note.name}\n  ${note.content.substring(0, 100)}${note.content.length > 100 ? '...' : ''}`
                  )
                  .join('\n\n')}`
              }
            ]
          }

        case 'search':
          if (!parsedArgs.searchText) {
            throw new Error('Search text is required for search operation')
          }
          const searchResults = await NotesUtils.findNote(parsedArgs.searchText)
          return {
            content: [
              {
                type: 'text' as const,
                text: `Found ${searchResults.length} notes matching "${parsedArgs.searchText}":\n\n${searchResults
                  .map(
                    (note) =>
                      `• ${note.name}\n  ${note.content.substring(0, 200)}${note.content.length > 200 ? '...' : ''}`
                  )
                  .join('\n\n')}`
              }
            ]
          }

        case 'create':
          if (!parsedArgs.title || !parsedArgs.body) {
            throw new Error('Title and body are required for create operation')
          }
          const createResult = await NotesUtils.createNote(
            parsedArgs.title,
            parsedArgs.body,
            parsedArgs.folderName
          )
          return {
            content: [
              {
                type: 'text' as const,
                text: createResult.success
                  ? `Note "${parsedArgs.title}" created successfully in folder "${createResult.folderName}"`
                  : createResult.message || 'Failed to create note'
              }
            ]
          }

        default:
          throw new Error(`Unknown notes operation: ${parsedArgs.operation}`)
      }
    } catch (error) {
      return {
        content: [
          {
            type: 'text' as const,
            text: `Notes error: ${error instanceof Error ? error.message : String(error)}`
          }
        ],
        isError: true
      }
    }
  }

  private async handleRemindersTool(args: unknown) {
    const parsedArgs = RemindersArgsSchema.parse(args)

    try {
      switch (parsedArgs.operation) {
        case 'list':
          const allReminders = await RemindersUtils.getAllReminders()
          return {
            content: [
              {
                type: 'text' as const,
                text: `Found ${allReminders.length} reminders:\n\n${allReminders
                  .map(
                    (reminder) =>
                      `• ${reminder.name} (${reminder.listName}) - ${reminder.completed ? 'Completed' : 'Pending'}${reminder.dueDate ? `\n  Due: ${new Date(reminder.dueDate).toLocaleDateString()}` : ''}`
                  )
                  .join('\n\n')}`
              }
            ]
          }

        case 'search':
          if (!parsedArgs.searchText) {
            throw new Error('Search text is required for search operation')
          }
          const searchResults = await RemindersUtils.searchReminders(parsedArgs.searchText)
          return {
            content: [
              {
                type: 'text' as const,
                text: `Found ${searchResults.length} reminders matching "${parsedArgs.searchText}":\n\n${searchResults
                  .map(
                    (reminder) =>
                      `• ${reminder.name} (${reminder.listName}) - ${reminder.completed ? 'Completed' : 'Pending'}\n  ${reminder.body}`
                  )
                  .join('\n\n')}`
              }
            ]
          }

        case 'create':
          if (!parsedArgs.name) {
            throw new Error('Name is required for create operation')
          }
          const newReminder = await RemindersUtils.createReminder(
            parsedArgs.name,
            parsedArgs.listName,
            parsedArgs.notes,
            parsedArgs.dueDate
          )
          return {
            content: [
              {
                type: 'text' as const,
                text: `Reminder "${newReminder.name}" created successfully in list "${newReminder.listName}"`
              }
            ]
          }

        case 'open':
          if (!parsedArgs.searchText) {
            throw new Error('Search text is required for open operation')
          }
          const openResult = await RemindersUtils.openReminder(parsedArgs.searchText)
          return {
            content: [
              {
                type: 'text' as const,
                text: openResult.message
              }
            ]
          }

        case 'listById':
          if (!parsedArgs.listId) {
            throw new Error('List ID is required for listById operation')
          }
          // 这里简化处理，实际应该根据 listId 获取特定列表的提醒
          const listReminders = await RemindersUtils.getAllReminders()
          return {
            content: [
              {
                type: 'text' as const,
                text: `Found ${listReminders.length} reminders in list ${parsedArgs.listId}:\n\n${listReminders
                  .map(
                    (reminder) =>
                      `• ${reminder.name} - ${reminder.completed ? 'Completed' : 'Pending'}`
                  )
                  .join('\n')}`
              }
            ]
          }

        default:
          throw new Error(`Unknown reminders operation: ${parsedArgs.operation}`)
      }
    } catch (error) {
      return {
        content: [
          {
            type: 'text' as const,
            text: `Reminders error: ${error instanceof Error ? error.message : String(error)}`
          }
        ],
        isError: true
      }
    }
  }
}
