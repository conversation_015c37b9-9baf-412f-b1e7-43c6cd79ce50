---
description: Best practices for Vue.js applications
globs: src/renderer/src/**/*.{vue,ts,tsx,js,jsx}
alwaysApply: false
---
- Use the Composition API for better code organization and reusability
- Implement proper state management with Pinia
- Utilize Vue Router for navigation and route management
- Leverage Vue's built-in reactivity system for efficient data handling
- Use scoped styles to prevent CSS conflicts between components