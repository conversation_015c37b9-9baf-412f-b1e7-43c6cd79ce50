{"enabled": "Activé", "disabled": "Désactivé", "loading": "Chargement...", "copySuccess": "<PERSON><PERSON><PERSON>", "copySuccessDesc": "Contenu copié dans le presse-papiers", "copyImageSuccess": "<PERSON><PERSON><PERSON>", "copyImageSuccessDesc": "Image copiée dans le presse-papiers", "copyFailed": "Impossible de copier", "copyFailedDesc": "Impossible de copier le contenu dans le presse-papiers", "copyCode": "Copier le code", "copy": "<PERSON><PERSON><PERSON>", "paste": "<PERSON><PERSON>", "export": "Exporter", "newChat": "Nouvelle conversation", "newTopic": "Nouveau sujet", "cancel": "Annuler", "confirm": "Confirmer", "close": "<PERSON><PERSON><PERSON>", "error": {"requestFailed": "La requête a échoué...", "createChatFailed": "Échec de la création de la conversation", "selectChatFailed": "Échec de la sélection de la conversation", "renameChatFailed": "Échec du renommage de la conversation", "deleteChatFailed": "Échec de la suppression de la conversation", "cleanMessagesFailed": "Échec du nettoyage des messages", "userCanceledGeneration": "L'utilisateur a annulé la génération", "sessionInterrupted": "La session a été interrompue de manière inattendue, la génération est incomplète", "noModelResponse": "Le modèle n'a renvoyé aucun contenu, il a peut-être expiré", "invalidJson": "Format JSON invalide", "maximumToolCallsReached": "Nombre maximum d'appels d'outils atteint", "causeOfError": "Causes possibles d'erreur:", "error400": "Demandez une erreur, des paramètres ou des problèmes de compatibilité", "error401": "Authentification Échec, mauvaise clé API ou nom de domaine configuré", "error403": "L'accès à ce modèle peut être dû à un équilibre insuffisant ou sans autorisation d'accès", "error404": "L'adresse de demande n'existe pas, le nom de domaine configuré ou le nom du modèle est incorrect", "error429": "La vitesse de demande est trop rapide et la fréquence d'accès est limitée par le fournisseur de services", "error500": "Il y a une erreur dans le serveur et le service demandé peut être très stable à l'heure actuelle. Vous pouvez l'essayer plus tard", "error502": "<PERSON><PERSON><PERSON> de passerelle, le service demandé peut ne pas être stable à l'heure actuelle, vous pouvez réessayer plus tard", "error503": "Le service n'est pas disponible, le service demandé peut être actuellement instable, vous pouvez l'essayer plus tard", "error504": "La demande a expiré, le service demandé peut être actuellement instable ou le lien réseau est défectueux. Veuillez vérifier le proxy et d'autres configurations réseau avant d'essayer.", "operationFailed": "L'opération a échoué"}, "resetDataConfirmTitle": "Réinitialiser toutes les données ?", "resetDataConfirmDescription": "<PERSON>la ré<PERSON>tialisera toutes vos données aux paramètres par défaut. Cette action ne peut pas être annulée.", "proxyMode": "Mode proxy", "proxyModeSelect": "Sélectionner le mode proxy", "proxyModeSystem": "Proxy système", "proxyModeNone": "Pas de proxy", "proxyModeCustom": "Proxy personnalis<PERSON>", "customProxyUrl": "URL du proxy personnalisé", "customProxyUrlPlaceholder": "Exemple : http://127.0.0.1:7890", "invalidProxyUrl": "URL de proxy invalide, veuil<PERSON>z entrer une URL http/https valide", "disclaimer": "Avertissement", "language": "<PERSON><PERSON>", "languageSelect": "Sé<PERSON>ionner une langue", "resetData": "Réinitialiser les données", "searchAssistantModel": "<PERSON><PERSON><PERSON><PERSON> de recherche", "searchEngine": "Moteur de recherche", "searchEngineSelect": "Sélectionner un moteur de recherche", "searchPreview": "Aperçu de la recherche", "selectModel": "Sélectionner un modèle", "title": "Paramètres généraux", "languageSystem": "<PERSON><PERSON><PERSON> le syst<PERSON>", "watermarkTip": "Généré par <PERSON>", "collapse": "<PERSON><PERSON><PERSON><PERSON>", "expand": "Développer", "image": "image", "add": "Ajouter", "reset": "Réinitialiser", "format": "Format", "edit": "Modifier", "delete": "<PERSON><PERSON><PERSON><PERSON>", "save": "Enregistrer", "clear": "<PERSON><PERSON><PERSON><PERSON>", "saved": "<PERSON><PERSON><PERSON><PERSON>", "closeToQuit": "Quitter l'application lors de la fermeture de la fenêtre"}