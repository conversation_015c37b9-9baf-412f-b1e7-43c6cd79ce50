{"title": "Settings", "common": {"title": "Common Settings", "modelSettings": "Model Settings", "searchSettings": "Search Settings", "networkSettings": "Network Settings", "interfaceSettings": "Interface & Interaction", "personalizationSettings": "Personalization Settings", "securitySettings": "Security & Privacy", "operationSettings": "Operation Settings", "resetData": "Reset Data", "language": "Language", "languageDesc": "Select the application interface language. Supports multiple languages including English, Chinese, Japanese, etc. Changes take effect immediately", "languageSelect": "Select language", "searchEngine": "Search Engine", "searchEngineDesc": "Select a search engine for web search enhancement. Supports major search engines like Google, Bing, and custom search engines", "searchEngineSelect": "Select search engine", "searchPreview": "Search Preview", "searchPreviewDesc": "Enable preview thumbnails in search result pages for quick browsing of search content", "searchAssistantModel": "Assistant Model", "searchAssistantModelDesc": "Choose the model for processing search results and generating answers. Affects the quality and speed of search enhancement", "selectModel": "Select Model", "visionModel": "Vision Model", "visionModelDesc": "Select a model for processing images and visual content, used for image description, analysis and other features", "visionModelNotSelectedWarning": "No vision model selected. You will be prompted to set one up when using image features", "proxyMode": "Proxy Mode", "proxyModeDesc": "Configure network proxy settings. Choose system proxy, no proxy, or custom proxy for accessing overseas services", "proxyModeSelect": "Select proxy mode", "proxyModeSystem": "System Proxy", "proxyModeNone": "No Proxy", "proxyModeCustom": "Custom Proxy", "customProxyUrl": "Custom Proxy URL", "customProxyUrlPlaceholder": "Example: http://127.0.0.1:7890", "invalidProxyUrl": "Invalid proxy URL, please enter valid http/https URL", "addCustomSearchEngine": "Add Custom Search Engine", "addCustomSearchEngineDesc": "Add a new search engine by providing a name and search URL. The URL must include {query} as the query placeholder.", "searchEngineName": "Search Engine Name", "searchEngineNamePlaceholder": "Enter search engine name", "searchEngineUrl": "Search URL", "searchEngineUrlPlaceholder": "Ex: https://a.com/search?q={'{'}query{'}'}", "searchEngineUrlError": "URL must include {'{'}query{'}'} as the query placeholder", "deleteCustomSearchEngine": "Delete Custom Search Engine", "deleteCustomSearchEngineDesc": "Are you sure you want to delete the custom search engine \"{name}\"? This action cannot be undone.", "testSearchEngine": "Test Search Engine", "testSearchEngineDesc": "A test search for \"weather\" will be performed using the {engine} search engine.", "testSearchEngineNote": "If the search page requires login or other actions, you can perform them in the test window. Please close the test window when you're done.", "theme": "Theme", "themeSelect": "Select theme", "closeToQuit": "Exit app when closing window", "closeToQuitDesc": "Choose whether to exit the application or hide to system tray when clicking the close button. Hidden app can be restored from tray", "contentProtectionDialogTitle": "Confirm Screen Protection Change", "contentProtectionEnableDesc": "Prevent screen sharing apps from capturing the DeepChat window to help protect your privacy. Not all apps honor this setting; in some environments a black window may remain.", "contentProtectionDisableDesc": "Allow screen sharing apps to capture the DeepChat window.", "contentProtectionRestartNotice": "Changing this setting will restart the application. Do you want to continue?", "soundEnabled": "Enable Sound Effects", "soundEnabledDesc": "Enable system sound effects for message notifications, error alerts, and other interactions to provide better user experience", "copyWithCotEnabled": "Copy COT Details", "copyWithCotEnabledDesc": "Include Chain of Thought information when copying messages to help understand AI reasoning process", "loggingEnabled": "Enable Logging", "loggingEnabledDesc": "Enable logging to help diagnose issues and improve the application. May contain sensitive information and requires restart to take effect", "devToolsAutoOpen": "Auto Open Developer Tools", "devToolsAutoOpenDesc": "Automatically open developer tools when creating windows and tabs for debugging and development", "loggingDialogTitle": "Confirm Logging Setting Change", "loggingEnableDesc": "Enabling logging will help us diagnose issues and improve the application. Log files may contain sensitive information.", "loggingDisableDesc": "Disabling logging will stop collecting application logs.", "loggingRestartNotice": "Changing this setting will restart the application. Do you want to continue?", "devToolsDialogTitle": "Confirm Developer Tools Setting Change", "devToolsEnableDesc": "Enabling auto-open developer tools will automatically open developer tools when creating new windows and tabs.", "devToolsDisableDesc": "Disabling auto-open developer tools will no longer automatically open developer tools.", "devToolsRestartNotice": "This setting will take effect immediately and apply to newly created windows.", "openLogFolder": "Open Log Folder", "shortcut": {"newChat": "Create a new chat", "title": "Shortcut key settings"}, "notifications": "System Notifications", "notificationsDesc": "When DeepChat is not in the foreground, if a session is generated, a system notification will be sent", "contentProtection": "Screen capture protection", "contentProtectionDesc": "Prevent screen sharing apps from capturing DeepChat window to protect chat content privacy. Requires restart to take effect"}, "data": {"title": "Data Settings", "syncEnable": "Enable Data Sync", "syncFolder": "Sync Folder", "openSyncFolder": "Open Sync Folder", "lastSyncTime": "Last Sync Time", "never": "Never", "startBackup": "Backup Now", "backingUp": "Backing up...", "importData": "Import Data", "incrementImport": "Incremental Import", "overwriteImport": "Overwrite Import", "importConfirmTitle": "Confirm Data Import", "importConfirmDescription": "Importing will overwrite all current data, including chat history and settings. Make sure you have backed up important data. You'll need to restart the application after import.", "importing": "Importing...", "confirmImport": "Confirm Import", "importSuccessTitle": "Import Successful", "importErrorTitle": "Import Failed", "resetData": "Reset Data", "resetConfirmTitle": "Confirm Data Reset", "resetConfirmDescription": "Please select the type of data to reset. This operation cannot be undone and the application will restart automatically after reset.", "resetChatData": "Reset Chat Data", "resetChatDataDesc": "Delete all chat history and conversation records", "resetKnowledgeData": "Reset Knowledge Base Data", "resetKnowledgeDataDesc": "Delete all knowledge base files and vector data", "resetConfig": "Reset Configuration", "resetConfigDesc": "Delete all app settings, model configurations and custom prompts", "resetAll": "Complete Reset", "resetAllDesc": "Delete all data including chat history, configurations and cache files", "resetting": "Resetting...", "confirmReset": "Confirm Reset", "resetCompleteDevTitle": "Data Reset Complete", "resetCompleteDevMessage": "Please manually restart the application in development mode. Stop the current process and run pnpm run dev again"}, "model": {"title": "Model Settings", "systemPrompt": {"label": "System Prompt", "placeholder": "Please enter the system prompt...", "description": "Set the system prompt for the AI assistant to define its behavior and role"}, "temperature": {"label": "Model Temperature", "description": "Controls the randomness of the output; higher values produce more creative responses"}, "contextLength": {"label": "Context Length", "description": "Set the maximum length of the conversation context"}, "responseLength": {"label": "Response Length", "description": "Set the maximum length of the AI response"}, "artifacts": {"description": "Enabling the Artifacts feature allows the AI to generate richer content", "title": "Artifacts"}, "addModel": "Add Model", "configureModel": "Configure Model", "modelList": "Model List", "provider": "Service provider", "providerSetting": "Service provider settings", "selectModel": "Select a model", "modelConfig": {"cancel": "Cancel", "contextLength": {"description": "Set the context length that the model can handle", "label": "Context Length"}, "description": "Please note that this configuration is only valid for the current model and will not affect other models. Please modify it with caution. Incorrect parameters may cause the model to not work properly.", "functionCall": {"description": "Whether the model supports function calls natively (DeepChat will automatically simulate function calls after turning off this option)", "label": "Function Calls"}, "maxTokens": {"description": "Set the maximum number of tokens for a single output of the model", "label": "Maximum output length"}, "reasoning": {"description": "Does the model support reasoning ability?", "label": "Reasoning ability"}, "thinkingBudget": {"label": "Thinking Budget", "description": "Limit the length of model thinking", "dynamic": "Dynamic Thinking", "range": "Range: {min} - {max}", "onlySupported": "Only supported in Gemini 2.5 Flash, 2.5 Pro and 2.5 Flash-Lite", "valueLabel": "Thinking Budget Value", "placeholder": "Enter thinking budget value", "forceEnabled": "Gemini 2.5 series models force enable thinking budget", "dynamicPrefix": "-1 = Dynamic Thinking", "notice": "Notice: ", "warnings": {"proNoDisable": "This model does not support disabling thinking, minimum value 128", "proCannotDisable": "Gemini 2.5 Pro cannot disable thinking function", "flashLiteCannotSetZero": "Gemini 2.5 Flash-Lite cannot be set to 0, minimum value is 512", "modelCannotDisable": "This model cannot disable thinking function", "flashLiteMinValue": "Thinking budget cannot be less than 512 when set to specific value (or use 0 to disable thinking, -1 to enable dynamic thinking)", "belowMin": "Thinking budget cannot be less than {min}{hint}", "aboveMax": "Thinking budget cannot be greater than {max}"}, "hints": {"flashLiteDisable": ", 0 = disable thinking, specific value minimum 512", "normalDisable": ", 0 = disable thinking", "withZeroAndDynamic": "(or use 0 to disable thinking, -1 to enable dynamic thinking)", "withDynamic": "(or use -1 to enable dynamic thinking)"}}, "resetConfirm": {"confirm": "Confirm reset", "message": "Are you sure you want to reset the configuration of this model to the default? \nThis operation is irrevocable.", "title": "Confirm reset"}, "reasoningEffort": {"label": "Reasoning Effort", "description": "Controls the depth of model reasoning; higher effort produces better results but slower responses", "placeholder": "Select reasoning effort", "options": {"minimal": "Minimal - Fastest response", "low": "Low - Low effort", "medium": "Medium - Moderate effort", "high": "High - High effort"}}, "verbosity": {"label": "Verbosity", "description": "Controls the detail level and length of model responses", "placeholder": "Select verbosity level", "options": {"low": "Low - Concise answers", "medium": "Medium - Balanced detail", "high": "High - Detailed answers"}}, "resetToDefault": "Reset to default", "saveConfig": "Save configuration", "useModelDefault": "Use model default configuration", "currentUsingModelDefault": "Currently using model default configuration", "temperature": {"description": "Control the randomness of the output. Most models are 0-1, and some support between 0-2. Higher values increase randomness.", "label": "Temperature"}, "title": "Custom model parameters", "type": {"description": "Select the type of model", "label": "Model Type", "options": {"chat": "Language Model", "embedding": "Embedding Model", "imageGeneration": "Image Generation Model", "rerank": "<PERSON><PERSON>"}}, "validation": {"contextLengthMax": "The context length cannot exceed 10000000", "contextLengthMin": "The context length must be greater than 0", "contextLengthRequired": "The context length cannot be empty", "maxTokensMax": "The maximum output length cannot exceed 1000000", "maxTokensMin": "The maximum output length must be greater than 0", "maxTokensRequired": "The maximum output length cannot be empty", "temperatureMax": "The temperature must be less than or equal to 2", "temperatureMin": "The temperature must be greater than or equal to 0", "temperatureRequired": "The temperature cannot be empty"}, "vision": {"description": "Does the model support visual ability?", "label": "Visual ability"}}}, "provider": {"search": "Search provider platforms…", "enable": "Enable Service", "enabled": "Enabled", "disabled": "Disabled", "urlPlaceholder": "Please enter API URL", "keyPlaceholder": "Please enter API Key", "accessKeyIdPlaceholder": "Please enter AWS Access Key ID", "secretAccessKeyPlaceholder": "Please enter AWS Secret Access Key", "regionPlaceholder": "Please enter AWS region", "verifyKey": "Verify Key", "howToGet": "How to get", "getKeyTip": "Please visit", "getKeyTipEnd": "to get API Key", "urlFormat": "API Example: {defaultUrl}", "modelList": "Model List", "enableModels": "Enable Models", "disableAllModels": "Disable All Models", "modelsEnabled": "Models have been enabled", "noModelsEnabled": {"title": "No models enabled", "description": "Please click the \"Enable Models\" button to manually select the models you want to use."}, "verifyLink": "Verify Link", "syncModelsFailed": "Failed to sync models...", "addCustomProvider": "Add Custom Provider", "delete": "Delete", "stopModel": "Stop Model", "pulling": "Pulling...", "runModel": "Run Model", "dialog": {"disableModel": {"title": "Confirm Disable Model", "content": "Are you sure you want to disable model \"{name}\"?", "confirm": "Disable"}, "disableAllModels": {"title": "Confirm Disable All Models", "content": "Are you sure you want to disable all models?", "confirm": "Disable All"}, "configModels": {"title": "Configure Model List", "description": "Select models to enable or disable"}, "verify": {"missingFields": "Please enter API Key and API URL", "failed": "Verification failed", "success": "Verification successful", "failedDesc": "API key or configuration verification failed, please check your configuration", "successDesc": "API key and configuration verified successfully, ready to use", "connectionError": "Connection error, please check the network connection and API address", "serverError": "Server error, please try again later", "unauthorized": "Authentication failed, API Key is invalid or expired"}, "addCustomProvider": {"title": "Add Custom Provider", "description": "Please fill in the necessary information for the provider", "name": "Name", "namePlaceholder": "Please enter the provider name", "apiType": "API Type", "apiTypePlaceholder": "Please select the API type", "apiKey": "API Key", "apiKeyPlaceholder": "Please enter the API key", "baseUrl": "API Base URL", "baseUrlPlaceholder": "Please enter the API base URL", "enable": "Enable Provider"}, "deleteProvider": {"title": "Confirm Delete Provider", "content": "Are you sure you want to delete provider \"{name}\"? This action cannot be undone.", "confirm": "Delete"}, "deleteModel": {"title": "Confirm Delete Model", "content": "Are you sure you want to delete model \"{name}\"? This action cannot be undone.", "confirm": "Delete"}, "pullModel": {"title": "Pull Model", "description": "Select models to download locally", "pull": "<PERSON><PERSON>"}, "modelCheck": {"title": "Model Check", "description": "Select a model to test connectivity and availability", "model": "Select Model", "modelPlaceholder": "Please select a model to test", "test": "Start Test", "checking": "Testing...", "success": "Model test successful", "failed": "Model test failed", "noModels": "No available models for this provider"}}, "pullModels": "Pull Models", "refreshModels": "Refresh Models", "modelsRunning": "Running Models", "runningModels": "Running Models", "noRunningModels": "No Running Models", "deleteModel": "Delete Model", "deleteModelConfirm": "Are you sure you want to delete model \"{name}\"? This action cannot be undone.", "noLocalModels": "No Local Models", "localModels": "Local Models", "azureApiVersion": "API Version", "safety": {"title": "Safety settings", "blockHighest": "Block high risk", "blockMost": "Block medium risks", "blockNone": "Not blocked", "blockSome": "Block low risk"}, "serverList": "Server List", "totalServers": "Total Servers", "addServer": "Add Server", "autoStart": "Auto Start", "githubCopilotAuth": "GitHub Co<PERSON><PERSON>", "githubCopilotConnected": "GitHub Copilot Connected", "githubCopilotNotConnected": "GitHub Copilot Not Connected", "loginWithGitHub": "Login with GitHub", "loggingIn": "Logging in...", "githubCopilotLoginTip": "Authorize DeepChat to access your GitHub Copilot subscription. 'read:user' and 'read:org' permissions are required to access the Copilot API.", "loginSuccess": "Login successful", "loginFailed": "<PERSON><PERSON> failed", "tokenValid": "Token is valid", "tokenInvalid": "<PERSON><PERSON> is invalid", "disconnect": "Disconnect", "disconnected": "Disconnected successfully", "disconnectFailed": "Disconnect failed", "keyStatus": {"remaining": "<PERSON><PERSON><PERSON>", "usage": "Used"}, "refreshingModels": "Refreshing...", "toast": {"modelRunning": "The model is running", "modelRunningDesc": "Please stop the model {model} first and then delete it."}, "modelscope": {"mcpSync": {"title": "Sync MCP Services", "description": "Sync MCP servers from ModelScope to local configuration, allowing quick addition of common MCP tools.", "sync": "Start Sync", "syncing": "Syncing...", "pageSize": "<PERSON>", "imported": "Imported {count} services", "skipped": "Skipped {count} services", "errors": "{count} errors", "errorDetails": "<PERSON><PERSON><PERSON>", "noApiKey": "Please configure ModelScope API Key first", "noServersFound": "No available MCP services found", "authenticationFailed": "Authentication failed, please check API Key", "convertingServers": "Converting server configuration...", "fetchingServers": "Getting the list of MCP servers...", "importingServers": "Importing server configuration...", "noOperationalUrls": "No available operating address found", "pageNumber": "page number", "pageNumberPlaceholder": "Please enter the page number", "serverAlreadyExists": "The server already exists, skip import", "syncComplete": "Synchronous completion", "invalidServerData": "Invalid server data"}, "apiKey": "API Key", "apiKeyHelper": "Get your API Key in the ModelScope console", "apiKeyPlaceholder": "Please enter ModelScope API Key", "baseUrl": "API Address", "baseUrlHelper": "ModelScope API service address", "connected": "Connected", "connecting": "Connecting...", "description": "ModelScope is a model-as-a-service sharing platform launched by Alibaba Damo Academy", "details": {"apiConfig": "API Configuration", "mcpSync": "MCP Synchronization", "modelManagement": "Model Management", "operationalDescription": "Synchronize MCP servers that can be used directly on the ModelScope platform", "operationalServers": "Operating a server", "rateLimitConfig": "Rate limit configuration", "safetySettings": "Security settings", "specialConfig": "Special configuration", "syncFromModelScope": "Sync from ModelScope", "title": "Provider settings details"}, "invalidKey": "Invalid API Key", "keyRequired": "Please enter API Key", "name": "ModelScope", "networkError": "Network connection error", "notConnected": "Not connected", "verifyFailed": "Verification failed", "verifySuccess": "Verification is successful"}, "anthropicApiKeyTip": "Please go to Anthropic Console to get your API Key", "anthropicConnected": "Anthropic connected", "anthropicNotConnected": "Anthropic not connected", "anthropicOAuthTip": "Click to authorize DeepChat to access your Anthropic account", "oauthLogin": "<PERSON><PERSON><PERSON>", "authMethod": "Authentication Method", "authMethodPlaceholder": "Select authentication method", "apiKeyLabel": "API Key", "apiUrlLabel": "API URL", "anthropicOAuthFlowTip": "The system will automatically open the authorization window. Please come back and enter the authorization code after authorization.", "anthropicBrowserOpened": "External browser is open", "anthropicCodeInstruction": "Please complete the authorization in an external browser and paste the obtained authorization code into the input box below", "browserOpenedSuccess": "The external browser is open, please complete the authorization", "codeRequired": "Please enter the authorization code", "inputOAuthCode": "Enter the authorization code", "codeExchangeFailed": "Authorization code exchange failed", "invalidCode": "Invalid authorization code", "oauthCodeHint": "Please paste the authorization code here after completing the authorization in an external browser", "oauthCodePlaceholder": "Please enter the authorization code...", "verifyConnection": "Verify Connection", "manageModels": "Manage Models", "anthropicOAuthActiveTip": "OAuth authentication is enabled, you can use Anthropic services directly", "oauthVerifySuccess": "OAuth connection verified successfully", "oauthVerifyFailed": "OAuth connection verification failed", "configurationSaved": "Configuration saved", "configurationUpdated": "Configuration updated", "dataRefreshed": "Data has been refreshed", "operationFailed": "Operation failed", "operationSuccess": "Operation is successful", "settingsApplied": "Settings applied", "bedrockLimitTip": "* Only Anthropic Claude supported (including Opus, Sonnet, Haiku models)", "bedrockVerifyTip": "DeepChat uses Claude 3.5 Sonnet for verification. If you do not have permission to invoke, the verification will fail. This will not affect the use of other models."}, "knowledgeBase": {"title": "Knowledge Base Settings", "addKnowledgeBase": "Add Knowledge Base", "selectKnowledgeBaseType": "Please select the type of knowledge base to add", "difyDescription": "Dify knowledge base helps you manage and use document data", "comingSoon": "Coming Soon", "featureNotAvailable": "This feature is not yet available", "addDifyConfig": "Add Dify Configuration", "apiKey": "API Key", "datasetId": "Dataset ID", "endpoint": "API Endpoint", "configAdded": "Configuration Added", "configAddedDesc": "{name} configuration has been successfully added", "addConfig": "Add Configuration", "moreComingSoon": "More knowledge base types coming soon", "configUpdated": "Configuration Updated", "configUpdatedDesc": "{name} configuration has been successfully updated", "descriptionPlaceholder": "Example: Company product documentation knowledge base", "ragflowTitle": "RAGFlow Knowledge Base", "ragflowDescription": "RAGFlow is a powerful knowledge base management system that supports multiple retrieval methods and document management features.", "addRagflowConfig": "Add RAGFlow Configuration", "editRagflowConfig": "Edit RAGFlow Configuration", "dify": "Dify Knowledge Base", "editDifyConfig": "Modify the Dify configuration", "fastgptTitle": "FastGPT Knowledge Base", "fastgptDescription": "FastGPT is a powerful knowledge base management system that supports multiple retrieval methods and document management features.", "addFastGptConfig": "Add FastGPT Configuration", "editFastGptConfig": "Edit FastGPT Configuration", "builtInKnowledgeDescription": "The built-in knowledge base provides some simple implementations that enable some basic functions in an offline environment.", "builtInKnowledgeTitle": "Built-in knowledge base", "addBuiltinKnowledgeConfig": "Add built-in knowledge base configuration", "editBuiltinKnowledgeConfig": "Edit the built-in knowledge base configuration", "chunkSize": "Block size", "chunkSizeHelper": "Cut the document into segments, the size of each segment cannot exceed the model context limit", "chunkOverlap": "Overlapping size", "chunkOverlapHelper": "The amount of content repeated between adjacent text blocks ensures that there is still a contextual connection between segmented text blocks, improving the overall effect of the model processing of long texts", "selectEmbeddingModel": "Select an embed model", "modelNotFound": "Service Provider {provider} or model {model} not found", "modelNotFoundDesc": "Make sure the model is configured correctly and that the model is enabled. \nYou can check the model configuration in the service provider settings.", "removeBuiltinKnowledgeConfirmDesc": "Deleting the built-in knowledge base configuration will delete all relevant data and cannot be restored. Please be cautious.", "removeBuiltinKnowledgeConfirmTitle": "Confirm to delete the built-in knowledge base {name}?", "descriptionDesc": "Description of the knowledge base so that the AI ​​decides whether to retrieve this knowledge base", "advanced": "Advanced Options", "autoDetectDimensions": "Automatically detect embedded dimensions", "autoDetectHelper": "Automatically detect embedded dimensions, consumes a small amount of Tokens", "chunkOverlapPlaceholder": "Default value, no modification is recommended", "chunkSizePlaceholder": "Default value, no modification is recommended", "dimensions": "Embed dimensions", "dimensionsPlaceholder": "Embed dimension size, such as 1024", "selectEmbeddingModelHelper": "Embedding models are prohibited after creation of knowledge base", "dimensionsHelper": "Make sure the model supports the set embed dimension size", "autoDetectDimensionsError": "Automatically detect embedded dimension failure", "fragmentsNumber": "Number of requested document fragments", "fragmentsNumberHelper": "The more fragments of requested document, the more information it comes with, but the more tokens it needs to be consumed", "selectRerankModel": "Select the rerank model", "rerankModel": "Reorder the model", "embeddingModel": "Embed Model", "return": "return", "uploadHelper": "Click to upload or drag the file here", "fileSupport": "Support {accept} and {count} other formats", "searchKnowledge": "Search the Knowledge Base", "searchKnowledgePlaceholder": "Please enter the query content", "noData": "No data yet", "file": "document", "uploadProcessing": "Uploading", "uploadCompleted": "Upload completed", "reAdd": "Re-upload", "uploadError": "Upload failed", "delete": "delete", "reason": "reason", "deleteSuccess": "Delete successfully", "copy": "copy", "copySuccess": "Copy successfully", "source": "source", "normalized": "L2 Normalization", "normalizedHelper": "Please confirm that the model supports L2 normalization of output vectors", "dialog": {"beforequit": {"cancel": "Cancel", "confirm": "confirm", "title": "Exit confirmation", "description": "There is a running knowledge base task. Are you sure to exit the software? \nAborted tasks can be restored after restarting the software."}}, "searchError": "Query failed", "processing": "Uploading", "paused": "Upload pause", "unknown": "Unknown status", "reAddFile": {"title": "Re-upload confirmation", "content": "Are you sure to re-upload the file \"{fileName}\"?"}, "deleteFile": {"title": "Delete file confirmation", "content": "Are you sure to delete the file \"{fileName}\"? \nThis operation is not restored."}, "resumeAllPausedTasks": "One-click recovery", "pauseAllRunningTasks": "One-click pause", "separators": "Block separator", "separatorsHelper": "Document segmentation delimiter, a single delimiter is enclosed in double quotes (\"\"), and delimiters are separated by commas (,)", "invalidSeparators": "Invalid separator", "selectLanguage": "Select a preset", "separatorsPreset": "Loading presets", "promptManagement": {"title": "Prompt Management", "description": "Manage and use custom prompt templates to better interact with AI"}}, "mcp": {"title": "MCP Settings", "description": "Manage and configure MCP (Model Context Protocol) servers and tools", "enabledTitle": "Enable MCP", "enabledDescription": "Enable or disable MCP functionality and tools", "enableToAccess": "Please enable MCP to access configuration options", "marketplace": "Go to the MCP Market for one-click installation", "technicalDetails": "Technical Details", "httpServer": "HTTP Server", "localProcess": "Local Process", "restartServer": "Restart Server", "viewLogs": "View Logs", "starting": "Starting", "error": "Error", "tabs": {"servers": "Servers", "tools": "Tools", "prompts": "Prompts", "resources": "Resources"}, "serverList": "Server List", "totalServers": "Total Servers", "addServer": "Add Server", "running": "Running", "stopped": "Stopped", "stopServer": "Stop Server", "startServer": "Start Server", "noServersFound": "No Servers Found", "addServerDialog": {"title": "Add Server", "description": "Configure a new MCP server"}, "editServerDialog": {"title": "Edit Server", "description": "Edit MCP server configuration"}, "serverForm": {"name": "Server Name", "namePlaceholder": "Enter server name", "nameRequired": "Server name is required", "type": "Server Type", "typePlaceholder": "Select server type", "typeStdio": "Standard input and output (Stdio)", "typeSse": "Server-Sent Events (SSE)", "typeInMemory": "In-Memory", "typeHttp": "Streaming HTTP Requests (HTTP)", "baseUrl": "Base URL", "baseUrlPlaceholder": "Enter server base URL (e.g. http://localhost:3000)", "command": "Command", "commandPlaceholder": "Enter command", "commandRequired": "Command is required", "args": "Arguments", "argsPlaceholder": "Enter arguments separated by spaces", "argsRequired": "Arguments are required", "env": "Environment Variables", "envPlaceholder": "Enter environment variables in JSON format", "envInvalid": "Environment variables must be valid JSON", "description": "Description", "descriptionPlaceholder": "Enter server description", "descriptions": "Description", "descriptionsPlaceholder": "Enter server description", "icon": "Icon", "iconPlaceholder": "Enter icon", "icons": "Icon", "iconsPlaceholder": "Enter icon", "autoApprove": "Auto Approve", "autoApproveAll": "All", "autoApproveRead": "Read", "autoApproveWrite": "Write", "autoApproveHelp": "Select operation types to auto-approve without user confirmation", "submit": "Submit", "add": "Add", "update": "Update", "cancel": "Cancel", "jsonConfigIntro": "You can paste a JSON configuration directly or choose to configure the server manually.", "jsonConfig": "JSON Configuration", "jsonConfigPlaceholder": "Paste your MCP server configuration in JSON format", "jsonConfigExample": "JSON Configuration Example", "parseSuccess": "Configuration Parsed", "configImported": "Configuration imported successfully", "parseError": "<PERSON><PERSON>", "skipToManual": "Skip to Manual Configuration", "parseAndContinue": "Parse & Continue", "jsonParseError": "JSON parsing failed", "browseMarketplace": "Browse MCP Marketplace", "imageModel": "Choose a vision model", "customHeadersParseError": "Custom Header parsing failed", "customHeaders": "Custom Request Headers", "clickToEdit": "Click to edit and view full content", "invalidKeyValueFormat": "Incorrect request header format, please check whether the input is correct.", "npmRegistry": "Custom NPM Registry", "npmRegistryPlaceholder": "Set up a custom NPM Registry, leave the system to automatically select the fastest one", "browseHigress": "View Higress MCP Marketplace", "selectFolderError": "Folder selection error", "folders": "Allowed Folders", "addFolder": "Add Folder", "noFoldersSelected": "No folders were selected", "useE2B": "Enable E2B Sandbox", "e2bDescription": "Execute Python code using E2B sandbox", "e2bApiKey": "E2B ApiKey", "e2bApiKeyPlaceholder": "Enter your E2B Api Keys here, such as e2b_1111xx*******", "e2bApiKeyHelp": "Go to e2b.dev to get your ApiKey", "e2bApiKeyRequired": "ApiKey must be entered to enable E2B function"}, "deleteServer": "Delete Server", "editServer": "Edit Server", "setDefault": "Set as <PERSON><PERSON><PERSON>", "removeDefault": "<PERSON><PERSON><PERSON>", "isDefault": "Default Server", "default": "<PERSON><PERSON><PERSON>", "setAsDefault": "Set as <PERSON><PERSON><PERSON>", "removeServer": "Remove Server", "autoStart": "Auto Start", "confirmRemoveServer": "Are you sure you want to delete server {name}? This action cannot be undone.", "removeServerDialog": {"title": "Delete Server"}, "confirmDelete": {"title": "Confirm Delete", "description": "Are you sure you want to delete server {name}? This action cannot be undone.", "confirm": "Delete", "cancel": "Cancel"}, "resetToDefault": "Reset to De<PERSON>ult", "resetConfirmTitle": "Reset to Default Servers", "resetConfirmDescription": "This will restore all default servers while keeping your custom servers. Any modifications to default servers will be lost.", "resetConfirm": "Reset", "builtInServers": "Built-in Servers", "customServers": "Custom Servers", "builtIn": "Built-in", "cannotRemoveBuiltIn": "Cannot Remove Built-in Server", "builtInServerCannotBeRemoved": "Built-in servers cannot be removed, only parameters and environment variables can be modified", "maxDefaultServersReached": "Only 3 default servers can be set at most", "removeDefaultFirst": "Please remove some default servers first", "higressMarket": "Go to Higress MCP Installation", "npmRegistry": {"title": "NPM Registry Configuration", "currentSource": "Current Source", "cached": "<PERSON><PERSON><PERSON>", "lastChecked": "Last Checked", "refresh": "Refresh", "advanced": "Advanced", "advancedSettings": "Advanced Settings", "advancedSettingsDesc": "Configure advanced NPM registry options, including auto-detection and custom source settings", "autoDetect": "Auto-detect optimal source", "autoDetectDesc": "Automatically detect and use the fastest NPM registry on startup", "customSource": "Custom Source", "customSourcePlaceholder": "Enter custom NPM registry URL", "currentCustom": "Current custom source", "justNow": "Just now", "minutesAgo": "{minutes} minutes ago", "hoursAgo": "{hours} hours ago", "daysAgo": "{days} days ago", "refreshSuccess": "NPM registry refreshed successfully", "refreshSuccessDesc": "Re-detected and updated optimal NPM registry", "refreshFailed": "NPM registry refresh failed", "autoDetectUpdated": "Auto-detect setting updated", "autoDetectEnabled": "Auto-detect optimal NPM registry enabled", "autoDetectDisabled": "Auto-detect disabled, will use default registry", "updateFailed": "Setting update failed", "customSourceSet": "Custom source set", "customSourceSetDesc": "Custom NPM registry set: {registry}", "customSourceCleared": "Custom source cleared", "customSourceClearedDesc": "Custom NPM registry cleared, will use auto-detect", "invalidUrl": "Invalid URL", "invalidUrlDesc": "Please enter a valid HTTP or HTTPS address", "testing": "Testing NPM registry", "testingDesc": "Testing connectivity to {registry}...", "testFailed": "NPM registry test failed", "testFailedDesc": "Cannot connect to {registry}, error: {error}. Please check if the URL is correct or network connection.", "redetectingOptimal": "Re-detecting optimal NPM registry...", "redetectComplete": "Re-detection completed", "redetectCompleteDesc": "Detected and set the current optimal NPM registry", "redetectFailed": "Re-detection failed", "redetectFailedDesc": "Unable to re-detect optimal registry, will use default configuration"}}, "about": {"title": "About Us", "version": "Version", "checkUpdate": "Check Update", "checking": "Checking...", "latestVersion": "Latest Version"}, "display": {"fontSize": "Text size", "fontSizeDesc": "Adjust the text size of the application interface, including chat content, menus, and settings interface fonts", "text-2xl": "Extremely large", "text-base": "<PERSON><PERSON><PERSON>", "text-lg": "Big", "text-sm": "Small", "text-xl": "Large", "floatingButton": "Floating <PERSON><PERSON>", "floatingButtonDesc": "Display a floating button on the desktop to quickly activate the application window"}, "shortcuts": {"title": "Shortcut Key Settings", "pressKeys": "Press keys", "pressEnterToSave": "Press Enter to save, Esc to cancel", "noModifierOnly": "Cannot use modifier key alone as shortcut", "keyConflict": "Shortcut key conflict, please choose another combination", "clearShortcut": "Clear shortcut", "cleanHistory": "Clear chat history", "deleteConversation": "Delete Conversation", "goSettings": "Open Settings", "hideWindow": "Hide window", "quitApp": "Quit app", "zoomIn": "Zoom in ", "zoomOut": "Zoom out ", "zoomReset": "Reset zoom", "closeTab": "Close the current tab page", "newTab": "Create a new tab", "newWindow": "Open a new window", "showHideWindow": "Show/Hide window", "newConversation": "New conversation", "nextTab": "Switch to next tab", "previousTab": "Switch to previous tab", "specificTab": "Switch to specific tab (1-8)", "lastTab": "Switch to last tab"}, "rateLimit": {"title": "Rate Limit", "description": "Control request interval to prevent exceeding API limits", "intervalLimit": "Request Interval", "intervalUnit": "seconds", "intervalHelper": "Minimum interval between requests, disable rate limiting if not needed", "lastRequestTime": "Last Request", "queueLength": "Queue Length", "nextAllowedTime": "Next Allowed", "never": "Never", "justNow": "Just now", "secondsAgo": "s ago", "minutesAgo": "m ago", "immediately": "Now", "secondsLater": "s later", "confirmDisableTitle": "Confirm Disable Rate Limit", "confirmDisableMessage": "Value cannot be less than or equal to 0. Do you want to disable rate limiting?", "confirmDisable": "Disable Limit", "disabled": "Rate Limit Disabled", "disabledDescription": "Rate limiting has been disabled"}}