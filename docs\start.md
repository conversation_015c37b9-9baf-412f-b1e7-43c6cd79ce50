# DeepChat Documentation

This directory contains design documents and usage guides for DeepChat.

## User Documentation

- [User Guide](./user-guide.md) - How to install, configure, and use DeepChat.

## Developer Documentation

- [Developer Guide](./developer-guide.md) - Information for developers contributing to or building with DeepChat.

## Design Documents

- [Event System Design](./event-system-design.md) - Design and implementation of the application event system
- [Data Sync Feature](./data-sync-feature.md) - Design and implementation of the data sync feature
- [LLM Provider Interface Design](./llm-provider-interface.md) - Design of the LLM Provider interface

## Build Guides

- [Linux Build Guide](./linux-build-guide.md) - Steps to build DeepChat on Linux systems
