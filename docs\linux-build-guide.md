# Deepchat Linux 构建指南

## 功能概述

本文档描述了如何使用Docker在低版本glibc环境下构建Deepchat应用程序。这种构建方式主要用于确保生成的二进制文件能在大多数Linux系统上运行。

## 技术实现

### 构建环境

- 基础镜像：node:22-slim（基于Debian，提供稳定的构建环境）
- Node.js版本：22.x
- 构建工具：npm（配置使用淘宝镜像源）

## 使用说明

### 构建步骤

1. 确保Docker已经安装并运行
2. 执行以下命令进行构建：

```bash
# 构建Docker镜像
docker build -t deepchat-builder -f Dockerfile.build.linux .

# 运行构建
docker run --rm -v $(pwd):/app/dist deepchat-builder
```

### 构建产物

构建完成后，在项目的`dist`目录下会生成以下文件：
- Linux可执行文件
- 相关依赖和资源文件
- AppImage或deb包（取决于构建配置）

### 注意事项

1. 构建过程可能需要较长时间，请耐心等待
2. 确保有足够的磁盘空间（建议至少10GB可用空间）
3. npm已配置使用淘宝镜像源，可以显著提高依赖包的下载速度
4. 如果需要更改npm镜像源，可以修改Dockerfile中的相关配置
5. 构建产物会继承基础镜像的glibc版本，确保良好的兼容性

## 常见问题

1. **构建失败**
   - 检查网络连接
   - 确认磁盘空间充足
   - 查看Docker日志获取详细错误信息

2. **依赖问题**
   - 可能需要手动安装一些系统级依赖
   - 确保node-gyp正确安装
   - 如果遇到网络问题，检查镜像源配置

3. **权限问题**
   - 确保Docker有足够的权限访问项目目录
   - 检查生成文件的所有权

## 技术支持

如果在构建过程中遇到问题，请：
1. 查看构建日志
2. 检查系统要求
3. 在项目Issue中搜索相关问题
4. 如果问题仍然存在，请创建新的Issue并提供详细的错误信息 