name: PR Check

on:
  pull_request:
    branches:
      - main
      - dev

jobs:
  build-check:
    runs-on: ubuntu-22.04
    strategy:
      matrix:
        arch: [x64]
        include:
          - arch: x64
    steps:
      - uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22.13.1'

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: 10.12.1

      - name: Install dependencies
        run: pnpm install
    
      - name: lint
        run: pnpm run lint
      
      - name: format:check
        run: pnpm run format:check

      - name: Configure pnpm workspace for Linux ${{ matrix.arch }}
        run: pnpm run install:sharp
        env:
          TARGET_OS: linux
          TARGET_ARCH: ${{ matrix.arch }}

      - name: Install dependencies
        run: pnpm install

      - name: Check Lint
        run: pnpm run lint

      - name: Check translations
        run: pnpm run i18n

      - name: Build
        run: pnpm run build