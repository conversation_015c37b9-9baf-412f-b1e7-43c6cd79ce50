---
description: Best practices for state management with Pinia
globs: src/renderer/src/stores/**/*.{vue,ts,tsx,js,jsx}
alwaysApply: false
---
- Use modules to organize related state and actions
- Implement proper state persistence for maintaining data across sessions
- Use getters for computed state properties
- Utilize actions for side effects and asynchronous operations
- Keep the store focused on global state, not component-specific data