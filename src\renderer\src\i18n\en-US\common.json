{"enabled": "Enabled", "disabled": "Disabled", "loading": "Loading...", "copySuccess": "Copy Success", "copySuccessDesc": "Content copied to clipboard", "copyImageSuccess": "Copy Success", "copyImageSuccessDesc": "Image copied to clipboard", "copyFailed": "<PERSON><PERSON> Failed", "copyFailedDesc": "Failed to copy content to clipboard", "copyCode": "Copy Code", "copy": "Copy", "paste": "Paste", "export": "Export", "newChat": "New Chat", "newTopic": "New Topic", "cancel": "Cancel", "confirm": "Confirm", "close": "Close", "error": {"requestFailed": "Request failed...", "createChatFailed": "Failed to create chat", "selectChatFailed": "Failed to select chat", "renameChatFailed": "Failed to rename chat", "deleteChatFailed": "Failed to delete chat", "cleanMessagesFailed": "Failed to clean messages", "userCanceledGeneration": "User canceled generation", "sessionInterrupted": "Session was unexpectedly interrupted, generation is incomplete", "noModelResponse": "Model did not return any content, it may have timed out", "invalidJson": "Invalid JSON format", "maximumToolCallsReached": "Maximum Tool Calls Reached", "causeOfError": "Possible causes of error:", "error400": "Bad request, possibly a parameter or compatibility issue", "error401": "Authentication failed, possibly incorrect API Key or domain", "error403": "Access to the model forbidden, possibly insufficient balance or no permission", "error404": "Requested URL not found, possibly incorrect domain or model name", "error429": "Too many requests, possibly rate limited by the service", "error500": "Server error, the service might be unstable, please try again later", "error502": "Gateway error, the service might be unstable, please try again later", "error503": "Service unavailable, the service might be unstable, please try again later", "error504": "Request timeout, the service might be unstable or there might be network issues, please check proxy settings and try again", "operationFailed": "Operation failed"}, "resetDataConfirmTitle": "Reset All Data?", "resetDataConfirmDescription": "This will reset all your data to default settings. This action cannot be undone.", "proxyMode": "Proxy Mode", "proxyModeSelect": "Select Proxy Mode", "proxyModeSystem": "System Proxy", "proxyModeNone": "No Proxy", "proxyModeCustom": "Custom Proxy", "customProxyUrl": "Custom Proxy URL", "customProxyUrlPlaceholder": "Example: http://127.0.0.1:7890", "invalidProxyUrl": "Invalid proxy URL, please enter a valid http/https URL", "disclaimer": "Disclaimer", "resetData": "Reset data", "searchAssistantModel": "Search Model", "visionModel": "Vision Model", "visionModelDesc": "Select a model for processing images and visual content, used for image description, analysis and other features", "searchEngine": "Search Engine", "searchEngineSelect": "Select a search engine", "searchPreview": "Search preview", "language": "language", "languageSelect": "Select a language", "selectModel": "Select a model", "title": "General settings", "languageSystem": "Follow the system", "watermarkTip": "Generated by AI", "collapse": "Collapse", "expand": "Expand", "image": "picture", "add": "Add", "reset": "Reset", "format": "Format", "edit": "Edit", "delete": "Delete", "save": "Save", "saveSuccess": "Save Success", "saveFailed": "Save Failed", "clear": "Clear", "saved": "Saved", "closeToQuit": "Exit app when closing window"}