{"search": {"placeholder": "Search models..."}, "error": {"loadFailed": "Failed to load models"}, "type": {"custom": "Custom Model", "official": "Official Model"}, "add": {"namePlaceholder": "Model Name", "idPlaceholder": "Model ID", "contextLengthPlaceholder": "Context Length", "maxTokensPlaceholder": "<PERSON>"}, "actions": {"add": "Add Model", "enableAll": "Enable All", "disableAll": "Disable All"}, "tags": {"reasoning": "Reasoning", "chat": "Cha<PERSON>", "code": "Code", "writing": "Writing", "analysis": "Analysis"}, "categories": {"all": "All", "chat": "Cha<PERSON>", "reasoning": "Reasoning", "vision": "Vision", "network": "Network", "free": "Free", "rerank": "<PERSON><PERSON>", "tool": "Tool", "embedding": "Embedding", "imageGeneration": "Image Generation"}}