{"title": "MCP 설정", "description": "MCP(Model Context Protocol) 서버 및 도구 관리 및 구성", "capability": {"title": "MCP 기능", "description": "MCP(Model Context Protocol) 서버, 도구, 설정의 관리 및 구성"}, "enabledTitle": "MCP 활성화", "enabledDescription": "MCP 기능 및 도구 활성화 또는 비활성화", "enableToAccess": "구성 옵션에 액세스하려면 먼저 MCP를 활성화하세요", "tabs": {"servers": "서버", "tools": "도구"}, "serverList": "서버 목록", "addServer": "서버 추가", "running": "실행 중", "stopped": "중지됨", "stopServer": "서버 중지", "startServer": "서버 시작", "noServersFound": "서버 없음", "addServerDialog": {"title": "서버 추가", "description": "새 MCP 서버 구성"}, "editServerDialog": {"title": "서버 편집", "description": "MCP 서버 구성 편집"}, "serverForm": {"name": "서버 이름", "namePlaceholder": "서버 이름 입력", "nameRequired": "서버 이름이 필요합니다", "type": "서버 유형", "typePlaceholder": "서버 유형 선택", "typeStdio": "표준 입출력", "typeSse": "서버 전송 이벤트", "typeInMemory": "인메모리", "baseUrl": "기본 URL", "baseUrlPlaceholder": "서버 기본 URL 입력(예: http://localhost:3000)", "command": "명령어", "commandPlaceholder": "명령어 입력", "commandRequired": "명령어는 비어 있을 수 없습니다", "args": "매개변수", "argsPlaceholder": "매개변수 입력, 공백으로 구분", "argsRequired": "매개변수는 비어 있을 수 없습니다", "env": "환경 변수", "envPlaceholder": "JSON 형식의 환경 변수 입력", "envInvalid": "환경 변수는 유효한 JSON 형식이어야 합니다", "description": "설명", "descriptionPlaceholder": "서버 설명 입력", "descriptions": "설명", "descriptionsPlaceholder": "서버 설명 입력", "icon": "아이콘", "iconPlaceholder": "아이콘 입력", "icons": "아이콘", "iconsPlaceholder": "아이콘 입력", "autoApprove": "자동 승인", "autoApproveAll": "전체", "autoApproveRead": "읽기", "autoApproveWrite": "쓰기", "autoApproveHelp": "자동 승인할 작업 유형을 선택하세요. 사용자 확인 없이 실행할 수 있습니다", "submit": "제출", "add": "추가", "update": "업데이트", "cancel": "취소", "jsonConfigIntro": "JSON 구성을 직접 붙여넣거나 서버를 수동으로 구성하는 것 중에서 선택할 수 있습니다.", "jsonConfig": "JSON 구성", "jsonConfigPlaceholder": "MCP 서버의 JSON 형식 구성을 붙여넣으세요", "jsonConfigExample": "JSON 구성 예시", "parseSuccess": "구성 파싱 성공", "configImported": "구성을 성공적으로 가져왔습니다", "parseError": "파싱 오류", "skipToManual": "수동 구성으로 건너뛰기", "parseAndContinue": "파싱 및 계속", "addFolder": "폴더를 추가하십시오", "folders": "폴더 목록", "noFoldersSelected": "폴더가 선택되지 않았습니다", "selectFolder": "폴더를 선택하십시오", "selectFolderError": "폴더를 선택하지 못했습니다"}, "deleteServer": "서버 삭제", "editServer": "서버 편집", "setDefault": "기본 설정", "removeDefault": "기본 설정 제거", "isDefault": "기본 서버", "default": "기본", "setAsDefault": "기본 설정", "removeServer": "서버 삭제", "confirmRemoveServer": "서버 {name}을(를) 삭제하시겠습니까? 이 작업은 취소할 수 없습니다.", "removeServerDialog": {"title": "서버 삭제"}, "confirmDelete": {"title": "삭제 확인", "description": "서버 {name}을(를) 삭제하시겠습니까? 이 작업은 취소할 수 없습니다.", "confirm": "삭제", "cancel": "취소"}, "resetToDefault": "기본값으로 재설정", "resetConfirmTitle": "기본 서버로 재설정", "resetConfirmDescription": "이 작업은 사용자 정의 서버를 유지하면서 모든 기본 서버를 복원합니다. 기본 서버에 대한 모든 수정 사항이 손실됩니다.", "resetConfirm": "재설정", "tools": {"searchPlaceholder": "도구 검색...", "noToolsAvailable": "사용 가능한 도구 없음", "toolList": "도구 목록", "functionDescription": "기능 설명", "path": "경로", "pathPlaceholder": "파일 경로 입력", "searchPattern": "검색 패턴", "searchPatternPlaceholder": "정규식 입력", "filePattern": "파일 패턴", "filePatternPlaceholder": "파일 패턴 입력, 예: *.md", "executeButton": "실행", "resultTitle": "실행 결과", "runningTool": "실행 중...", "loading": "로딩 중...", "error": "로딩 실패", "available": "{count}개 도구 사용 가능", "none": "사용 가능한 도구 없음", "title": "MCP 도구", "description": "MCP 서버가 제공하는 도구", "loadError": "도구 로딩 실패", "parameters": "매개변수", "refresh": "새로고침", "disabled": "MCP가 비활성화됨", "enableToUse": "도구를 사용하려면 MCP를 활성화하세요", "enabled": "MCP 활성화", "enabledDescription": "도구 호출을 사용하도록 MCP 기능 활성화", "empty": "비어있음", "invalidJsonFormat": "유효하지 않은 JSON 형식", "jsonInputPlaceholder": "JSON 형식의 매개 변수를 입력하십시오", "input": "매개 변수를 입력하십시오", "type": "유형", "annotations": "주석", "selectToolToDebug": "디버그할 도구 선택", "dialogDescription": "MCP 서버에서 제공하는 도구 디버그 및 테스트", "toolsCount": "도구", "availableTools": "사용 가능한 도구", "invalidJson": "잘못된 JSON 형식", "inputHint": "JSON 형식으로 매개변수를 입력하세요", "required": "필수", "noDescription": "설명 없음"}, "inmemory": {"Artifacts": {"desc": "DeepChat에서 다양한 Artifacts 콘텐츠를 출력할 수 있게 해줍니다", "name": "Artifacts"}, "bochaSearch": {"desc": "Bocha 검색 API https://open.bochaai.com/", "name": "Bocha Search"}, "buildInFileSystem": {"desc": "DeepChat이 로컬 파일을 조작할 수 있도록 해줍니다", "name": "파일 시스템"}, "imageServer": {"desc": "활성화 후 DeepChat의 모든 모델이 이미지를 이해하고 생성할 수 있습니다", "name": "이미지 서비스"}, "braveSearch": {"desc": "Brave 검색 API https://brave.com/search/api/", "name": "Brave 검색"}, "powerpack": {"desc": "모든 대형 모델에 시간 조회, 웹 정보 수집, 안전한 코드 실행 등의 향상된 기능을 제공하여 더 강력하고 정확한 정보 수집 능력을 제공합니다", "name": "파워팩 도구"}, "difyKnowledge": {"name": "Dify 지식 기반 검색", "desc": "Dify 지식 기반 검색 서비스, Dify 지식 기반의 콘텐츠를 검색할 수 있습니다."}, "ragflowKnowledge": {"name": "RAGFlow 지식 베이스 검색", "desc": "RAGFlow 지식 베이스 검색 서비스, RAGFlow 지식 베이스 콘텐츠를 검색할 수 있습니다"}, "fastGptKnowledge": {"name": "FastGPT 지식 베이스 검색", "desc": "FastGPT 지식 베이스 검색 서비스, FastGPT 지식 베이스 콘텐츠를 검색할 수 있습니다"}, "deepchat-inmemory/custom-prompts-server": {"desc": "DeepChat 내장 사용자 정의 프롬프트 단어 서비스", "name": "사용자 정의 프롬프트 단어"}, "deepchat-inmemory/deep-research-server": {"desc": "Bocha 검색을 기반으로 한 DeepChat 내장 내장 연구 서비스 (긴 컨텍스트 모델을 사용해야하고 컨텍스트가 불충분 한 모델이 실패 할 수 있습니다)", "name": "DeepResearch"}, "deepchat-inmemory/auto-prompting-server": {"name": "자동 템플릿 프롬프트", "desc": "사용자 입력을 기반으로 가장 적합한 사용자 정의 프롬프트를 자동으로 선택하고, 프롬프트 템플릿을 지능적으로 채웁니다."}, "deepchat-inmemory/conversation-search-server": {"name": "대화 기록 검색", "desc": "DeepChat 내장 대화 기록 검색 서비스, 과거 대화 기록과 메시지 내용을 검색할 수 있습니다"}, "deepchat-inmemory/meeting-server": {"name": "멀티 에이전트 회의", "desc": "DeepChat의 내장 회의 서비스는 다중 에이전트 토론의 주최와 진행을 지원합니다."}, "builtinKnowledge": {"desc": "Deepchat 내장 지식 기반 검색 서비스, Deepchat 내장 지식 기반의 내용을 검색 할 수있는 Deepchat 내장 지식 기반 검색 서비스", "name": "내장 된 지식 기반 검색"}, "deepchat/apple-server": {"desc": "모델이 캘린더, 연락처, 이메일,지도, 메모, 알림 및 기타 시스템과 같은 MacOS의 시스템 기능을 작동하도록합니다.", "name": "MACOS 시스템 어시스턴트"}}, "prompts": {"noPromptsAvailable": "사용 가능한 프롬프트가 없습니다", "noDescription": "아직 설명이 없습니다", "selectPrompt": "여기에 표시됩니다", "parameters": "프롬프트 매개 변수", "input": "입력 매개 변수", "runningPrompt": "프롬프트가 얻고 있습니다", "executeButton": "클릭하려면 클릭하십시오", "resultTitle": "신속한 세부 사항", "invalidJson": "유효하지 않은 JSON 형식", "parametersHint": "JSON 형식의 매개 변수를 입력하고 자동 형식을 지원하십시오.", "resetToDefault": "기본 매개 변수로 재설정하십시오", "dialogDescription": "MCP 서버가 제공하는 프롬프트를 디버그하고 테스트합니다"}, "resources": {"noResourcesAvailable": "자원이 없습니다", "selectResource": "여기에 리소스 콘텐츠를 표시하십시오", "loading": "로딩", "loadContent": "리소스 컨텐츠를 얻으십시오", "pleaseSelect": "디스플레이 리소스 세부 정보를 얻으려면 클릭하십시오", "dialogDescription": "MCP 서버가 제공하는 리소스를 탐색하고 봅니다"}, "errors": {"loadConfigFailed": "MCP 구성 로드 실패", "setEnabledFailed": "MCP 활성화 상태 설정 실패", "getServerStatusFailed": "서버 {serverName} 상태 가져오기 실패", "addServerFailed": "서버 추가 실패", "updateServerFailed": "서버 업데이트 실패", "removeServerFailed": "서버 제거 실패", "maxDefaultServersReached": "기본 서버 최대 수(30)에 도달했습니다", "toggleDefaultServerFailed": "기본 서버 상태 전환 실패", "resetToDefaultFailed": "기본 서버로 재설정 실패", "toggleServerFailed": "서버 {serverName} 상태 전환 실패", "loadToolsFailed": "도구 로드 실패", "loadPromptsFailed": "프롬프트 로드 실패", "loadResourcesFailed": "리소스 로드 실패", "callToolFailed": "도구 {toolName} 호출 실패", "toolCallError": "도구 호출 오류: {error}", "mcpDisabled": "MCP가 비활성화되어 있습니다", "getPromptFailed": "프롬프트 가져오기 실패", "readResourceFailed": "리소스 읽기 실패"}, "market": {"apiKeyPlaceholder": "McProuter API 키를 입력하십시오", "apiKeyRequiredDesc": "설치하기 전에 먼저 McProuter API 키를 작성하십시오.", "apiKeyRequiredTitle": "API 키가 필요합니다", "browseBuiltin": "내장 MCP 시장을 찾아보십시오", "builtinTitle": "MCP 시장", "empty": "아직 서비스가 없습니다", "install": "설치하다", "installFailed": "설치가 실패했습니다", "installSuccess": "성공적으로 설치", "installed": "설치", "keyGuide": "열쇠를 얻으십시오", "keyHelpEnd": "API 키를 신청 한 후 위의 입력 상자를 채우십시오.", "keyHelpText": "먼저 도착하십시오", "loadMore": "더로드하십시오", "noMore": "더 이상", "poweredBy": "McProuter에 의해 구동", "pullDownToLoad": "더 많은로드하기 위해 계속 드롭 다운하십시오"}}