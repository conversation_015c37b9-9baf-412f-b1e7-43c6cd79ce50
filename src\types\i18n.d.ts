import { DefineLocaleMessage } from 'vue-i18n'

declare module 'vue-i18n' {
  interface DefineLocaleMessage {
    title: string
    description: string
    website: string
    disclaimerButton: string
    disclaimerTitle: string
    checkUpdateButton: string
    deviceInfo: {
      title: string
      platform: string
      arch: string
      cpuModel: string
      totalMemory: string
      osVersion: string
    }
    clickToOpen: string
    codeSnippet: string
    function: string
    class: string
    reactComponent: string
    moduleImport: string
    variableDefinition: string
    markdownDocument: string
    htmlDocument: string
    svgImage: string
    flowchart: string
    sequenceDiagram: string
    classDiagram: string
    stateDiagram: string
    erDiagram: string
    ganttChart: string
    pieChart: string
    mermaidDiagram: string
    flowchartOf: string
    sequenceDiagramBetween: string
    classDiagramOf: string
    stateDiagramOf: string
    erDiagramOf: string
    pieChartOf: string
    unknownDocument: string
    preview: string
    code: string
    export: {
      failed: string
      failedDesc: string
    }
    htmlPreviewTitle: string
    svgPreviewTitle: string
    copy: string
    copyAsImage: string
    copyImageSuccessDesc: string
    copyImageFailedDesc: string
    copySuccess: string
    copySuccessDesc: string
    copyFailed: string
    copyFailedDesc: string
    input: {
      placeholder: string
      fileArea: string
      inputArea: string
      functionSwitch: string
      fileSelect: string
      pasteFiles: string
      dropFiles: string
      promptFilesAdded: string
      promptFilesAddedDesc: string
      promptFilesError: string
      promptFilesErrorDesc: string
      historyPlaceholder: string
    }
    features: {
      deepThinking: string
      webSearch: string
      deepThinkingProgress: string
      thinkingDuration: string
      artifactThinking: string
    }
    search: {
      results: string
      searching: string
      title: string
      description: string
      optimizing: string
      reading: string
      error: string
      placeholder: string
    }
    messages: {
      thinking: string
    }
    notify: {
      generationComplete: string
      generationError: string
    }
    loading: string
    paste: string
    copyImageSuccess: string
    copyCode: string
    newChat: string
    newTopic: string
    cancel: string
    confirm: string
    enabled: string
    disabled: string
    disclaimer: string
    close: string
    image: string
    error: string
    resetDataConfirmTitle: string
    resetDataConfirmDescription: string
    resetData: string
    language: string
    languageSelect: string
    searchEngine: string
    searchEngineSelect: string
    searchPreview: string
    searchAssistantModel: string
    selectModel: string
    proxyMode: string
    proxyModeSelect: string
    proxyModeSystem: string
    proxyModeNone: string
    proxyModeCustom: string
    customProxyUrl: string
    customProxyUrlPlaceholder: string
    invalidProxyUrl: string
    languageSystem: string
    watermarkTip: string
    collapse: string
    expand: string
    add: {
      namePlaceholder: string
      idPlaceholder: string
      contextLengthPlaceholder: string
      maxTokensPlaceholder: string
    }
    reset: string
    format: string
    edit: string
    delete: {
      title: string
      description: string
      confirm: string
    }
    emojiPicker: {
      search: string
      smileys: string
      people: string
      animals: string
      food: string
      travel: string
      activities: string
      objects: string
      symbols: string
      flags: string
    }
    messageBlockAction: {
      continue: string
      continued: string
    }
    messageBlockPermissionRequest: {
      title: string
      allow: string
      deny: string
      rememberChoice: string
      granted: string
      denied: string
      type: {
        read: string
        write: string
        all: string
      }
      description: {
        read: string
        write: string
        all: string
      }
    }
    translate: {
      title: string
      original: string
      translated: string
      error: string
    }
    askAI: {
      title: string
      question: string
      answer: string
      error: string
    }
    cut: string
    ok: string
    rename: {
      title: string
      description: string
    }
    cleanMessages: {
      title: string
      description: string
      confirm: string
    }
    fork: {
      title: string
      description: string
      confirm: string
      tag: string
    }
    enabledTitle: string
    enabledDescription: string
    enableToAccess: string
    errors: {
      loadConfigFailed: string
      setEnabledFailed: string
      getServerStatusFailed: string
      addServerFailed: string
      updateServerFailed: string
      removeServerFailed: string
      maxDefaultServersReached: string
      toggleDefaultServerFailed: string
      resetToDefaultFailed: string
      toggleServerFailed: string
      loadToolsFailed: string
      loadPromptsFailed: string
      loadResourcesFailed: string
      callToolFailed: string
      toolCallError: string
      mcpDisabled: string
      getPromptFailed: string
      readResourceFailed: string
    }
    tabs: {
      servers: string
      tools: string
    }
    serverList: string
    addServer: string
    running: string
    stopped: string
    stopServer: string
    startServer: string
    noServersFound: string
    addServerDialog: {
      title: string
      description: string
    }
    editServerDialog: {
      title: string
      description: string
    }
    serverForm: {
      name: string
      namePlaceholder: string
      nameRequired: string
      type: string
      typePlaceholder: string
      typeStdio: string
      typeSse: string
      typeInMemory: string
      baseUrl: string
      baseUrlPlaceholder: string
      command: string
      commandPlaceholder: string
      commandRequired: string
      args: string
      argsPlaceholder: string
      argsRequired: string
      env: string
      envPlaceholder: string
      envInvalid: string
      description: string
      descriptionPlaceholder: string
      descriptions: string
      descriptionsPlaceholder: string
      icon: string
      iconPlaceholder: string
      icons: string
      iconsPlaceholder: string
      autoApprove: string
      autoApproveAll: string
      autoApproveRead: string
      autoApproveWrite: string
      autoApproveHelp: string
      submit: string
      add: string
      update: string
      cancel: string
      jsonConfigIntro: string
      jsonConfig: string
      jsonConfigPlaceholder: string
      jsonConfigExample: string
      parseSuccess: string
      configImported: string
      parseError: string
      skipToManual: string
      parseAndContinue: string
      folders: string
      addFolder: string
      selectFolder: string
      selectFolderError: string
      noFoldersSelected: string
    }
    deleteServer: string
    editServer: string
    setDefault: string
    removeDefault: string
    isDefault: string
    default: string
    setAsDefault: string
    removeServer: string
    confirmRemoveServer: string
    removeServerDialog: {
      title: string
    }
    confirmDelete: string
    resetToDefault: string
    resetConfirmTitle: string
    resetConfirmDescription: string
    resetConfirm: string
    tools: {
      searchPlaceholder: string
      noToolsAvailable: string
      selectToolToDebug: string
      dialogDescription: string
      toolsCount: string
      availableTools: string
      toolList: string
      functionDescription: string
      invalidJson: string
      inputHint: string
      required: string
      noDescription: string
      input: string
      path: string
      pathPlaceholder: string
      searchPattern: string
      searchPatternPlaceholder: string
      filePattern: string
      filePatternPlaceholder: string
      executeButton: string
      resultTitle: string
      runningTool: string
      loading: string
      error: string
      available: string
      none: string
      title: string
      description: string
      loadError: string
      parameters: string
      refresh: string
      disabled: string
      enableToUse: string
      enabled: string
      enabledDescription: string
      jsonInputPlaceholder: string
      type: string
      annotations: string
      empty: string
      invalidJsonFormat: string
    }
    inmemory: {
      bochaSearch: {
        name: string
        desc: string
      }
      buildInFileSystem: {
        name: string
        desc: string
      }
      Artifacts: {
        name: string
        desc: string
      }
      imageServer: {
        name: string
        desc: string
      }
      braveSearch: {
        name: string
        desc: string
      }
      powerpack: {
        name: string
        desc: string
      }
      difyKnowledge: {
        name: string
        desc: string
      }
      ragflowKnowledge: {
        name: string
        desc: string
      }
      fastGptKnowledge: {
        name: string
        desc: string
      }
      builtinKnowledge: {
        name: string
        desc: string
      }
      'deepchat-inmemory/custom-prompts-server': {
        name: string
        desc: string
      }
      'deepchat-inmemory/deep-research-server': {
        name: string
        desc: string
      }
      'deepchat-inmemory/auto-prompting-server': {
        name: string
        desc: string
      }
      'deepchat-inmemory/conversation-search-server': {
        name: string
        desc: string
      }
      'deepchat-inmemory/meeting-server': {
        name: string
        desc: string
      }
      'deepchat/apple-server': {
        name: string
        desc: string
      }
    }
    prompts: {
      noPromptsAvailable: string
      noDescription: string
      selectPrompt: string
      parameters: string
      input: string
      runningPrompt: string
      executeButton: string
      resultTitle: string
      resetToDefault: string
      invalidJson: string
      parametersHint: string
      dialogDescription: string
    }
    resources: {
      noResourcesAvailable: string
      selectResource: string
      loading: string
      loadContent: string
      pleaseSelect: string
      dialogDescription: string
    }
    type: {
      custom: string
      official: string
    }
    categories: {
      all: string
      chat: string
      reasoning: string
      vision: string
      network: string
      free: string
      rerank: string
      tool: string
      embedding: string
      imageGeneration: string
    }
    actions: {
      add: string
      enableAll: string
      disableAll: string
      rename: string
      delete: string
      cleanMessages: string
      pin: string
      unpin: string
      export: string
      exportText: string
    }
    tags: {
      reasoning: string
      chat: string
      code: string
      writing: string
      analysis: string
    }
    greeting: string
    prompt: string
    addTitle: string
    addDescription: string
    editTitle: string
    editDescription: string
    name: string
    namePlaceholder: string
    descriptionPlaceholder: string
    content: string
    contentPlaceholder: string
    basicInfo: string
    contentTip: string
    noPrompt: string
    noPromptDesc: string
    active: string
    noDescription: string
    customDate: string
    showMore: string
    showLess: string
    import: string
    exportSuccess: string
    exportFailed: string
    importSuccess: string
    importFailed: string
    importStats: string
    parameters: string
    addParameter: string
    noParameters: string
    noParametersDesc: string
    parameterName: string
    parameterDescription: string
    parameterNamePlaceholder: string
    parameterDescriptionPlaceholder: string
    required: string
    characters: string
    fileManagement: string
    uploadFromDevice: string
    uploadFromDeviceDesc: string
    uploadedFiles: string
    noFiles: string
    noFilesUploadDesc: string
    uploadSuccess: string
    uploadedCount: string
    deleteSuccess: string
    deleteFailed: string
    inactive: string
    clickToEnable: string
    clickToDisable: string
    enableSuccess: string
    disableSuccess: string
    toggleFailed: string
    enablePrompt: string
    sourceLocal: string
    sourceImported: string
    sourceBuiltin: string
    defaultSystemPrompt: string
    defaultSystemPromptPlaceholder: string
    defaultSystemPromptDescription: string
    typing: string
    saving: string
    saved: string
    saveDefaultPromptFailed: string
    chat: string
    welcome: string
    settings: string
    'settings-common': string
    'settings-provider': string
    'settings-mcp': string
    'settings-database': string
    'settings-about': string
    'settings-shortcut': string
    'settings-display': string
    'settings-knowledge-base': string
    'settings-prompt': string
    common: {
      title: string
      resetData: string
      language: string
      languageSelect: string
      searchEngine: string
      searchEngineSelect: string
      searchPreview: string
      searchAssistantModel: string
      selectModel: string
      proxyMode: string
      proxyModeSelect: string
      proxyModeSystem: string
      proxyModeNone: string
      proxyModeCustom: string
      customProxyUrl: string
      customProxyUrlPlaceholder: string
      invalidProxyUrl: string
      addCustomSearchEngine: string
      addCustomSearchEngineDesc: string
      searchEngineName: string
      searchEngineNamePlaceholder: string
      searchEngineUrl: string
      searchEngineUrlPlaceholder: string
      searchEngineUrlError: string
      deleteCustomSearchEngine: string
      deleteCustomSearchEngineDesc: string
      testSearchEngine: string
      testSearchEngineDesc: string
      testSearchEngineNote: string
      theme: string
      themeSelect: string
      closeToQuit: string
      shortcut: {
        title: string
        newChat: string
      }
      contentProtection: string
      contentProtectionDialogTitle: string
      contentProtectionEnableDesc: string
      contentProtectionDisableDesc: string
      contentProtectionRestartNotice: string
      soundEnabled: string
      copyWithCotEnabled: string
      loggingEnabled: string
      loggingDialogTitle: string
      loggingEnableDesc: string
      loggingDisableDesc: string
      loggingRestartNotice: string
      devToolsAutoOpen: string
      devToolsAutoOpenDesc: string
      devToolsDialogTitle: string
      devToolsEnableDesc: string
      devToolsDisableDesc: string
      devToolsRestartNotice: string
      openLogFolder: string
      notifications: string
      notificationsDesc: string
    }
    data: {
      title: string
      syncEnable: string
      syncFolder: string
      openSyncFolder: string
      lastSyncTime: string
      never: string
      startBackup: string
      backingUp: string
      importData: string
      incrementImport: string
      overwriteImport: string
      importConfirmTitle: string
      importConfirmDescription: string
      importing: string
      confirmImport: string
      importSuccessTitle: string
      importErrorTitle: string
    }
    model: {
      title: string
      systemPrompt: {
        label: string
        placeholder: string
        description: string
      }
      temperature: {
        label: string
        description: string
      }
      contextLength: {
        label: string
        description: string
      }
      responseLength: {
        label: string
        description: string
      }
      artifacts: {
        title: string
        description: string
      }
      provider: string
      modelList: string
      selectModel: string
      providerSetting: string
      configureModel: string
      addModel: string
      modelConfig: {
        title: string
        description: string
        maxTokens: {
          label: string
          description: string
        }
        contextLength: {
          label: string
          description: string
        }
        temperature: {
          label: string
          description: string
        }
        vision: {
          label: string
          description: string
        }
        functionCall: {
          label: string
          description: string
        }
        reasoning: {
          label: string
          description: string
        }
        thinkingBudget: {
          label: string
          description: string
          dynamic: string
          range: string
          onlySupported: string
          valueLabel: string
          placeholder: string
          forceEnabled: string
          dynamicPrefix: string
          notice: string
          warnings: {
            proNoDisable: string
            proCannotDisable: string
            flashLiteCannotSetZero: string
            modelCannotDisable: string
            flashLiteMinValue: string
            belowMin: string
            aboveMax: string
          }
          hints: {
            flashLiteDisable: string
            normalDisable: string
            withZeroAndDynamic: string
            withDynamic: string
          }
        }
        type: {
          label: string
          description: string
          options: {
            chat: string
            embedding: string
            rerank: string
            imageGeneration: string
          }
        }
        resetToDefault: string
        saveConfig: string
        cancel: string
        useModelDefault: string
        currentUsingModelDefault: string
        resetConfirm: {
          title: string
          message: string
          confirm: string
        }
        validation: {
          maxTokensRequired: string
          maxTokensMin: string
          maxTokensMax: string
          contextLengthRequired: string
          contextLengthMin: string
          contextLengthMax: string
          temperatureRequired: string
          temperatureMin: string
          temperatureMax: string
        }
      }
    }
    provider: {
      enable: string
      urlPlaceholder: string
      keyPlaceholder: string
      verifyKey: string
      howToGet: string
      refreshingModels: string
      getKeyTip: string
      getKeyTipEnd: string
      urlFormat: string
      modelList: string
      enableModels: string
      disableAllModels: string
      modelsEnabled: string
      verifyLink: string
      syncModelsFailed: string
      addCustomProvider: string
      delete: string
      stopModel: string
      pulling: string
      runModel: string
      toast: {
        modelRunning: string
        modelRunningDesc: string
      }
      dialog: {
        disableModel: {
          title: string
          content: string
          confirm: string
        }
        disableAllModels: {
          title: string
          content: string
          confirm: string
        }
        configModels: {
          title: string
        }
        verify: {
          missingFields: string
          failed: string
          success: string
        }
        addCustomProvider: {
          title: string
          description: string
          name: string
          namePlaceholder: string
          apiType: string
          apiTypePlaceholder: string
          apiKey: string
          apiKeyPlaceholder: string
          baseUrl: string
          baseUrlPlaceholder: string
          enable: string
        }
        deleteProvider: {
          title: string
          content: string
          confirm: string
        }
        deleteModel: {
          title: string
          content: string
          confirm: string
        }
        pullModel: {
          title: string
          pull: string
        }
        modelCheck: {
          title: string
          description: string
          model: string
          modelPlaceholder: string
          test: string
          checking: string
          success: string
          failed: string
          noModels: string
        }
      }
      pullModels: string
      refreshModels: string
      modelsRunning: string
      runningModels: string
      noRunningModels: string
      deleteModel: string
      deleteModelConfirm: string
      noLocalModels: string
      localModels: string
      azureApiVersion: string
      safety: {
        title: string
        blockNone: string
        blockSome: string
        blockMost: string
        blockHighest: string
      }
      serverList: string
      totalServers: string
      addServer: string
      autoStart: string
      githubCopilotAuth: string
      githubCopilotConnected: string
      githubCopilotNotConnected: string
      loginWithGitHub: string
      loggingIn: string
      githubCopilotLoginTip: string
      loginSuccess: string
      loginFailed: string
      tokenValid: string
      tokenInvalid: string
      disconnect: string
      disconnected: string
      disconnectFailed: string
      keyStatus: {
        usage: string
        remaining: string
      }
      select: string
      apiUrl: string
      apiKey: string
    }
    knowledgeBase: {
      title: string
      addKnowledgeBase: string
      selectKnowledgeBaseType: string
      difyDescription: string
      comingSoon: string
      featureNotAvailable: string
      addDifyConfig: string
      apiKey: string
      datasetId: string
      endpoint: string
      configAdded: string
      configAddedDesc: string
      addConfig: string
      moreComingSoon: string
      configUpdated: string
      configUpdatedDesc: string
      descriptionPlaceholder: string
      ragflowTitle: string
      ragflowDescription: string
      addRagflowConfig: string
      editRagflowConfig: string
      dify: string
      editDifyConfig: string
      fastgptTitle: string
      fastgptDescription: string
      addFastGptConfig: string
      editFastGptConfig: string
      builtInKnowledgeTitle: string
      builtInKnowledgeDescription: string
      addBuiltinKnowledgeConfig: string
      editBuiltinKnowledgeConfig: string
      descriptionDesc: string
      embeddingModel: string
      selectEmbeddingModel: string
      selectEmbeddingModelHelper: string
      rerankModel: string
      selectRerankModel: string
      chunkSize: string
      chunkOverlap: string
      fragmentsNumber: string
      chunkSizeHelper: string
      chunkOverlapHelper: string
      fragmentsNumberHelper: string
      modelNotFound: string
      modelNotFoundDesc: string
      removeBuiltinKnowledgeConfirmTitle: string
      removeBuiltinKnowledgeConfirmDesc: string
      advanced: string
      dimensions: string
      dimensionsPlaceholder: string
      autoDetectDimensions: string
      autoDetectHelper: string
      dimensionsHelper: string
      autoDetectDimensionsError: string
      normalized: string
      normalizedHelper: string
      chunkSizePlaceholder: string
      chunkOverlapPlaceholder: string
      return: string
      uploadHelper: string
      onlySupport: string
      searchKnowledge: string
      searchKnowledgePlaceholder: string
      noData: string
      file: string
      uploadProcessing: string
      uploadCompleted: string
      reAdd: string
      uploadError: string
      delete: string
      reason: string
      deleteSuccess: string
      copy: string
      copySuccess: string
      source: string
      dialog: {
        beforequit: {
          title: string
          description: string
          cancel: string
          confirm: string
        }
      }
      searchError: string
    }
    mcp: {
      title: string
      description: string
      enabledTitle: string
      enabledDescription: string
      enableToAccess: string
      marketplace: string
      technicalDetails: string
      httpServer: string
      localProcess: string
      restartServer: string
      viewLogs: string
      starting: string
      error: string
      tabs: {
        servers: string
        tools: string
        prompts: string
        resources: string
      }
      serverList: string
      addServer: string
      running: string
      stopped: string
      stopServer: string
      startServer: string
      noServersFound: string
      addServerDialog: {
        title: string
        description: string
      }
      editServerDialog: {
        title: string
        description: string
      }
      serverForm: {
        name: string
        namePlaceholder: string
        nameRequired: string
        type: string
        typePlaceholder: string
        typeStdio: string
        typeSse: string
        typeInMemory: string
        typeHttp: string
        baseUrl: string
        baseUrlPlaceholder: string
        command: string
        commandPlaceholder: string
        commandRequired: string
        args: string
        argsPlaceholder: string
        argsRequired: string
        env: string
        envPlaceholder: string
        envInvalid: string
        description: string
        descriptionPlaceholder: string
        descriptions: string
        descriptionsPlaceholder: string
        icon: string
        iconPlaceholder: string
        icons: string
        iconsPlaceholder: string
        autoApprove: string
        autoApproveAll: string
        autoApproveRead: string
        autoApproveWrite: string
        autoApproveHelp: string
        submit: string
        add: string
        update: string
        cancel: string
        jsonConfigIntro: string
        jsonConfig: string
        jsonConfigPlaceholder: string
        jsonConfigExample: string
        parseSuccess: string
        configImported: string
        parseError: string
        skipToManual: string
        parseAndContinue: string
        jsonParseError: string
        browseMarketplace: string
        imageModel: string
        customHeadersParseError: string
        customHeaders: string
        invalidKeyValueFormat: string
        npmRegistry: string
        npmRegistryPlaceholder: string
        browseHigress: string
        selectFolderError: string
        folders: string
        addFolder: string
        noFoldersSelected: string
        useE2B: string
        e2bDescription: string
        e2bApiKey: string
        e2bApiKeyPlaceholder: string
        e2bApiKeyHelp: string
        e2bApiKeyRequired: string
      }
      deleteServer: string
      editServer: string
      setDefault: string
      removeDefault: string
      isDefault: string
      default: string
      setAsDefault: string
      removeServer: string
      autoStart: string
      confirmRemoveServer: string
      removeServerDialog: {
        title: string
      }
      confirmDelete: {
        title: string
        description: string
        confirm: string
        cancel: string
      }
      resetToDefault: string
      resetConfirmTitle: string
      resetConfirmDescription: string
      resetConfirm: string
      builtInServers: string
      customServers: string
      builtIn: string
      cannotRemoveBuiltIn: string
      builtInServerCannotBeRemoved: string
      maxDefaultServersReached: string
      removeDefaultFirst: string
      higressMarket: string
      totalServers: string
    }
    about: {
      title: string
      version: string
      checkUpdate: string
      checking: string
      latestVersion: string
    }
    display: {
      fontSize: string
      'text-sm': string
      'text-base': string
      'text-lg': string
      'text-xl': string
      'text-2xl': string
      floatingButton: string
      floatingButtonDesc: string
    }
    shortcuts: {
      title: string
      pressKeys: string
      pressEnterToSave: string
      noModifierOnly: string
      keyConflict: string
      clearShortcut: string
      zoomIn: string
      zoomOut: string
      zoomReset: string
      goSettings: string
      cleanHistory: string
      deleteConversation: string
      hideWindow: string
      quitApp: string
      newWindow: string
      newTab: string
      closeTab: string
      showHideWindow: string
      newConversation: string
      nextTab: string
      previousTab: string
      specificTab: string
      lastTab: string
    }
    success: {
      importComplete: string
    }
    message: {
      toolbar: {
        save: string
      }
    }
    toolbar: {
      save: string
      cancel: string
      previousVariant: string
      nextVariant: string
      copy: string
      copyImage: string
      copyImageWithLongPress: string
      copyFromTopSuccess: string
      capturing: string
      retry: string
      fork: string
      edit: string
      delete: string
    }
    calling: string
    response: string
    end: string
    clickToView: string
    functionName: string
    permission: string
    params: string
    responseData: string
    newVersion: string
    version: string
    releaseDate: string
    releaseNotes: string
    later: string
    githubDownload: string
    netdiskDownload: string
    checkUpdate: string
    downloading: string
    installNow: string
    autoUpdate: string
    restarting: string
    alreadyUpToDate: string
    alreadyUpToDateDesc: string
    steps: {
      welcome: {
        title: string
        description: string
      }
      provider: {
        title: string
        description: string
      }
      configuration: {
        title: string
        description: string
      }
      complete: {
        title: string
        description: string
      }
    }
    complete: {
      title: string
      description: string
    }
    buttons: {
      getStarted: string
      next: string
      back: string
    }
  }
}
