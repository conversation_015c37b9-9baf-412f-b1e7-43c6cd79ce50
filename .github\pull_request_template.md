## Pull Request Description

**Is your feature request related to a problem? Please describe.**
A clear and concise description of what the problem is.
*For example: I'm always frustrated when [...] *

**Describe the solution you'd like**
A clear and concise description of what you want to happen.

**UI/UX changes for Desktop Application**
If this PR introduces UI/UX changes, please describe them in detail.
* Include screenshots or GIFs if applicable to visually demonstrate the changes.
* Explain the reasoning behind the UI/UX decisions and how they improve the user experience of the desktop application.

**Platform Compatibility Notes**
If this PR has specific platform compatibility considerations (Windows, macOS, Linux), please describe them here.
* Are there any platform-specific behaviors or code adjustments?
* Have you tested on all relevant platforms?

**Additional context**
Add any other context about the pull request here.

---

## Pull Request Description (中文)

**你的功能请求是否与某个问题有关？请描述一下。**
请对问题进行清晰扼要的描述。
*例如：我增加了 [...] 的功能 *

**请描述你希望的解决方案**
请对你希望实现的效果进行清晰扼要的描述。

**桌面应用程序的 UI/UX 更改**
如果此 PR 引入了 UI/UX 更改，请详细描述它们。
* 如果适用，请包含屏幕截图或 GIF 以直观地演示更改。
* 解释 UI/UX 决策背后的原因，以及它们如何改善桌面应用程序的用户体验。

**平台兼容性注意事项**
如果此 PR 具有特定的平台兼容性考虑因素（Windows、macOS、Linux），请在此处描述。
* 是否有任何平台特定的行为或代码调整？
* 你是否已在所有相关平台上进行过测试？

**附加背景**
在此处添加关于此 Pull Request 的任何其他背景信息。
