{"loading": "加载中...", "copy": "复制", "paste": "粘贴", "copySuccess": "复制成功", "copySuccessDesc": "内容已复制到剪贴板", "copyImageSuccess": "复制成功", "copyImageSuccessDesc": "图片已复制到剪贴板", "copyFailed": "复制失败", "copyFailedDesc": "无法复制内容到剪贴板", "copyCode": "复制代码", "export": "导出", "newChat": "新会话", "newTopic": "新话题", "cancel": "取消", "confirm": "确认", "enabled": "已启用", "disabled": "已禁用", "disclaimer": "免责声明", "close": "关闭", "image": "图片", "error": {"requestFailed": "请求失败...", "createChatFailed": "创建会话失败", "selectChatFailed": "选择会话失败", "renameChatFailed": "重命名会话失败", "deleteChatFailed": "删除会话失败", "cleanMessagesFailed": "清空会话消息失败", "userCanceledGeneration": "用户取消了生成", "sessionInterrupted": "会话意外中断，生成未完成", "noModelResponse": "模型未返回任何内容，可能是超时了", "invalidJson": "无效的JSON格式", "maximumToolCallsReached": "达到最大工具调用次数", "causeOfError": "错误可能原因：", "error400": "请求错误，参数或者兼容问题", "error401": "身份验证失败，配置了错误的 API Key或者域名", "error403": "禁止访问该模型，可能是余额不足或者没有权限访问", "error404": "请求地址不存在，配置的域名或者模型名错误了", "error429": "请求速度过快，被服务商限制了访问频率", "error500": "服务器出错了，请求的服务当前可能不太稳定，可以稍后再试试", "error529": "服务器出错了，请求的服务当前可能不太稳定，可以稍后再试试", "error502": "网关错误，请求的服务当前可能不太稳定，可以稍后再试试", "error503": "服务不可用，请求的服务可能当前不稳定，可以稍后再试试", "error504": "请求超时，请求的服务可能当前不稳定或者网络链路有故障，请检查代理等网络配置后再试试", "operationFailed": "操作失败"}, "resetDataConfirmTitle": "确定要重置所有数据吗？", "resetDataConfirmDescription": "这将会把所有数据恢复到默认设置。此操作无法撤销。", "title": "通用设置", "resetData": "重置数据", "language": "语言", "languageSelect": "选择语言", "searchEngine": "搜索引擎", "searchEngineSelect": "选择搜索引擎", "searchPreview": "搜索预览", "searchAssistantModel": "搜索模型", "selectModel": "选择模型", "proxyMode": "代理模式", "proxyModeSelect": "选择代理模式", "proxyModeSystem": "系统代理", "proxyModeNone": "不使用代理", "proxyModeCustom": "自定义代理", "customProxyUrl": "自定义代理地址", "customProxyUrlPlaceholder": "例如: http://127.0.0.1:7890", "invalidProxyUrl": "无效的代理地址，请输入有效的 http/https URL", "languageSystem": "跟随系统", "watermarkTip": "AI生成内容，请谨慎参考", "collapse": "收起", "expand": "展开", "add": "新增", "reset": "重置", "format": "格式化", "edit": "编辑", "delete": "删除", "save": "保存", "clear": "清除", "saved": "已保存", "closeToQuit": "关闭应用时退出程序"}