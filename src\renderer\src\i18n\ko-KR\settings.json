{"title": "설정", "common": {"title": "일반 설정", "modelSettings": "모델 설정", "searchSettings": "검색 설정", "networkSettings": "네트워크 설정", "interfaceSettings": "인터페이스 및 상호작용", "personalizationSettings": "개인화 설정", "securitySettings": "보안 및 개인정보", "operationSettings": "작동 설정", "resetData": "데이터 초기화", "language": "언어", "languageSelect": "언어 선택", "searchEngine": "검색 엔진", "searchEngineDesc": "웹 검색 향상에 사용할 검색 엔진을 선택하세요. Google, Bing 등 주요 검색 엔진과 사용자 정의 검색 엔진을 지원합니다", "searchAssistantModel": "보조 모델", "searchAssistantModelDesc": "검색 결과를 처리하고 답변을 생성할 모델을 선택하세요. 검색 향상의 품질과 속도에 영향을 줍니다", "selectModel": "모델 선택", "visionModel": "비전 모델", "visionModelDesc": "이미지 및 시각적 콘텐츠를 처리하는 데 사용되는 모델을 선택하여 이미지 설명, 분석 등의 기능에 사용됩니다", "visionModelNotSelectedWarning": "비전 모델이 선택되지 않았습니다. 이미지 기능을 사용할 때 먼저 설정하라는 메시지가 표시됩니다", "searchPreview": "검색 미리보기", "searchPreviewDesc": "검색 결과 페이지에서 미리보기 썸네일을 표시하여 검색 콘텐츠를 빠르게 톨색할 수 있습니다", "proxyMode": "프록시 모드", "proxyModeDesc": "네트워크 프록시 설정을 구성합니다. 해외 서비스 액세스를 위해 시스템 프록시, 프록시 없음 또는 사용자 정의 프록시를 선택하세요", "searchEngineSelect": "검색 엔진 선택", "proxyModeSelect": "프록시 모드 선택", "proxyModeSystem": "시스템 프록시", "proxyModeNone": "프록시 사용 안 함", "proxyModeCustom": "사용자 정의 프록시", "customProxyUrl": "사용자 정의 프록시 URL", "customProxyUrlPlaceholder": "예: http://127.0.0.1:7890", "invalidProxyUrl": "잘못된 프록시 URL, 유효한 http/https URL을 입력하세요", "addCustomSearchEngine": "사용자 정의 검색 엔진 추가", "addCustomSearchEngineDesc": "새 검색 엔진을 추가하려면 이름과 검색 URL을 제공해야 합니다. URL에는 {query}가 쿼리 자리 표시자로 포함되어야 합니다.", "searchEngineName": "검색 엔진 이름", "searchEngineNamePlaceholder": "검색 엔진 이름을 입력하세요", "searchEngineUrl": "검색 URL", "searchEngineUrlPlaceholder": "예: https://a.com/search?q={query}", "searchEngineUrlError": "URL에는 {query}가 쿼리 자리 표시자로 포함되어야 합니다", "deleteCustomSearchEngine": "커스텀 검색 엔진 삭제", "deleteCustomSearchEngineDesc": "정말로 커스텀 검색 엔진 \"{name}\"을(를) 삭제하시겠습니까? 이 작업은 되돌릴 수 없습니다.", "testSearchEngine": "검색 엔진 테스트", "testSearchEngineDesc": "{engine} 검색 엔진을 사용하여 \"날씨\"에 대한 테스트 검색을 수행합니다.", "testSearchEngineNote": "검색 페이지에 로그인이나 다른 작업이 필요한 경우 테스트 창에서 수행할 수 있습니다. 테스트가 완료되면 테스트 창을 닫아주세요.", "theme": "테마", "themeSelect": "테마 선택", "closeToQuit": "닫기 버튼을 클릭할 때 프로그램 종료", "contentProtection": "화면 보호", "contentProtectionDialogTitle": "화면 보호 전환 확인", "contentProtectionEnableDesc": "화면 보호를 활성화하면 화면 공유 소프트웨어가 DeepChat 창을 캡쳐할 수 없습니다. 이 기능은 모든 인터페이스를 완전히 숨기지 않습니다. 이 기능을 사용할 때는 항상 규정을 준수하세요. 또한, 모든 화면 공유 소프트웨어가 이 기능을 지원하지 않을 수 있습니다. 또한, 일부 환경에서는 검은색 창이 남을 수 있습니다.", "contentProtectionDisableDesc": "화면 보호를 비활성화하면 화면 공유 소프트웨어가 DeepChat 창을 캡쳐할 수 있습니다.", "contentProtectionRestartNotice": "이 설정을 변경하면 프로그램이 재시작됩니다. 계속하시겠습니까?", "soundEnabled": "소리 활성화", "copyWithCotEnabled": "COT 정보 복사", "loggingEnabled": "로그 활성화", "devToolsAutoOpen": "개발자 도구 자동 열기", "devToolsAutoOpenDesc": "창과 탭을 만들 때 개발자 도구를 자동으로 엽니다", "devToolsDialogTitle": "개발자 도구 설정 변경 확인", "devToolsEnableDesc": "개발자 도구 자동 열기를 활성화하면 새 창과 탭을 만들 때 자동으로 개발자 도구가 열립니다.", "devToolsDisableDesc": "개발자 도구 자동 열기를 비활성화하면 개발자 도구가 더 이상 자동으로 열리지 않습니다.", "devToolsRestartNotice": "이 설정은 즉시 적용되며 다음에 창을 만들 때 사용됩니다.", "loggingDialogTitle": "로그 설정 변경 확인", "loggingEnableDesc": "로그를 활성화하면 문제 진단 및 애플리케이션 개선에 도움이 됩니다. 로그 파일에는 민감한 정보가 포함될 수 있습니다.", "loggingDisableDesc": "로그를 비활성화하면 애플리케이션 로그 수집이 중지됩니다.", "loggingRestartNotice": "이 설정을 변경하면 프로그램이 재시작됩니다. 계속하시겠습니까?", "openLogFolder": "로그 폴더 열기", "shortcut": {"newChat": "새 채팅을 만듭니다", "title": "바로 가기 키 설정"}, "notifications": "시스템 알림", "notificationsDesc": "DeepChat이 전경에 있지 않으면 세션이 생성되면 시스템 알림이 전송됩니다."}, "data": {"title": "데이터 설정", "syncEnable": "데이터 동기화 활성화", "syncFolder": "동기화 폴더", "openSyncFolder": "동기화 폴더 열기", "lastSyncTime": "마지막 동기화 시간", "never": "동기화 안 됨", "startBackup": "지금 백업하기", "backingUp": "백업 중...", "importData": "데이터 가져오기", "incrementImport": "증분 가져오기", "overwriteImport": "덮어쓰기 가져오기", "importConfirmTitle": "데이터 가져오기 확인", "importConfirmDescription": "가져오기를 실행하면 채팅 기록과 설정을 포함한 모든 현재 데이터가 덮어쓰기됩니다. 중요한 데이터를 백업했는지 확인하세요. 가져오기 후에는 애플리케이션을 다시 시작해야 합니다.", "importing": "가져오는 중...", "confirmImport": "가져오기 확인", "importSuccessTitle": "가져오기 성공", "importErrorTitle": "가져오기 실패", "resetData": "데이터 초기화", "resetConfirmTitle": "데이터 초기화 확인", "resetConfirmDescription": "초기화할 데이터 유형을 선택하세요. 이 작업은 되돌릴 수 없으며, 초기화 후 애플리케이션이 자동으로 재시작됩니다.", "resetChatData": "채팅 데이터 초기화", "resetChatDataDesc": "모든 채팅 기록과 대화 내역을 삭제", "resetKnowledgeData": "지식베이스 데이터 초기화", "resetKnowledgeDataDesc": "모든 지식베이스 파일과 벡터 데이터를 삭제", "resetConfig": "설정 초기화", "resetConfigDesc": "모든 앱 설정, 모델 설정, 사용자 정의 프롬프트를 삭제", "resetAll": "완전 초기화", "resetAllDesc": "채팅 기록, 설정, 캐시 파일을 포함한 모든 데이터를 삭제", "resetting": "초기화 중...", "confirmReset": "초기화 확인", "resetCompleteDevTitle": "데이터 초기화 완료", "resetCompleteDevMessage": "개발 환경에서는 수동으로 애플리케이션을 재시작하세요. 현재 프로세스를 중지하고 pnpm run dev를 다시 실행하세요"}, "model": {"title": "모델 설정", "systemPrompt": {"label": "시스템 프롬프트", "placeholder": "시스템 프롬프트를 입력하세요...", "description": "AI 어시스턴트의 행동과 역할을 정의하는 시스템 프롬프트 설정"}, "temperature": {"label": "모델 온도", "description": "출력의 무작위성을 제어합니다. 값이 높을수록 더 창의적인 응답이 생성됩니다"}, "contextLength": {"label": "컨텍스트 길이", "description": "대화 컨텍스트의 최대 길이 설정"}, "responseLength": {"label": "응답 길이", "description": "AI 응답의 최대 길이 설정"}, "artifacts": {"description": "Artifacts 기능을 활성화하면 AI가 더 풍부한 콘텐츠를 생성할 수 있습니다", "title": "Artifacts 효과"}, "addModel": "모델 추가", "configureModel": "모델 구성", "modelList": "모델 목록", "provider": "서비스 제공 업체", "providerSetting": "서비스 제공 업체 설정", "selectModel": "모델 선택", "modelConfig": {"cancel": "취소", "contextLength": {"description": "모델이 처리 할 수있는 컨텍스트 길이를 설정하십시오", "label": "컨텍스트 길이"}, "description": "이 구성은 현재 모델에만 유효하며 다른 모델에는 영향을 미치지 않습니다. 주의해서 수정하십시오. 잘못된 매개 변수로 인해 모델이 제대로 작동하지 않을 수 있습니다.", "functionCall": {"description": "모델이 기능을 지원하는지 여부는 기본적으로 호출을 지원합니다 (DeepChat은이 옵션을 끄면 기능 호출을 자동으로 시뮬레이션합니다).", "label": "기능 호출"}, "maxTokens": {"description": "모델의 단일 출력에 대한 최대 토큰 수를 설정하십시오.", "label": "최대 출력 길이"}, "reasoning": {"description": "모델이 추론 능력을 지원합니까?", "label": "추론 능력"}, "thinkingBudget": {"label": "사고 예산", "description": "모델 사고 길이 제한", "dynamic": "동적 사고", "range": "범위: {min} - {max}", "onlySupported": "Gemini 2.5 Flash, 2.5 Pro 및 2.5 Flash-Lite에서만 지원", "valueLabel": "사고 예산 값", "placeholder": "사고 예산 값 입력", "forceEnabled": "Gemini 2.5 시리즈 모델은 사고 예산을 강제로 활성화합니다", "dynamicPrefix": "-1 = 동적 사고", "notice": "주의: ", "warnings": {"proNoDisable": "이 모델은 사고 비활성화를 지원하지 않습니다, 최소값 128", "proCannotDisable": "Gemini 2.5 Pro는 사고 기능을 비활성화할 수 없습니다", "flashLiteCannotSetZero": "Gemini 2.5 Flash-Lite는 0으로 설정할 수 없습니다, 최소값은 512", "modelCannotDisable": "이 모델은 사고 기능을 비활성화할 수 없습니다", "flashLiteMinValue": "사고 예산을 구체적인 값으로 설정할 때 512 미만일 수 없습니다 (또는 0으로 사고 비활성화, -1로 동적 사고 활성화)", "belowMin": "사고 예산은 {min} 미만일 수 없습니다{hint}", "aboveMax": "사고 예산은 {max}를 초과할 수 없습니다"}, "hints": {"flashLiteDisable": ", 0 = 사고 비활성화, 구체적인 값 최소 512", "normalDisable": ", 0 = 사고 비활성화", "withZeroAndDynamic": "(또는 0으로 사고 비활성화, -1로 동적 사고 활성화)", "withDynamic": "(또는 -1로 동적 사고 활성화)"}}, "resetConfirm": {"confirm": "재설정 확인", "message": "이 모델의 구성을 기본값으로 재설정 하시겠습니까? 이 작업은 돌이킬 수 없습니다.", "title": "재설정 확인"}, "reasoningEffort": {"label": "추론 노력도", "description": "모델 추론의 깊이를 제어합니다. 더 높은 노력도는 더 나은 결과를 생성하지만 응답이 느려집니다", "placeholder": "추론 노력도 선택", "options": {"minimal": "Minimal - 가장 빠른 응답", "low": "Low - 낮은 노력", "medium": "Medium - 중간 노력", "high": "High - 높은 노력"}}, "verbosity": {"label": "상세도", "description": "모델 응답의 상세 수준과 길이를 제어합니다", "placeholder": "상세도 선택", "options": {"low": "Low - 간결한 답변", "medium": "Medium - 균형잡힌 상세", "high": "High - 상세한 답변"}}, "resetToDefault": "기본값으로 재설정하십시오", "saveConfig": "구성 저장", "useModelDefault": "모델 기본 설정 사용", "currentUsingModelDefault": "현재 모델 기본 설정 사용 중", "temperature": {"description": "출력의 무작위성을 제어하십시오. 대부분의 모델은 0-1이며 일부는 0-2 사이의 지원입니다. 무작위성이 높아집니다.", "label": "온도"}, "title": "사용자 정의 모델 매개 변수", "type": {"description": "모델 유형을 선택하십시오", "label": "모델 유형", "options": {"chat": "언어 모델", "embedding": "임베드 모델", "imageGeneration": "이미지 생성 모델", "rerank": "모델을 재정렬하십시오"}}, "validation": {"contextLengthMax": "컨텍스트 길이는 100000000을 초과 할 수 없습니다", "contextLengthMin": "컨텍스트 길이는 0보다 커야합니다", "contextLengthRequired": "컨텍스트 길이는 비어있을 수 없습니다", "maxTokensMax": "최대 출력 길이는 1000000을 초과 할 수 없습니다", "maxTokensMin": "최대 출력 길이는 0보다 크기가 높아야합니다", "maxTokensRequired": "최대 출력 길이는 비어있을 수 없습니다", "temperatureMax": "온도는 2보다 작거나 동일해야합니다.", "temperatureMin": "온도는 0보다 크거나 같아야합니다.", "temperatureRequired": "온도는 비어있을 수 없습니다"}, "vision": {"description": "모델이 시각적 능력을 지원합니까?", "label": "시각적 능력"}}}, "provider": {"search": "프로바이더 플랫폼 검색…", "enable": "서비스 활성화", "enabled": "활성화됨", "disabled": "비활성화됨", "urlPlaceholder": "API URL을 입력하세요", "keyPlaceholder": "API 키를 입력하세요", "accessKeyIdPlaceholder": "AWS Access Key ID를 입력하세요", "secretAccessKeyPlaceholder": "AWS Secret Access Key를 입력하세요", "regionPlaceholder": "AWS 리전을 입력하세요", "verifyKey": "키 확인", "howToGet": "얻는 방법", "getKeyTip": "다음으로 이동하세요", "getKeyTipEnd": "API 키를 얻으려면", "modelList": "모델 목록", "enableModels": "모델 활성화", "disableAllModels": "모든 모델 비활성화", "modelsEnabled": "모델이 활성화되었습니다", "noModelsEnabled": {"title": "활성화된 모델이 없습니다", "description": "「모델 활성화」 버튼을 클릭하여 사용할 모델을 수동으로 선택하십시오."}, "verifyLink": "링크 확인", "syncModelsFailed": "모델 동기화 실패...", "addCustomProvider": "커스텀 제공자 추가", "delete": "삭제", "stopModel": "모델 중지", "pulling": "가져오는 중...", "runModel": "모델 실행", "dialog": {"disableModel": {"title": "모델 비활성화 확인", "content": "모델 \"{name}\"을(를) 비활성화하시겠습니까?", "confirm": "비활성화"}, "disableAllModels": {"title": "모든 모델 비활성화 확인", "content": "\"{name}\"의 모든 모델을 비활성화하시겠습니까?", "confirm": "모두 비활성화"}, "configModels": {"title": "모델 목록 구성", "description": "활성화하거나 비활성화할 모델 선택"}, "verify": {"missingFields": "API 키와 API URL을 입력하세요", "failed": "확인 실패", "success": "확인 성공", "failedDesc": "API 키 또는 설정 확인에 실패했습니다. 설정을 확인해주세요", "successDesc": "API 키와 설정이 성공적으로 확인되었습니다. 사용 가능합니다", "connectionError": "연결 오류, 네트워크 연결 및 API 주소를 확인하십시오.", "serverError": "서버 오류, 나중에 다시 시도하십시오", "unauthorized": "인증 실패, API 키가 유효하지 않거나 만료되었습니다"}, "addCustomProvider": {"title": "커스텀 제공자 추가", "description": "제공자에 필요한 정보를 입력하세요", "name": "이름", "namePlaceholder": "제공자 이름을 입력하세요", "apiType": "API 유형", "apiTypePlaceholder": "API 유형을 선택하세요", "apiKey": "API 키", "apiKeyPlaceholder": "API 키를 입력하세요", "baseUrl": "기본 URL", "baseUrlPlaceholder": "기본 URL을 입력하세요", "enable": "제공자 활성화"}, "deleteProvider": {"title": "제공자 삭제", "content": "제공자 \"{name}\"을(를) 삭제하시겠습니까? 이 작업은 취소할 수 없습니다.", "confirm": "삭제"}, "deleteModel": {"title": "모델 삭제 확인", "content": "모델 \"{name}\"을(를) 삭제하시겠습니까? 이 작업은 취소할 수 없습니다.", "confirm": "삭제"}, "pullModel": {"title": "모델 가져오기", "description": "로컬로 다운로드할 모델 선택", "pull": "가져오기"}, "modelCheck": {"checking": "테스트 ...", "description": "연결 및 유용성 테스트 모델을 선택하십시오", "failed": "모델 테스트가 실패했습니다", "model": "모델을 선택하십시오", "modelPlaceholder": "테스트 할 모델을 선택하십시오", "noModels": "이 서비스 제공 업체에는 사용할 수있는 모델이 없습니다", "success": "모델 테스트가 성공했습니다", "test": "테스트를 시작하십시오", "title": "모델 점검"}}, "pullModels": "모델 가져오기", "refreshModels": "모델 새로고침", "modelsRunning": "실행 중인 모델", "runningModels": "실행 중인 모델", "noRunningModels": "실행 중인 모델 없음", "deleteModel": "모델 삭제", "deleteModelConfirm": "모델 \"{name}\"을(를) 삭제하시겠습니까? 이 작업은 취소할 수 없습니다.", "noLocalModels": "로컬 모델 없음", "localModels": "로컬 모델", "urlFormat": "API 샘플 : {defaultUrl}", "azureApiVersion": "API 버전", "safety": {"title": "보안 설정", "blockHighest": "높은 위험을 차단하십시오", "blockMost": "중간 위험을 차단합니다", "blockNone": "차단되지 않았습니다", "blockSome": "낮은 위험을 차단하십시오"}, "serverList": "서버 목록", "totalServers": "총 서버 수", "addServer": "서버 추가", "autoStart": "자기 시작", "githubCopilotAuth": "GitHub Copilot 인증", "githubCopilotConnected": "GitHub Copilot 연결됨", "githubCopilotNotConnected": "GitHub Copilot 연결되지 않음", "loginWithGitHub": "GitHub로 로그인", "loggingIn": "로그인 중...", "githubCopilotLoginTip": "DeepChat이 GitHub Copilot 구독에 액세스하도록 허용하세요. Copilot API에 액세스하려면 'read:user' 및 'read:org' 권한이 필요합니다.", "loginSuccess": "로그인 성공", "loginFailed": "로그인 실패", "tokenValid": "토큰이 유효합니다", "tokenInvalid": "토큰이 유효하지 않습니다", "disconnect": "연결 해제", "disconnected": "성공적으로 연결 해제됨", "disconnectFailed": "연결 해제 실패", "keyStatus": {"remaining": "나머지 금액", "usage": "사용된"}, "refreshingModels": "상쾌한 ...", "toast": {"modelRunning": "모델이 실행 중입니다", "modelRunningDesc": "먼저 모델 {model}을 중지 한 다음 삭제하십시오."}, "anthropicApiKeyTip": "API 키를 얻으려면 Anthropic Console으로 이동하십시오.", "anthropicConnected": "인류 연결", "anthropicNotConnected": "인류가 연결되어 있지 않습니다", "anthropicOAuthTip": "인류 계정에 액세스하려면 DeepChat 인증을 클릭하십시오", "oauthLogin": "OAUTH 로그인", "authMethod": "인증 방법", "authMethodPlaceholder": "인증 방법 선택", "apiKeyLabel": "API Key", "apiUrlLabel": "API URL", "anthropicOAuthFlowTip": "시스템은 권한 부여 창을 자동으로 엽니 다. 다시 와서 승인 후 인증 코드를 입력하십시오.", "anthropicBrowserOpened": "외부 브라우저가 열려 있습니다", "anthropicCodeInstruction": "외부 브라우저에서 인증을 완료하고 얻은 인증 코드를 아래 입력 상자에 붙여 넣으십시오.", "browserOpenedSuccess": "외부 브라우저가 열려 있습니다. 승인을 완료하십시오", "codeRequired": "인증 코드를 입력하십시오", "inputOAuthCode": "인증 코드를 입력하십시오", "codeExchangeFailed": "승인 코드 교환에 실패했습니다", "invalidCode": "잘못된 권한 부여 코드", "oauthCodeHint": "외부 브라우저에서 승인을 완료 한 후 여기에 승인 코드를 붙여 넣으십시오.", "oauthCodePlaceholder": "승인 코드를 입력하십시오 ...", "verifyConnection": "연결 확인", "manageModels": "모델 관리", "anthropicOAuthActiveTip": "OAuth 인증이 활성화되어 있어 Anthropic 서비스를 직접 사용할 수 있습니다", "oauthVerifySuccess": "OAuth 연결 확인 성공", "oauthVerifyFailed": "OAuth 연결 확인 실패", "configurationSaved": "구성 저장", "configurationUpdated": "구성 업데이트", "dataRefreshed": "데이터가 새로 고쳐졌습니다", "modelscope": {"apiKey": "API 키", "apiKeyHelper": "ModelScope 콘솔에서 API 키를 얻으십시오", "apiKeyPlaceholder": "ModelScope API 키를 입력하십시오", "baseUrl": "API 주소", "baseUrlHelper": "ModelScope API 서비스 주소", "connected": "연결", "connecting": "연결 ...", "description": "ModelScope는 Alibaba Damo Academy가 시작한 Model-as-A-Service 공유 플랫폼입니다.", "details": {"apiConfig": "API 구성", "mcpSync": "MCP 동기화", "modelManagement": "모델 관리", "operationalDescription": "ModelScope 플랫폼에서 직접 사용할 수있는 MCP 서버 동기화", "operationalServers": "서버 작동", "rateLimitConfig": "요율 제한 구성", "safetySettings": "보안 설정", "specialConfig": "특별 구성", "syncFromModelScope": "ModelScope에서 동기화됩니다", "title": "제공자 설정 세부 정보"}, "invalidKey": "잘못된 API 키", "keyRequired": "API 키를 입력하십시오", "mcpSync": {"authenticationFailed": "인증 실패, API 키를 확인하십시오", "convertingServers": "서버 구성 변환 ...", "description": "Modescope에서 로컬 구성으로 MCP 서버를 동기화하면 일반적으로 사용되는 MCP 도구를 빠르게 추가 할 수 있습니다. 모든 서비스는 기본적으로 비활성화되며 가져온 후 수동으로 활성화 할 수 있습니다.", "errorDetails": "오류 세부 사항", "errors": "오류 {count}", "fetchingServers": "MCP 서버 목록 얻기 ...", "imported": "{count} 서비스가 가져 왔습니다", "importingServers": "서버 구성 가져 오기 ...", "invalidServerData": "잘못된 서버 데이터", "noApiKey": "먼저 ModelScope API 키를 구성하십시오", "noOperationalUrls": "사용 가능한 운영 주소가 없습니다", "noServersFound": "사용 가능한 MCP 서비스가 없습니다", "pageNumber": "페이지 번호", "pageNumberPlaceholder": "페이지 번호를 입력하십시오", "pageSize": "페이지 당 수량", "serverAlreadyExists": "서버가 이미 존재하고 가져 오기를 건너 뛰십시오", "skipped": "{count} 서비스를 건너 뛰십시오", "sync": "동기화를 시작하십시오", "syncComplete": "동기 완성", "syncing": "동기화...", "title": "MCP 서비스 동기화"}, "name": "ModelsCope", "networkError": "네트워크 연결 오류", "notConnected": "연결되지 않았습니다", "verifyFailed": "확인이 실패했습니다", "verifySuccess": "확인이 성공적입니다"}, "operationFailed": "작동 실패", "operationSuccess": "운영이 성공적입니다", "settingsApplied": "설정이 적용됩니다", "bedrockLimitTip": "* Anthropic Claude(Opus, Sonnet, Haiku 모델 포함)만 지원합니다.", "bedrockVerifyTip": "DeepChat은 검증을 위해 Claude 3.5 Sonnet을 사용합니다. 호출 권한이 없으면 검증이 실패합니다. 이는 다른 모델 사용에는 영향을 미치지 않습니다."}, "knowledgeBase": {"title": "지식 베이스 설정", "addKnowledgeBase": "지식 베이스 추가", "selectKnowledgeBaseType": "추가할 지식 베이스 유형을 선택하세요", "difyDescription": "Dify 지식 베이스는 문서 데이터를 관리하고 사용하는 데 도움이 됩니다", "comingSoon": "출시 예정", "featureNotAvailable": "이 기능은 아직 사용할 수 없습니다", "addDifyConfig": "Dify 구성 추가", "apiKey": "API 키", "datasetId": "데이터셋 ID", "endpoint": "API 엔드포인트", "configAdded": "구성 추가됨", "configAddedDesc": "{name} 구성이 성공적으로 추가되었습니다", "addConfig": "구성 추가", "moreComingSoon": "더 많은 지식 베이스 유형이 곧 제공될 예정입니다", "configUpdated": "구성 업데이트됨", "configUpdatedDesc": "{name} 구성이 성공적으로 업데이트되었습니다", "descriptionPlaceholder": "예: 회사 제품 문서 지식 베이스", "ragflowTitle": "RAGFlow 지식 베이스", "ragflowDescription": "RAGFlow는 다양한 검색 방법과 문서 관리 기능을 지원하는 강력한 지식 베이스 관리 시스템입니다.", "addRagflowConfig": "RAGFlow 구성 추가", "editRagflowConfig": "RAGFlow 구성 편집", "dify": "Dify Knowledge Base", "editDifyConfig": "dify 구성을 수정하십시오", "fastgptTitle": "FastGPT 지식 베이스", "fastgptDescription": "FastGPT는 여러 검색 방법과 문서 관리 기능을 지원하는 강력한 지식 베이스 관리 시스템입니다.", "addFastGptConfig": "FastGPT 설정 추가", "editFastGptConfig": "FastGPT 설정 편집", "builtInKnowledgeDescription": "내장 지식 기반은 오프라인 환경에서 몇 가지 기본 기능을 가능하게하는 간단한 구현을 제공합니다.", "builtInKnowledgeTitle": "내장 지식 기반", "addBuiltinKnowledgeConfig": "내장 된 지식 기반 구성을 추가하십시오", "editBuiltinKnowledgeConfig": "내장 된 지식 기반 구성을 편집하십시오", "chunkSize": "블록 크기", "chunkSizeHelper": "문서를 세그먼트로 자르면 각 세그먼트의 크기는 모델 컨텍스트 한계를 초과 할 수 없습니다.", "chunkOverlap": "겹치는 크기", "chunkOverlapHelper": "인접한 텍스트 블록 사이에 반복되는 콘텐츠의 양은 세그먼트 된 텍스트 블록 사이에 여전히 상황에 맞는 연결이 있음을 보장하여 긴 텍스트의 모델 처리의 전반적인 효과를 향상시킵니다.", "selectEmbeddingModel": "임베드 모델을 선택하십시오", "modelNotFound": "서비스 제공자 {provider} 또는 model {model}을 찾을 수 없습니다", "modelNotFoundDesc": "모델이 올바르게 구성되고 모델이 활성화되어 있는지 확인하십시오. 서비스 제공 업체 설정에서 모델 구성을 확인할 수 있습니다.", "removeBuiltinKnowledgeConfirmDesc": "내장 지식 기반 구성을 삭제하면 모든 관련 데이터가 삭제되며 복원 할 수 없습니다. 조심하세요.", "removeBuiltinKnowledgeConfirmTitle": "내장 지식 기반 {name}을 삭제하도록 확인 하시겠습니까?", "descriptionDesc": "AI 가이 지식 기반을 검색할지 여부를 결정하도록 지식 기반에 대한 설명", "advanced": "고급 옵션", "autoDetectDimensions": "내장 치수를 자동으로 감지합니다", "autoDetectHelper": "내장 치수를 자동으로 감지하고 소량의 토큰을 소비합니다.", "chunkOverlapPlaceholder": "기본값, 수정은 권장되지 않습니다", "chunkSizePlaceholder": "기본값, 수정은 권장되지 않습니다", "dimensions": "치수를 포함시킵니다", "dimensionsPlaceholder": "1024와 같은 치수 크기를 포함시킵니다", "selectEmbeddingModelHelper": "임베딩 모델은 지식 기반을 창출 한 후 금지됩니다", "dimensionsHelper": "모델이 세트 임베드 치수 크기를 지원하는지 확인하십시오.", "autoDetectDimensionsError": "임베디드 치수 고장을 자동으로 감지합니다", "fragmentsNumber": "요청 된 문서 조각의 수", "fragmentsNumberHelper": "요청 된 문서의 조각이 많을수록 더 많은 정보가 제공되지만 더 많은 토큰을 소비해야합니다.", "selectRerankModel": "재주문 모델을 선택하십시오", "rerankModel": "모델을 재정렬하십시오", "embeddingModel": "임베드 모델", "return": "반품", "uploadHelper": "여기에서 파일을 업로드하거나 드래그하려면 클릭하십시오", "fileSupport": "{accept} 및 {count}개의 다른 형식을 지원합니다", "searchKnowledge": "지식 기반을 검색하십시오", "searchKnowledgePlaceholder": "쿼리 내용을 입력하십시오", "noData": "아직 데이터가 없습니다", "file": "문서", "uploadProcessing": "업로드", "uploadCompleted": "업로드 완료", "reAdd": "재 포장", "uploadError": "업로드 실패", "delete": "삭제", "reason": "이유", "deleteSuccess": "성공적으로 삭제하십시오", "copy": "복사", "copySuccess": "성공적으로 복사하십시오", "source": "원천", "normalized": "L2 정규화", "normalizedHelper": "모델이 출력 벡터의 L2 정규화를 지원하는지 확인하십시오.", "dialog": {"beforequit": {"cancel": "취소", "confirm": "확인하다", "title": "확인 확인", "description": "실행중인 지식 기반 작업이 있습니다. 소프트웨어를 종료 하시겠습니까? 소프트웨어를 다시 시작한 후 낙태 된 작업을 복원 할 수 있습니다."}}, "searchError": "쿼리가 실패했습니다", "processing": "업로드", "paused": "일시 정지를 업로드하십시오", "unknown": "알 수없는 상태", "reAddFile": {"title": "확인 확인", "content": "\"{filename}\"파일을 다시 업로드하시겠습니까?"}, "deleteFile": {"title": "파일 확인 삭제", "content": "\"{filename}\"파일을 삭제해야합니까? 이 작업은 복원되지 않습니다."}, "resumeAllPausedTasks": "원 클릭 복구", "pauseAllRunningTasks": "한 번의 클릭 일시 중지", "separators": "블록 분리기", "separatorsHelper": "문서 분할 구분 기호, 단일 구분 기호는 큰따옴표(\"\")로 묶고, 구분 기호는 쉼표(,)로 구분합니다", "invalidSeparators": "잘못된 분리기", "selectLanguage": "사전 설정을 선택하십시오", "separatorsPreset": "사전 설정을로드합니다", "promptManagement": {"title": "프롬프트 관리", "description": "사용자 정의 프롬프트 템플릿을 관리하고 사용하여 AI와 더 나은 상호작용을 도와줍니다"}}, "mcp": {"title": "MCP 설정", "description": "MCP(Model Context Protocol) 서버 및 도구 관리 및 구성", "enabledTitle": "MCP 활성화", "enabledDescription": "MCP 기능 및 도구 활성화 또는 비활성화", "enableToAccess": "구성 옵션에 액세스하려면 MCP를 활성화하세요", "tabs": {"servers": "서버", "tools": "도구", "prompts": "신속한 단어", "resources": "의지"}, "serverList": "서버 목록", "totalServers": "총 서버 수", "addServer": "서버 추가", "running": "실행 중", "stopped": "중지됨", "stopServer": "서버 중지", "startServer": "서버 시작", "noServersFound": "서버 없음", "addServerDialog": {"title": "서버 추가", "description": "새 MCP 서버 구성"}, "editServerDialog": {"title": "서버 편집", "description": "MCP 서버 구성 편집"}, "serverForm": {"name": "서버 이름", "namePlaceholder": "서버 이름 입력", "nameRequired": "서버 이름이 필요합니다", "type": "서버 유형", "typePlaceholder": "서버 유형 선택", "typeStdio": "표준 입력 및 출력 (STDIO)", "typeSse": "서버 보내기 이벤트 (SSE)", "typeInMemory": "인메모리", "baseUrl": "기본 URL", "baseUrlPlaceholder": "서버 기본 URL 입력(예: http://localhost:3000)", "command": "명령어", "commandPlaceholder": "명령어 입력", "commandRequired": "명령어는 비어 있을 수 없습니다", "args": "매개변수", "argsPlaceholder": "매개변수 입력, 공백으로 구분", "argsRequired": "매개변수는 비어 있을 수 없습니다", "env": "환경 변수", "envPlaceholder": "JSON 형식의 환경 변수 입력", "envInvalid": "환경 변수는 유효한 JSON 형식이어야 합니다", "description": "설명", "descriptionPlaceholder": "서버 설명 입력", "descriptions": "설명", "descriptionsPlaceholder": "서버 설명 입력", "icon": "아이콘", "iconPlaceholder": "아이콘 입력", "icons": "아이콘", "iconsPlaceholder": "아이콘 입력", "autoApprove": "자동 승인", "autoApproveAll": "전체", "autoApproveRead": "읽기", "autoApproveWrite": "쓰기", "autoApproveHelp": "자동 승인할 작업 유형을 선택하세요. 사용자 확인 없이 실행할 수 있습니다", "submit": "제출", "add": "추가", "update": "업데이트", "cancel": "취소", "jsonConfigIntro": "JSON 구성을 직접 붙여넣거나 서버를 수동으로 구성하는 것 중에서 선택할 수 있습니다.", "jsonConfig": "JSON 구성", "jsonConfigPlaceholder": "MCP 서버의 JSON 형식 구성을 붙여넣으세요", "jsonConfigExample": "JSON 구성 예시", "parseSuccess": "구성 파싱 성공", "configImported": "구성을 성공적으로 가져왔습니다", "parseError": "파싱 오류", "skipToManual": "수동 구성으로 건너뛰기", "parseAndContinue": "파싱 및 계속", "jsonParseError": "JSON 파싱이 실패했습니다", "typeHttp": "스트리밍 HTTP 요청 (HTTP)", "browseMarketplace": "MCP 서비스 시장을 탐색하십시오", "imageModel": "시각적 모델을 선택하십시오", "customHeadersParseError": "사용자 정의 헤더 구문 분석이 실패했습니다", "customHeaders": "사용자 정의 요청 헤더", "invalidKeyValueFormat": "잘못된 요청 헤더 형식, 입력이 올바른지 확인하십시오.", "npmRegistry": "사용자 정의 NPM 레지스트리", "npmRegistryPlaceholder": "사용자 정의 NPM 레지스트리를 설정하고 시스템을 남겨두고 가장 빠른 것을 자동으로 선택하십시오.", "browseHigress": "Higress MCP Marketplace를보십시오", "selectFolderError": "폴더 선택 오류", "folders": "폴더에 액세스 할 수 있습니다", "addFolder": "폴더를 추가하십시오", "noFoldersSelected": "폴더가 선택되지 않았습니다", "useE2B": "E2B 샌드 박스를 활성화하십시오", "e2bDescription": "E2B 샌드 박스를 사용하여 파이썬 코드를 실행하십시오", "e2bApiKey": "E2B Apikey", "e2bApiKeyPlaceholder": "E2B_1111XX *******와 같은 E2B API 키를 여기에 입력하십시오.", "e2bApiKeyHelp": "apikey를 얻으려면 e2b.dev로 이동하십시오", "e2bApiKeyRequired": "E2B 기능을 활성화하려면 Apikey를 입력해야합니다", "clickToEdit": "전체 내용을 보려면 편집을 클릭하십시오"}, "deleteServer": "서버 삭제", "editServer": "서버 편집", "setDefault": "기본 설정", "removeDefault": "기본 설정 제거", "isDefault": "기본 서버", "default": "기본", "setAsDefault": "기본 설정", "removeServer": "서버 삭제", "autoStart": "자동 시작", "confirmRemoveServer": "서버 {name}을(를) 삭제하시겠습니까? 이 작업은 취소할 수 없습니다.", "removeServerDialog": {"title": "서버 삭제"}, "confirmDelete": {"title": "삭제 확인", "description": "서버 {name}을(를) 삭제하시겠습니까? 이 작업은 취소할 수 없습니다.", "confirm": "삭제", "cancel": "취소"}, "resetToDefault": "기본값으로 재설정", "resetConfirmTitle": "기본 서버로 재설정", "resetConfirmDescription": "이 작업은 사용자 정의 서버를 유지하면서 모든 기본 서버를 복원합니다. 기본 서버에 대한 모든 수정 사항이 손실됩니다.", "resetConfirm": "재설정", "builtInServers": "내장 서버", "customServers": "사용자 정의 서버", "builtIn": "내장", "cannotRemoveBuiltIn": "내장 서버를 제거할 수 없음", "builtInServerCannotBeRemoved": "내장 서버는 제거할 수 없으며, 매개변수와 환경 변수만 수정할 수 있습니다", "marketplace": "MCP 시장으로 가서 한 번의 클릭으로 설치하십시오.", "maxDefaultServersReached": "최대 3 개의 기본 서버 만 설정할 수 있습니다", "removeDefaultFirst": "기본 서버를 먼저 제거하십시오", "higressMarket": "Higress MCP 설치로 이동하십시오", "npmRegistry": {"title": "NPM 소스 설정", "currentSource": "현재 소스", "cached": "캐시됨", "lastChecked": "마지막 확인", "refresh": "새로고침", "advanced": "고급", "advancedSettings": "고급 설정", "advancedSettingsDesc": "자동 감지 및 사용자 정의 소스 설정을 포함한 NPM 소스의 고급 옵션 구성", "autoDetect": "최적 소스 자동 감지", "autoDetectDesc": "시작 시 자동으로 가장 빠른 NPM 소스를 감지하여 사용", "customSource": "사용자 정의 소스", "customSourcePlaceholder": "사용자 정의 NPM 소스 주소 입력", "currentCustom": "현재 사용자 정의 소스", "justNow": "방금 전", "minutesAgo": "{minutes}분 전", "hoursAgo": "{hours}시간 전", "daysAgo": "{days}일 전", "refreshSuccess": "NPM 소스 새로고침 성공", "refreshSuccessDesc": "최적의 NPM 소스를 다시 감지하고 업데이트했습니다", "refreshFailed": "NPM 소스 새로고침 실패", "autoDetectUpdated": "자동 감지 설정이 업데이트되었습니다", "autoDetectEnabled": "최적 NPM 소스 자동 감지가 활성화되었습니다", "autoDetectDisabled": "자동 감지가 비활성화되어 기본 소스를 사용합니다", "updateFailed": "설정 업데이트 실패", "customSourceSet": "사용자 정의 소스가 설정되었습니다", "customSourceSetDesc": "사용자 정의 NPM 소스가 설정되었습니다: {registry}", "customSourceCleared": "사용자 정의 소스가 지워졌습니다", "customSourceClearedDesc": "사용자 정의 NPM 소스를 지우고 자동 감지를 사용합니다", "invalidUrl": "유효하지 않은 URL", "invalidUrlDesc": "유효한 HTTP 또는 HTTPS 주소를 입력하세요", "testing": "NPM 소스 테스트 중", "testingDesc": "소스 {registry}의 연결성을 테스트 중...", "testFailed": "NPM 소스 테스트 실패", "testFailedDesc": "{registry}에 연결할 수 없습니다. 오류: {error}. URL이 올바른지 또는 네트워크 연결을 확인하세요.", "redetectingOptimal": "최적 NPM 소스 재감지 중...", "redetectComplete": "재감지 완료", "redetectCompleteDesc": "현재 최적의 NPM 소스를 감지하고 설정했습니다", "redetectFailed": "재감지 실패", "redetectFailedDesc": "최적 소스를 재감지할 수 없어 기본 설정을 사용합니다"}, "technicalDetails": "기술 세부 사항", "httpServer": "HTTP 서버", "localProcess": "로컬 프로세스", "restartServer": "서버 재시작", "viewLogs": "로그 보기", "starting": "시작 중", "error": "오류"}, "about": {"title": "우리에 대해", "version": "버전", "checkUpdate": "업데이트 확인", "checking": "확인 중...", "latestVersion": "최신 버전"}, "display": {"fontSize": "텍스트 크기", "text-2xl": "매우 큽니다", "text-base": "기본", "text-lg": "큰", "text-sm": "작은", "text-xl": "매우 큽니다", "floatingButton": "플로팅 버튼", "floatingButtonDesc": "데스크톱에 플로팅 버튼을 표시하여 애플리케이션 창을 빠르게 활성화할 수 있습니다"}, "shortcuts": {"title": "단축키 설정", "pressKeys": "키를 누르세요", "pressEnterToSave": "Enter 키를 누르면 저장되고 Esc 키를 누르면 취소됩니다", "noModifierOnly": "수정 키만 단축키로 사용할 수 없습니다", "keyConflict": "단축키 충돌이 발생했습니다. 다른 조합을 선택해주세요", "clearShortcut": "단축키 지우기", "cleanHistory": "명확한 채팅 기록", "deleteConversation": "대화 삭제", "goSettings": "열기 설정", "hideWindow": "창을 숨기십시오", "quitApp": "프로그램을 종료하십시오", "zoomIn": "글꼴을 확대하십시오", "zoomOut": "글꼴을 줄입니다", "zoomReset": "글꼴을 재설정하십시오", "closeTab": "현재 탭 페이지를 닫습니다", "newTab": "새 탭을 만듭니다", "newWindow": "새 창을 엽니 다", "showHideWindow": "창을 표시/숨기기", "newConversation": "새로운 대화", "lastTab": "마지막 탭으로 전환하십시오", "previousTab": "이전 탭으로 전환하십시오", "specificTab": "지정된 탭 페이지 (1-8)로 전환", "nextTab": "다음 탭으로 전환하십시오"}, "rateLimit": {"title": "속도 제한", "description": "요청 시간 간격을 제어하여 API 제한 초과 방지", "intervalLimit": "요청 간격", "intervalUnit": "초", "intervalHelper": "두 요청 간의 최소 간격, 필요하지 않으면 속도 제한을 비활성화하세요", "lastRequestTime": "마지막 요청", "queueLength": "대기열 길이", "nextAllowedTime": "다음 허용된 요청", "never": "없음", "justNow": "방금 전", "secondsAgo": "초 전", "minutesAgo": "분 전", "immediately": "즉시", "secondsLater": "초 후", "confirmDisableTitle": "속도 제한 비활성화 확인", "confirmDisableMessage": "값은 0 이하일 수 없습니다. 속도 제한 기능을 비활성화하시겠습니까?", "confirmDisable": "제한 비활성화", "disabled": "속도 제한이 비활성화됨", "disabledDescription": "속도 제한 기능이 비활성화되었습니다"}}